#更新外加工附加费
DROP PROCEDURE `osclub`.`gx_wjg_fj`;
DELIMITER $$

CREATE PROCEDURE `osclub`.`gx_wjg_fj`()
BEGIN
    # 更新订单表的快递费
    update dd as d
        LEFT join (select `ddbh` AS `ddbh`, sum(`jg`) AS `kdf`
                   from `kdf`
                   group by `ddbh`) as a on d.ddbh = a.ddbh
    set d.kdf=a.kdf;

    # 更新订单里面的报销金额
    update dd as d
        LEFT join (select `ddbh` AS `ddbh`, sum(`bxje`) AS `bxje`
                   from `bx`
                   group by `ddbh`) as a on d.ddbh = a.ddbh
    set d.ywxgfy=a.bxje;

    # 更新纯材料成本
    UPDATE dd_kz AS v
        LEFT JOIN (select m.ddbh ddbh, round(sum(IF(`m`.`zwszm` = '喷砂膜', (ifnull(`m`.`sl`, 0) * ifnull(cc.`jg`, 0)), (ifnull(`m`.`cs`, 0) * ifnull(cc.`jg`, 0)))), 2) cclcb
                   from (select ddbh, zwszm, sum(sl) sl, sum(cs) cs from ddmx where (gc is null or gc like '%壹林%') group by ddbh, zwszm) m,
                        dd d,
                        cl c,
                        cl_cb cc
                   where m.ddbh = d.ddbh
                     and m.zwszm = c.mc
                     and c.id = cc.cl_id
                     and d.cq >= cc.kssj
                     and d.cq <= cc.jssj
                     and d.lx = '正常订单'
                     and cc.jg is not null
                   group by ddbh) AS a ON v.ddbh = a.ddbh
    SET v.cclcb = a.cclcb;

    # 更新纯材料成本+外厂报销部分
    UPDATE dd_kz AS v
        LEFT JOIN (select ddbh, round(sum(dcb), 2) clcb
                   from (select m.ddbh ddbh, round(sum(IF(`m`.`zwszm` = '喷砂膜', (ifnull(`m`.`sl`, 0) * ifnull(cc.`jg`, 0)), (ifnull(`m`.`cs`, 0) * ifnull(cc.`jg`, 0)))), 2) dcb
                         from (select ddbh, zwszm, sum(sl) sl, sum(cs) cs from ddmx where (gc is null or gc like '%壹林%') group by ddbh, zwszm) m,
                              dd d,
                              cl c,
                              cl_cb cc
                         where m.ddbh = d.ddbh
                           and m.zwszm = c.mc
                           and c.id = cc.cl_id
                           and d.cq >= cc.kssj
                           and d.cq <= cc.jssj
                           and d.lx = '正常订单'
                           and cc.jg is not null
                         group by ddbh
                         union all
                         select `ddbh` ddbh, sum(`bxje`) dcb
                         from `wcbx`
                         where ddbh is not null
                           and ddbh <> ''
                         group by ddbh) a
                   group by ddbh) AS g ON v.ddbh = g.ddbh
    SET v.clcb = g.clcb;

    # 更新外加工
    UPDATE dd_kz AS d
        LEFT JOIN (SELECT ddbh, ROUND(SUM(alje), 2) AS total_alje
                   FROM wjg
                   GROUP BY ddbh) AS a ON d.ddbh = a.ddbh
    SET d.alje = a.total_alje;

    # 更新大磨软膜造型材料附加
    truncate table wjg_fj;
    insert into wjg_fj (ddbh, dm_zcs, dm_zje, dm_cc, dm_fj) select ddbh, zcs, zje, cc, dmfj from vw_wjg_dm_fj;
    insert into wjg_fj (ddbh, rm_zcs, rm_zje, rm_cc, rm_fj) select ddbh, zcs, zje, cc, rmfj from vw_wjg_rm_fj;
    insert into wjg_fj (ddbh, zx_zcs, zx_zje, zx_cc, zx_fj) select ddbh, zcs, zje, cc, zxfj from vw_wjg_zx_fj;
    insert into wjg_fj (ddbh, cl_fj) select ddbh, clfj from vw_clfj;
    UPDATE dd_kz AS d
        LEFT JOIN (select ddbh,
                          round(sum(ifnull(dm_zcs, 0)), 3) dm_zcs,
                          round(sum(ifnull(dm_zje, 0)), 3) dm_zje,
                          round(sum(ifnull(dm_cc, 0)), 3)  dm_cc,
                          round(sum(ifnull(dm_fj, 0)), 3)  dm_fj,
                          round(sum(ifnull(rm_zcs, 0)), 3) rm_zcs,
                          round(sum(ifnull(rm_zje, 0)), 3) rm_zje,
                          round(sum(ifnull(rm_cc, 0)), 3)  rm_cc,
                          round(sum(ifnull(rm_fj, 0)), 3)  rm_fj,
                          round(sum(ifnull(zx_zcs, 0)), 3) zx_zcs,
                          round(sum(ifnull(zx_zje, 0)), 3) zx_zje,
                          round(sum(ifnull(zx_cc, 0)), 3)  zx_cc,
                          round(sum(ifnull(zx_fj, 0)), 3)  zx_fj,
                          round(sum(ifnull(cl_fj, 0)), 3)  cl_fj
                   from wjg_fj
                   group by ddbh) AS a ON d.ddbh = a.ddbh
    SET d.dm_zcs = a.dm_zcs,
        d.dm_zje = a.dm_zje,
        d.dm_cc  = a.dm_cc,
        d.dm_fj  = a.dm_fj,
        d.rm_zcs = a.rm_zcs,
        d.rm_zje = a.rm_zje,
        d.rm_cc  = a.rm_cc,
        d.rm_fj  = a.rm_fj,
        d.zx_zcs = a.zx_zcs,
        d.zx_zje = a.zx_zje,
        d.zx_cc  = a.zx_cc,
        d.zx_fj  = a.zx_fj,
        d.cl_fj  = a.cl_fj;

    # 更新其他字段
#     update dd_kz set hjje=ifnull(v.zje, 0)+ifnull(v.dduyf, 0)+ifnull(v.sjdj, 0),
#                     dkje
END;
$$

DELIMITER ;
