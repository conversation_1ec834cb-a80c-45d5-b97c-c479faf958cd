DROP PROCEDURE `gxbx_rb_gz_yxkh`;
DELIMITER $$

CREATE PROCEDURE `gxbx_rb_gz_yxkh`()
BEGIN
    -- 更新快递费，报销，外场报销，工厂报销的所属客户信息，其中选了订单编号没有选客户的条目
    update kdf k, dd d set k.khid=d.kh where k.ddbh = d.ddbh and k.khid is null and k.ddbh is not null;
    update bx k, dd d set k.khid=d.kh where k.ddbh = d.ddbh and k.khid is null and k.ddbh is not null;
    update wcbx k, dd d set k.khid=d.kh where k.ddbh = d.ddbh and k.khid is null and k.ddbh is not null;
    update gcbx k, dd d set k.khid=d.kh where k.ddbh = d.ddbh and k.khid is null and k.ddbh is not null;

    -- 更新工资的应发提成, 总提成, 总工资
    update gz z left join (select xm, substr(cq, 1, 7) yf, round(sum(tc), 2) tc from vw_tc group by xm, substr(cq, 1, 7)) t on z.xm = t.xm and z.yf = t.yf
        left join (select xm, substr(cq, 1, 7) yf, round(sum(tc), 2) tc from vw_yttc group by xm, substr(cq, 1, 7)) tt on z.xm = tt.xm and z.yf = tt.yf
    set z.yjtc=round(t.tc, 2), z.ztc=round(tt.tc, 2), zj=round(ifnull(gz, 0) + ifnull(ysb, 0) + ifnull(gjj, 0) + ifnull(jj, 0) + ifnull(xkfj, 0) + ifnull(jxjj, 0) + ifnull(ztc, 0) + ifnull(kq, 0), 2);


    -- 更新营销活动中的客户阅读数据
    update yxhd y, (select hdid, count(distinct yx) cs from yjhdmx where dz = '打开' group by hdid) m
    set y.dks=m.cs
    where y.id = m.hdid;
    update yxhd y, (select hdid, count(distinct yx) cs from yjhdmx where dz = '格式不对' group by hdid) m
    set y.fssbs=m.cs
    where y.id = m.hdid;
    update yxhd y, (select hdid, count(distinct yx) cs from yjhdmx where dz = '已合作客户' group by hdid) m
    set y.yhz=m.cs
    where y.id = m.hdid;
    update yxhd y, (select hdid, count(distinct yx) cs from yjhdmx where dz = '格式不对' group by hdid) m
    set y.fssbs=m.cs
    where y.id = m.hdid;

    -- 超过90天没有联系的客户，直接放回公海
    update yxkh y, (select g.*
                    from yxkh_gtjy g,
                         (select khid, max(gtsj) zdgtsj from yxkh_gtjy group by khid) z
                    where z.zdgtsj <= date_format(date_add(now(), interval - 90 day), '%Y-%m-%d')
                      and g.khid = z.khid
                      and g.gtsj = z.zdgtsj) z,account a
    set y.fg=null
    where y.id = z.khid
      and z.ywy = a.id;

    -- 更新日报中的新增数据
    update rb r, (select cjr, cjsj, count(*) xzcp from cp group by cjr, cjsj) c set r.xzcp=c.xzcp where c.cjr = r.username and c.cjsj = r.jlrq and r.jlrq=date_format(now(), '%Y-%m-%d');
    update rb r, (select lrr, lrsj, count(*) xzxj from xjd group by lrr, lrsj) c set r.xzxj=c.xzxj where c.lrr = r.username and c.lrsj = r.jlrq and r.jlrq=date_format(now(), '%Y-%m-%d') and r.username in (select username from account where bq <> 'WDS' or bq is null);
    update rb r, (select lrr, lrsj, count(*) xzxj from wds_xjd group by lrr, lrsj) c set r.xzxj=c.xzxj where c.lrr = r.username and c.lrsj = r.jlrq and r.jlrq=date_format(now(), '%Y-%m-%d') and r.username in (select username from account where bq = 'WDS');
    update rb r, (select cjr, substring(cjsj, 1, 10) sj, count(*) xzyxkh from yxkh group by cjr, substring(cjsj, 1, 10)) c set r.xzyxkh=c.xzyxkh where c.cjr = r.username and c.sj = r.jlrq and r.jlrq=date_format(now(), '%Y-%m-%d') and r.username in (select username from account where bq <> 'WDS' or bq is null);
    update rb r, (select g.ywy, g.gtsj, count(*) xzgt
                  from yxkh_gtjy g,
                       yxkh y
                  where g.khid = y.id and g.gtsj=date_format(now(), '%Y-%m-%d')
                  group by g.ywy, g.gtsj) g
    set r.xzgt=g.xzgt
    where g.ywy = r.username
      and g.gtsj = r.jlrq
      and r.jlrq=date_format(now(), '%Y-%m-%d')
      and r.username in (select username from account where bq <> 'WDS' or bq is null);
    update rb r, (select d.ywy, d.bdrq, count(*) xzbd
                  from yxkh_bd d,
                       yxkh y
                  where d.khid = y.id and d.bdrq=date_format(now(), '%Y-%m-%d')
                  group by d.ywy, d.bdrq) g
    set r.xzbd=g.xzbd
    where g.ywy = r.username
      and g.bdrq = r.jlrq
      and r.jlrq=date_format(now(), '%Y-%m-%d')
      and r.username in (select username from account where bq <> 'WDS' or bq is null);
    update rb r, (select cjr, substring(cjsj, 1, 10) sj, count(*) xzyxkh from wds_yxkh group by cjr, substring(cjsj, 1, 10)) c set r.xzyxkh=c.xzyxkh where c.cjr = r.username and c.sj = r.jlrq and r.jlrq=date_format(now(), '%Y-%m-%d') and r.username in (select username from account where bq = 'WDS');
    update rb r, (select g.ywy, g.gtsj, count(*) xzgt
                  from wds_yxkh_gtjy g,
                       wds_yxkh y
                  where g.khid = y.id and g.gtsj=date_format(now(), '%Y-%m-%d')
                  group by g.ywy, g.gtsj) g
    set r.xzgt=g.xzgt
    where g.ywy = r.username
      and g.gtsj = r.jlrq
      and r.jlrq=date_format(now(), '%Y-%m-%d')
      and r.username in (select username from account where bq = 'WDS');

    update rb r, (select a.userName,substring(d.czsj, 1, 10) czsj, count(*) xzddgj
                  from ddrz d,
                       account a
                  where d.czrid = a.id
                  and substring(d.czsj, 1, 10)=date_format(now(), '%Y-%m-%d')
                  group by a.userName, substring(d.czsj, 1, 10)) d
    set r.xzddgj=d.xzddgj
    where d.userName = r.username
      and r.jlrq=date_format(now(), '%Y-%m-%d')
      and d.czsj = r.jlrq;

    update rb r, (select a.userName, substring(t.wtsj, 1, 10) sj, count(*) xzxx
                  from pxwt t,
                       account a
                  where t.ygid = a.id
                  and substring(t.wtsj, 1, 10)=date_format(now(), '%Y-%m-%d')
                  group by a.userName, substring(t.wtsj, 1, 10)) d
    set r.xzxx=d.xzxx
    where d.userName = r.username
      and r.jlrq=date_format(now(), '%Y-%m-%d')
      and d.sj = r.jlrq;
    update rb set jlz=concat(substr(jlrq, 1, 4), '-', WEEK(date_add(jlrq, interval 6 day), 2)) where jlrq=date_format(now(), '%Y-%m-%d');
    update zb z, (select jlz, username, sum(ifnull(xzcp, 0)) xzcp,sum(ifnull(xzbd, 0)) xzbd, sum(ifnull(xzxj, 0)) xzxj, sum(ifnull(xzyxkh, 0)) xzyxkh, sum(ifnull(xzgt, 0)) xzgt, sum(ifnull(xzddgj, 0)) xzddgj, sum(ifnull(xzxx, 0)) xzxx from rb group by jlz, username) r
    set z.xzcp=r.xzcp,
        z.xzxj=r.xzxj,
        z.xzyxkh=r.xzyxkh,
        z.xzgt=r.xzgt,
        z.xzbd=r.xzbd,
        z.xzddgj=r.xzddgj,
        z.xzxx=r.xzxx
    where z.jlz = r.jlz
      and r.jlz=concat(date_format(now(), '%Y'), '-', WEEK(date_add(now(), interval 6 day), 2))
      and z.username = r.username;

    -- wds的客户，超过90天放回公海
    update wds_yxkh y, (select g.*
                        from wds_yxkh_gtjy g,
                             (select khid, max(gtsj) zdgtsj from wds_yxkh_gtjy group by khid) z
                        where z.zdgtsj <= date_format(date_add(now(), interval + 90 day), '%Y-%m-%d')
                          and g.khid = z.khid
                          and g.gtsj = z.zdgtsj) z,account a
    set y.fg=null
    where y.id = z.khid
      and z.ywy = a.id;

    -- 更新客户的合作时间
    update kh k, (select kh, min(cq) zxsj from dd where cq is not null and cq <> '' group by kh) d
    set k.hzsj=d.zxsj
    where k.id = d.kh
      and (k.hzsj is null or k.hzsj = '');
END;
$$

DELIMITER ;