List<Record> jsyxglList = Db.find("select mc name, mc value from jsyxgl order by mc");
setAttr("jsyxglJson", JsonKit.toJson(jsyxglList));
var multipleSelect = xmSelect.render({
    el: '#multipleSelect',
    tips: '请选择邮箱接受者?',
    filterable: true,
    data: $.parseJSON('#(jsyxglJson??[])'),
    initValue: $.parseJSON('#(yjmb.jsyx??"[]")'),
    on: function (data) {
        var arr = data.arr;
        const values = arr.map(item => item.value);
        const jsonString = JSON.stringify(values);
        console.log(jsonString);
        document.getElementById("yjmb.jsyx").value = jsonString;
    },
});


var layuiIndex;
var totalFiles = 0; // 记录总文件数
var uploadedFiles = 0; // 记录已上传文件数
layui.upload.render({
    elem: '#uploadFile'
    , url: '/my/dd/importDraws?id=#(dd.id??)'
    , multiple: true
    , accept: 'file'
    , timeout: 300000
    , exts: 'jpg|png|jpeg|bmp|JPG|PNG|JPEG|BMP'
    , before: function () {
        totalFiles++;
        layuiIndex = layer.load(0);
        layer.msg('文件上传中，请稍后...');
    }
    , done: function (res) {
        layer.msg('文件上传完成', {time: 2000, shade: 0.3});
        uploadedFiles++;
        if (uploadedFiles === totalFiles) {
            layer.close(layuiIndex);
            uploadedFiles = 0;
            totalFiles = 0;
        }
    }
});