# 管理员报表功能问题排查指南

## 问题1: JSON解析错误 "Unexpected end of JSON input"

### 可能原因
1. 后端返回空字符串而不是JSON
2. 后端返回HTML错误页面
3. 网络请求被中断
4. 权限验证失败返回重定向

### 排查步骤
1. **检查浏览器网络面板**
   - 打开F12开发者工具
   - 切换到Network标签
   - 重新发起请求
   - 查看`/my/marketresearch/getChartData`请求的响应内容

2. **检查服务器日志**
   - 查看控制台输出的调试信息
   - 确认SQL查询是否执行成功
   - 检查是否有异常堆栈信息

3. **验证权限**
   - 确认当前用户是否有管理员权限
   - 检查`AdminAuthService.me.hasRole()`方法是否正常工作

### 解决方案
1. **临时解决**：我已经在代码中暂时注释了严格的权限检查
2. **永久解决**：配置正确的用户角色权限

## 问题2: 图表弹窗空白

### 可能原因
1. ECharts库未正确加载
2. DOM元素未正确创建
3. 图表数据格式错误
4. CSS样式冲突

### 排查步骤
1. **检查ECharts加载**
   ```javascript
   console.log(typeof echarts); // 应该输出 "object"
   ```

2. **检查DOM元素**
   ```javascript
   console.log(document.getElementById('chartDiv')); // 应该不为null
   ```

3. **检查数据格式**
   - 在浏览器控制台查看`console.log`输出的数据
   - 确认数据结构是否正确

### 解决方案
1. **ECharts加载问题**：确保CDN链接可访问
2. **DOM问题**：我已经修改为动态创建DOM元素
3. **数据问题**：添加了数据验证和默认值处理

## 问题3: 数据库查询无结果

### 可能原因
1. 数据库中没有测试数据
2. 日期范围不正确
3. 表结构不匹配
4. SQL语法错误

### 排查步骤
1. **检查数据库数据**
   ```sql
   -- 检查各表的数据量
   SELECT 'yxkh_bd' as table_name, COUNT(*) as count FROM yxkh_bd
   UNION ALL
   SELECT 'market_research_answer', COUNT(*) FROM market_research_answer
   UNION ALL
   SELECT 'yxkh_voice_record', COUNT(*) FROM yxkh_voice_record;
   ```

2. **验证日期范围**
   ```sql
   -- 检查数据的日期范围
   SELECT MIN(bdrq) as min_date, MAX(bdrq) as max_date FROM yxkh_bd;
   SELECT MIN(survey_date), MAX(survey_date) FROM market_research_answer;
   SELECT MIN(create_time), MAX(create_time) FROM yxkh_voice_record;
   ```

3. **测试SQL查询**
   - 复制控制台输出的SQL语句
   - 在数据库客户端中直接执行
   - 检查是否有语法错误或结果

### 解决方案
1. **插入测试数据**：使用提供的`测试数据生成脚本.sql`
2. **调整日期范围**：选择有数据的日期范围进行测试
3. **修复SQL**：根据实际表结构调整查询语句

## 调试步骤

### 1. 启用调试模式
我已经在代码中添加了大量的`console.log`和`System.out.println`调试信息：

**前端调试**：
- 打开浏览器F12控制台
- 查看JavaScript控制台输出
- 检查网络请求和响应

**后端调试**：
- 查看服务器控制台输出
- 检查SQL执行日志
- 验证数据查询结果

### 2. 分步测试

**步骤1：测试基本页面加载**
1. 访问`/my/marketresearch/adminReport`
2. 确认页面正常显示
3. 检查筛选条件是否正常

**步骤2：测试数据查询**
1. 选择日期范围（建议选择有数据的范围）
2. 点击查询按钮
3. 检查表格是否显示数据

**步骤3：测试图表功能**
1. 在有数据的情况下点击"图表分析"
2. 检查弹窗是否正常打开
3. 查看控制台是否有错误信息

### 3. 常见解决方案

**权限问题**：
```java
// 临时解决：在MarketResearchService中注释权限检查
// boolean hasRole = AdminAuthService.me.hasRole(...);
// if (!hasRole) { return new ArrayList<>(); }
```

**数据问题**：
```sql
-- 执行测试数据生成脚本
source doc/测试数据生成脚本.sql
```

**前端问题**：
```javascript
// 检查ECharts是否加载
if (typeof echarts === 'undefined') {
    console.error('ECharts未加载');
}
```

## 快速修复清单

### 立即可以尝试的修复
1. ✅ **权限检查已简化** - 暂时注释了严格权限验证
2. ✅ **错误处理已加强** - 添加了详细的错误日志
3. ✅ **图表DOM已修复** - 改为动态创建DOM元素
4. ✅ **数据验证已添加** - 加入了数据格式验证

### 需要手动操作的修复
1. **插入测试数据** - 执行提供的SQL脚本
2. **检查ECharts CDN** - 确保网络可以访问CDN
3. **验证用户权限** - 确认当前用户角色配置

## 预期结果

修复后应该看到：
1. **页面正常加载**：筛选条件和统计概览正常显示
2. **数据表格有内容**：根据查询条件显示相应数据
3. **图表正常显示**：点击"图表分析"后弹窗显示图表
4. **控制台无错误**：浏览器和服务器控制台无错误信息

如果仍有问题，请提供：
1. 浏览器控制台的完整错误信息
2. 服务器控制台的日志输出
3. 网络请求的响应内容
4. 当前使用的日期范围和查询条件
