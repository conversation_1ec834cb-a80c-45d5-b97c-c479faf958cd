create table tc
(
    id int auto_increment,
    khid int null comment '客户ID',
    ywyid int null comment '业务员ID',
    xs varchar(32) null comment '提成系数',
    kssj varchar(32) null comment '开始时间',
    jssj varchar(32) null comment '结束时间',
    constraint tc_pk
        primary key (id)
)
    comment '提成管理';

alter table kh
    add kffs varchar(32) null comment '开发方式';

alter table bx
    add gj varchar(128) null comment '归属国家';

create table wjg
(
    id int auto_increment comment 'ID',
    ddbh varchar(128) null comment '订单编号',
    jglx varchar(64) null comment '加工类型',
    grmc varchar(64) null comment '工人名称',
    zwh varchar(128) null comment '注文号',
    sz varchar(128) null comment '石种',
    pm varchar(128) null comment '品名',
    sizea varchar(32) null comment 'Size A',
    sizeb varchar(32) null comment 'Size B',
    sizec varchar(32) null comment 'Size C',
    sl varchar(32) null comment '数量',
    cs varchar(32) null comment '才数',
    jgfs varchar(1024) null comment '加工方式',
    dj varchar(32) null comment '单价',
    je varchar(32) null comment '金额',
    bz varchar(32) null comment '备注',
    constraint wjg_pk
        primary key (id)
)
    comment '外加工';

