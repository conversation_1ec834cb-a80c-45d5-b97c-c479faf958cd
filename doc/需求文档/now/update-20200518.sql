create table zxd
(
    id int auto_increment comment 'ID',
    ddbh varchar(128) null comment '订单编号',
    tdh varchar(128) null comment '提单号',
    zxs int null comment '总箱数',
    zsl int null comment '总数量',
    zcs double null comment '总才数',
    zjz double null comment '总净重',
    zmz double null comment '总毛重',
    constraint zxd_pk
        primary key (id)
)
    comment '装箱单';

create table zxdmx
(
    id int auto_increment comment 'ID',
    xh varchar(128) null comment '箱号',
    gcbh varchar(128) null comment '工程编号',
    khsz varchar(128) null comment '客户石种',
    zwsz varchar(128) null comment '中文石种',
    ywpm varchar(128) null comment '英文品名',
    zwpm varchar(128) null comment '中文品名',
    sizea varchar(32) null comment 'Size A',
    sizeb varchar(32) null comment 'Size B',
    sizec varchar(32) null comment 'Size C',
    sl varchar(32) null comment '数量',
    cs varchar(32) null comment '才数',
    jz varchar(32) null comment '净重',
    mz varchar(32) null comment '毛重',
    xsizea varchar(32) null comment '箱子长',
    xsizeb varchar(32) null comment '箱子宽',
    xsizec varchar(32) null comment '箱子高',
    constraint zxdmx_pk
        primary key (id)
)
    comment '装箱单明细';

create table dzhy
(
    id int auto_increment comment 'ID',
    ddbh varchar(128) null comment '麦头',
    chsj varchar(32) null comment '出货时间',
    jyms varchar(32) null comment '交易模式',
    hl varchar(32) null comment '货量',
    hglx varchar(128) null comment '货柜类型',
    zl varchar(32) null comment '重量',
    xs varchar(32) null comment '箱数',
    tdh varchar(128) null comment '提单号',
    cmhc varchar(128) null comment '船名航次',
    gh varchar(128) null comment '柜号',
    fqh varchar(128) null comment '封铅号',
    jjcsj varchar(32) null comment '截进场时间',
    jjlxr varchar(128) null comment '紧急联系人',
    cfg varchar(128) null comment '出发港',
    cfrq varchar(32) null comment '出发日期',
    mdg varchar(128) null comment '目的港',
    dgsj varchar(32) null comment '到港时间',
    hd varchar(128) null comment '货代',
    zxd varchar(128) null comment '装箱单',
    zlxz varchar(128) null comment '资料下载',
    constraint dzhy_pk
        primary key (id)
)
    comment '单证货运';

alter table kh
    add tdtt varchar(256) null comment '提单抬头';

alter table kh
    add bg varchar(128) null comment '报关';

alter table kh
    add gsz varchar(32) null comment '公/私账';

alter table kh
    add hq varchar(32) null comment '航期';

alter table kh
    add jjdz varchar(256) null comment ' 寄件地址';

alter table kh
    add sbmjxs varchar(32) null comment '申报美金系数';

alter table kh
    add tdshrtt varchar(128) null comment '提单收货人抬头';

alter table kh
    add tdlxr varchar(128) null comment '提单联系人';

alter table kh
    add tdsh varchar(128) null comment '提单税号';

create unique index kh_jc_uindex
    on kh (jc);

alter table kh modify lxr varchar(256) null comment '联系人';

alter table kh modify dh varchar(256) null comment '电话';

alter table kh modify qgzl varchar(256) null comment '清关资料';

alter table kh modify bz varchar(1024) null comment '备注';

alter table kh modify tdtzr varchar(256) null comment '提单通知人';



# =CONCATENATE("update kh set gb='", C3,"', mdg='",D3, "', mytk='", E3, "', fkfs='",F3, "', tdtt='", G3, "', bg='", H3, "', gsz='", I3, "', hq='", J3, "', hb='", K3, "', hd='", L3,  "', mc='", M3, "', dz='", N3, "', jjdz='", O3, "', lxr='", P3, "', dh='", Q3, "', cz='", R3, "', sh='", S3, "', tdtzr='", T3, "', qgzl='", U3, "', sbmjxs='", V3, "', tdshrtt='", W3, "', tdlxr='", X3, "', tdsh='", Y3, "' where jc='", A2, "';")
# 简称 唛头 国别 目的港 贸易条款 付款方式 提单抬头 报关 公/私账 航期 币种 货代 公司 地址 寄件地址 负责人 电话 传真 税号 提单通知人（Notify Party） 清关资料 申报美金系数（净重*系数） 提单收货人抬头 提单联系人 提单税号

# insert into kh (jc, mc, ckzq, lxr, dh, yx, dz, ckf, gwdjs, mdg, xxdz, hd, fkfs, qgzl, bz, hzsj, ywy, mytk, cz, sh, tdtzr, hb, gb, kffs, tdtt, bg, gsz, hq, jjdz, sbmjxs, tdshrtt, tdlxr, tdsh) values ()


alter table zxdmx
    add ddbh varchar(128) null comment '订单编号';


alter table zxdmx
    add khddh varchar(128) null comment '客户订单号';

alter table zxdmx
    add jgfs varchar(1024) null comment '加工方式';

alter table kh
    add md int null comment '密度';

alter table dzhy
    add ts varchar(32) null comment '套数';


alter table dzhy
    add gbzl varchar(32) null comment '过磅重量';

alter table dzhy
    add wj varchar(2048) null comment '文件';

alter table dzhy add gz varchar(32) null comment '柜重';

alter table dzhy
    add bg varchar(128) null comment '报关';

create table sbhlxs
(
    id int auto_increment,
    hb varchar(32) null comment '货币',
    xs varchar(32) null comment '系数',
    constraint sbhlxs_pk
        primary key (id)
)
    comment '申报汇率系数';

alter table dzhy
    add rct varchar(1024) default 'default.png;' null comment '入仓图';

alter table zxdmx
    add ddid varchar(32) null comment '订单ID';

alter table dzhy
    add sl varchar(32) null comment '总数量';

alter table dzhy
    add cs varchar(32) null comment '总才数';

alter table zxdmx
    add bzbz varchar(1024) null comment '包装备注';

alter table dzhy
    add tdwj varchar(1024) null comment '提单文件';

alter table kh
    add qgjexs varchar(32) null comment '清关金额系数';

alter table kh
    add dqxh varchar(128) null comment '当前箱号';
alter table dzhy alter column rct drop default;

alter table dzhy
    add lfs varchar(32) null comment '立方数';

alter table dzhy
    add gblfs varchar(32) null comment '过磅立方数';

alter table dzhy
    add hhsj varchar(32) null comment '货好时间';


alter table dzhy
    add hwqssj varchar(32) null comment '货物签收时间';

alter table dzhy
    add cdzwj varchar(1024) null comment '产地证';

alter table zxdmx
    add xxh varchar(32) null comment '小箱号';
alter table dd
    add lx varchar(32) null comment '订单类型';

alter table dd alter column lx set default '正常订单';

alter table kh
    add nggdh varchar(32) null comment 'NGG电话';

alter table dzhy
    add shr varchar(1024) null comment '收货人';

alter table dzhy
    add dz varchar(1024) null comment '地址';

alter table dzhy
    add lxr varchar(1024) null comment '联系人';

alter table dzhy
    add dh varchar(128) null comment '电话';

alter table ddmx modify zwjgff varchar(1024) null comment '中文加工方法';

alter table ddmx modify ywjgff varchar(1024) null comment '英文加工方法';

alter table ddmx modify khjg varchar(1024) null comment '客户加工';

alter table hd
    add gzskr varchar(128) null comment '公帐收款人';

alter table hd
    add gzkhh varchar(128) null comment '公帐开户行';

alter table hd
    add gzkhzh varchar(128) null comment '公帐开户账号';

alter table hd
    add szskr varchar(128) null comment '私账收款人';

alter table hd
    add szkhh varchar(128) null comment '私账开户行';

alter table hd
    add szkhzh varchar(128) null comment '私账开户账号';

alter table hd
    modify gzskr varchar(128) null comment '公账收款人';

alter table hd
    modify gzkhh varchar(128) null comment '公账开户行';

alter table hd
    modify gzkhzh varchar(128) null comment '公账开户账号';

create table hdlxr
(
    id int auto_increment,
    hdid int null comment '货代ID',
    mc varchar(128) null comment '名称',
    gw varchar(128) null comment '岗位',
    zj varchar(32) null comment '座机',
    sj varchar(32) null comment '手机',
    yx varchar(128) null comment '邮箱',
    qq varchar(128) null comment 'QQ',
    constraint hdlxr_pk
        primary key (id)
)
    comment '货代联系人';

alter table cp
    add px varchar(32) null comment '排序';

alter table cp alter column px set default 999999999;

alter table dzhy
    add pzwj varchar(128) null comment '排载文件';

alter table dzhy
	add tdqrwj varchar(128) null comment '托单文件';

alter table kh
    add gsmc varchar(128) null comment '公司名称';

alter table zxdmx
    add dw varchar(32) null comment '单位';

alter table zxdmx
    add bsizea varchar(32) null comment '订单sizea';

alter table zxdmx
    add bsizeb varchar(32) null comment '订单sizeb';

alter table zxdmx
    add bsizec varchar(32) null comment '订单sizec';
alter table zxdmx
    add ywjgff varchar(2048) null comment '英文加工方法';

alter table kh
    add dylxr varchar(128) null comment '第一联系人';

alter table kh
    add delxr varchar(128) null comment '第二联系人';

alter table kh
    add dslxr varchar(128) null comment '第三联系人';

alter table kh
    add khdylxr varchar(128) null comment '客户第一联系人';

alter table kh
    add khdelxr varchar(128) null comment '客户第二联系人';

alter table kh
    add khdslxr varchar(128) null comment '客户第三联系人';

alter table kh
    add bz1 varchar(2048) null comment '备注1';

alter table kh
    add bz2 varchar(2048) null comment '备注2';

alter table kh
    add bz3 varchar(2048) null comment '备注3';

alter table kh
    add wj varchar(2048) null comment '文件';

alter table wjg modify je varchar(32) null comment '壹林成本';

alter table wjg
    add alje varchar(32) null comment '澳林成本';

alter table dd
    add hl varchar(32) default '整柜' null comment '货量';

alter table dd
    add gxsl varchar(32) default '1*20gp' null comment '柜型数量';

alter table dzhy
    add tgzt varchar(32) null comment '提柜状态';

alter table dzhy
    add dfwj varchar(128) null comment '电放文件';

alter table wjg
    add zs varchar(32) null comment '字数';

alter table wjg
    add lx varchar(32) null comment '类型';

create table dzhydp
(
    id int auto_increment comment 'ID',
    ddbh varchar(128) null comment '订单编号',
    lx varchar(32) null comment '类型',
    constraint dzhydp_pk
        primary key (id)
);

alter table dzhydp
    add zddbh varchar(128) null comment '主订单编号' after id;

alter table dzhydp modify ddbh varchar(1024) null comment '订单编号';


create table gz
(
    id int auto_increment,
    xm varchar(32) null comment '姓名',
    yf varchar(32) null comment '月份',
    gz varchar(32) null comment '工资',
    ysb varchar(32) null comment '医社保',
    gjj varchar(32) null comment '公积金',
    zj varchar(32) null comment '总计',
    gssc varchar(128) null comment '归属市场',
    constraint gz_pk
        primary key (id)
);

create table kdf
(
    id int auto_increment,
    rq varchar(32) null comment '日期',
    ddbh varchar(32) null comment '订单编号',
    hw varchar(128) null comment '货物',
    dh varchar(128) null comment '单号',
    kdgs varchar(128) null comment '快递公司',
    zl varchar(32) null comment '重量',
    jg varchar(32) null comment '价格',
    sx varchar(128) null comment '时效',
    bz varchar(1024) null comment '备注',
    txr varchar(128) null comment '填写人',
    txsj varchar(32) null comment '填写时间',
    constraint kdf_pk
        primary key (id)
);

alter table dd
    add mdg varchar(32) null comment '目的港';

alter table dd
    add po varchar(128) null comment 'PO';

alter table dzhy
    add po varchar(128) null comment 'PO';


alter table kh
    add khdylxrm varchar(128) null comment '客户第一联系人名字';

alter table kh
    add khdelxrm varchar(128) null comment '客户第二联系人名';

alter table kh
    add khdslxrm varchar(128) null comment '客户第三联系人名';

alter table kh
    add khdylxrfd varchar(128) null comment '客户第一联系人分店';

alter table kh
    add khdelxrfd varchar(128) null comment '客户第二联系人分店';

alter table kh
    add khdslxrfd varchar(128) null comment '客户第三联系人分店';


alter table kh
    add khd4lxrm varchar(128) null comment '客户第4联系人名';

alter table kh
    add khd5lxrm varchar(128) null comment '客户第5联系人名';

alter table kh
    add khd6lxrm varchar(128) null comment '客户第6联系人名';

alter table kh
    add khd4lxrfd varchar(128) null comment '客户第4联系人分店';

alter table kh
    add khd5lxrfd varchar(128) null comment '客户第5联系人分店';

alter table kh
    add khd6lxrfd varchar(128) null comment '客户第6联系人分店';

alter table kh
    add khd4lxr varchar(128) null comment '客户第4联系人';

alter table kh
    add khd5lxr varchar(128) null comment '客户第5联系人';

alter table kh
    add khd6lxr varchar(128) null comment '客户第6联系人';

alter table kh
    add khd7lxrm varchar(128) null comment '客户第7联系人名';
alter table kh
    add khd7lxrfd varchar(128) null comment '客户第7联系人分店';
alter table kh
    add khd7lxr varchar(128) null comment '客户第7联系人';

alter table kh
    add khd8lxrm varchar(128) null comment '客户第8联系人名';
alter table kh
    add khd8lxrfd varchar(128) null comment '客户第8联系人分店';
alter table kh
    add khd8lxr varchar(128) null comment '客户第8联系人';
alter table kh add yy varchar(32) null comment '语言';


alter table dzhy
    add zdwj varchar(128) null comment '账单文件';

alter table dzhy
    add sdwj varchar(128) null comment '水单文件';

create table pjmx
(
    id int auto_increment comment 'ID',
    xh varchar(32) null comment '箱号',
    zwpm varchar(128) null comment '中文品名',
    ywpm varchar(128) null comment '英文品名',
    sl varchar(32) null comment '数量',
    jz varchar(32) null comment '净重',
    mz varchar(32) null comment '毛重',
    xsizea varchar(32) null comment '箱子长',
    xsizeb varchar(32) null comment '箱子宽',
    xsizec varchar(32) null comment '箱子高',
    hs varchar(32) null comment 'HS',
    sbys varchar(32) null comment '申报要素',
    constraint pjmx_pk
        primary key (id)
)
    comment '配件明细';


alter table dzhy
    add pjwj varchar(128) null comment '配件文件';


alter table pjmx
    add ddbh varchar(128) null comment '订单编号' after id;


create table yjmb
(
    id int auto_increment,
    mbbt varchar(128) null comment '模版标题',
    yt varchar(128) null comment '用途',
    yjbt varchar(1024) null comment '邮件标题',
    yjnr text null comment '邮件内容',
    cjr varchar(64) null comment '创建人',
    constraint yjmb_pk
        primary key (id)
);
alter table yjmb comment '邮件模版';

CREATE TABLE `yxpz` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `yx` varchar(128) DEFAULT NULL COMMENT '邮箱',
    `lx` varchar(128) DEFAULT NULL COMMENT '类型',
    `fwq` varchar(128) DEFAULT NULL COMMENT '服务器',
    `dk` varchar(32) DEFAULT NULL COMMENT '端口',
    `mm` varchar(64) DEFAULT NULL COMMENT '密码',
    PRIMARY KEY (`id`)
);
alter table yxpz comment '邮箱配置';

alter table pjmx modify sbys varchar(2048) null comment '申报要素';

alter table dzhy
    add tdqr varchar(128) null comment '提单确认';

alter table dzhy
    add cdqr varchar(128) null comment '产地确认';

alter table dzhy
    add jtdsj varchar(32) null comment '截提单时间';

alter table dzhy
    add isfwj varchar(128) null comment 'ISF文件';

alter table dzhy
    add dfzt varchar(128) null comment '电放状态';

alter table yjmb
    add zt varchar(32) null comment '状态';

alter table tip
    add zt varchar(32) null comment '状态';


alter table dzhy alter column rct drop default;


create index ddmx_ddbh_index
    on ddmx (ddbh);

create index ddmx_zwszm_index
    on ddmx (zwszm);


create index dd_kh_index
    on dd (kh);

create index dd_lx_index
    on dd (lx);

create index cl_mc_index
    on cl(mc);

create table wcbx
(
    id       int auto_increment comment 'ID'
        primary key,
    username varchar(32)   null comment '记录人',
    bxsj     varchar(32)   null comment '报销时间',
    bxje     varchar(32)   null comment '报销金额',
    bxnr     varchar(1024) null comment '报销内容',
    wj       varchar(1024) null comment '文件路径',
    bxzt     varchar(16)   null comment '报销状态',
    khid     int           null comment '客户ID',
    ddbh     varchar(128)  null comment '订单编号',
    pj       varchar(32)   null comment '票据',
    txsj     varchar(32)   null comment '填写时间',
    bxr      varchar(32)   null comment '报销人',
    fylb     varchar(128) default '货款' null comment '费用类别',
    skr     varchar(128)  null comment '收款人',
    gj       varchar(128)  null comment '归属国家'
)
    comment '外厂报销';
alter table wcbx
    add khh varchar(256) null comment '开户行';

alter table wcbx
    add khzh varchar(256) null comment '开户账号';



create table wcskr
(
    id       int auto_increment comment 'ID'
        primary key,
    skr varchar(64)   null comment '收款人',
    khh     varchar(256)   null comment '开户行',
    khzh     varchar(128)   null comment '开户账号'
)
    comment '外厂收款人';

alter table ddmx
    add gc varchar(138) null comment '工厂名字';

create table gcbx
(
    id       int auto_increment comment 'ID'
        primary key,
    username varchar(32)               null comment '记录人',
    bxsj     varchar(32)               null comment '报销时间',
    bxje     varchar(32)               null comment '报销金额',
    bxnr     varchar(1024)             null comment '报销内容',
    wj       varchar(1024)             null comment '文件路径',
    bxzt     varchar(16)               null comment '报销状态',
    khid     int                       null comment '客户ID',
    ddbh     varchar(128)              null comment '订单编号',
    pj       varchar(32)               null comment '票据',
    txsj     varchar(32)               null comment '填写时间',
    bxr      varchar(32)               null comment '报销人',
    fylb     varchar(128) default '货款' null comment '费用类别',
    skr      varchar(128)              null comment '收款人',
    gj       varchar(128)              null comment '归属国家',
    khh      varchar(256)              null comment '开户行',
    khzh     varchar(256)              null comment '开户账号'
)
    comment '工厂报销';

alter table account
    add yxmm varchar(64) null comment '邮箱密码';

alter table sbhlxs
    add kssj varchar(32) null comment '开始时间';

alter table sbhlxs
    add jssj varchar(32) null comment '结束时间';

alter table gz
    add jj varchar(32) null comment '奖金';

create index zxdmx_ddid_index
    on zxdmx (ddid);

create index zxdmx_ddbh_index
    on zxdmx (ddbh);

    alter table account
	add xm varchar(32) null comment '姓名';


