create table hy
(
    id int auto_increment
        primary key,
    sj varchar(32)   null comment '与会时间',
    bt varchar(256)  not null comment '标题',
    zc varchar(128)  null comment '主持人',
    ry varchar(1024) null comment '参会人员',
    yc text          null comment '会议议程',
    jg text          null comment '会议结果/行动',
    wj varchar(2048) null comment '附件'
)
    comment '会议';
alter table hy
    add ygsj varchar(128) null comment '预估时间';

alter table hy
    add cdr varchar(1024) null comment '迟到人';

alter table hy
    add sqr varchar(128) null comment '申请人';

alter table hy
    add sqsj varchar(32) null comment '申请时间';


