package com.theolympiastone.club._admin.account;

import com.google.gson.Gson;
import com.jfinal.aop.Before;
import com.jfinal.aop.Inject;
import com.jfinal.kit.JsonKit;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import com.jfinal.upload.ExceededSizeException;
import com.jfinal.upload.UploadFile;
import com.theolympiastone.club._admin.role.RoleAdminService;
import com.theolympiastone.club.common.GsonKit;
import com.theolympiastone.club.common.account.AccountService;
import com.theolympiastone.club.common.controller.BaseController;
import com.theolympiastone.club.common.kit.IpKit;
import com.theolympiastone.club.common.kit.XmSelect;
import com.theolympiastone.club.common.model.Account;
import com.theolympiastone.club.common.model.Role;
import com.theolympiastone.club.my.setting.MySettingService;
import com.theolympiastone.club.reg.RegService;
import net.m3u8.utils.StringUtils;
import org.assertj.core.util.Lists;

import java.util.List;

import static com.theolympiastone.club.common.CacheKit.KeyEnum.YH;
import static com.theolympiastone.club.common.CacheKit.refreshCacheTable;

/**
 * 账户管理控制器
 */
public class AccountAdminController extends BaseController {

    @Inject
    AccountAdminService srv;
    @Inject
    MySettingService mySettingSrv;
    @Inject
    RegService regSrv;

    public void index() {
        keepPara();
        Page<Account> accountPage = srv.paginate(getParaToInt("p", 1));
        setAttr("accountPage", accountPage);
        render("index.html");
    }

    private String getGbSelectJson() {
        List<Record> gbList = Db.find("select distinct gb from yxkh order by gb");
        XmSelect[] xmSelects = new XmSelect[gbList.size()];
        for (int i = 0; i < gbList.size(); i++) {
            xmSelects[i] = new XmSelect(gbList.get(i).getStr("gb"));
        }
        return new Gson().toJson(xmSelects);
    }

    public void add() {
        keepPara();
        setAttr("gbSelectJson", getGbSelectJson());
        render("add.html");
    }

    public void password() {
        keepPara();
        Account account = srv.findById(getParaToInt("id"));
        setAttr("account", account);
        render("password.html");
    }

    public void edit() {
        keepPara();    // 保持住分页的页号，便于在 ajax 提交后跳转到当前数据所在的页
        Account account = srv.findById(getParaToInt("id"));
        setAttr("account", account);
        setAttr("gbSelectJson", getGbSelectJson());
        render("edit.html");
    }

    public void remark() {
        keepPara();
        Account account = srv.findById(getParaToInt("id"));
        setAttr("account", account);
        render("remark.html");
    }

    public void updateRemark() {
        keepPara();
        String remark = getPara("remark", "");
        Account account = srv.findById(getParaToInt("id"));
        account.setRemark(remark);
        account.update();
        renderJson(Ret.ok().set("id", account.getId()));
    }

    /**
     * 提交修改
     */
    @Before(AccountUpdateValidator.class)
    public void update() {
        Account account = getBean(Account.class);
        String password = account.getPassword();
        if (!StringUtils.isEmpty(password) && password.length() < 30) {
            account.setPass(password);
        }
        Ret ret = srv.update(account);
        refreshCacheTable(YH);
        renderJson(ret.set("id", account.getId()));
    }

    public void save() {
        Account account = getBean(Account.class);
        account.setPass(account.getPassword());
        String ip = IpKit.getRealIp(getRequest());
        Ret ret = regSrv.reg(account, ip);
        if (ret.isOk()) {
            ret.set("regEmail", getPara("userName"));
        }
        refreshCacheTable(YH);
        if (ret.isOk()) {
            Account saveAccount = ret.getAs("account");
            renderJson(ret.set("id", saveAccount.getId()));
        } else {
            renderJson(ret);
        }
    }

    public void resetPassword() {
        Account account = getBean(Account.class);
        Ret ret = srv.resetPassword(account);
        renderJson(ret.set("id", account.getId()));
    }

    /**
     * 账户锁定
     */
    public void lock() {
        Integer id = getParaToInt("id");
        String type = get("type");
        Ret ret = srv.lock(getLoginAccountId(), id, type);
        renderJson(ret.set("id", id));
    }

    /**
     * 账户解锁
     */
    public void unlock() {
        Integer id = getParaToInt("id");
        String type = get("type");
        Ret ret = srv.unlock(id, type);
        renderJson(ret.set("id", id));
    }

    public void changeStatus() {
        Integer id = getParaToInt("id");
        String type = get("type", "");
        if(id==null){
            renderJson(Ret.fail("没有指定的用户!"));
            return;
        }
        Account account = srv.findById(id);
        Ret ret = null;
        if("status".equalsIgnoreCase(type)) {
            Integer status = account.getStatus();
            if(status==1){
                ret = srv.lock(getLoginAccountId(), id, type);
            }else{
                ret = srv.unlock(id, type);
            }
        }else if("zwh".equalsIgnoreCase(type)){
            Integer zwh = account.getZwh();
            account.setZwh(zwh==1?0:1);
            account.update();
            ret = Ret.ok("更新成功");
        }
        if(ret == null){
            ret = Ret.fail("更新失败!");
        }
        ret.set("id", id);
        renderJson(ret);
    }

    /**
     * 账户激活
     */
    public void unactive() {
        Ret ret = srv.unactive(getLoginAccountId(), getParaToInt("id"));
        renderJson(ret);
    }

    /**
     * 账户激活
     */
    public void active() {
        Ret ret = srv.active(getParaToInt("id"));
        renderJson(ret);
    }


    /**
     * 分配角色
     */
    public void assignRoles() {
        Account account = srv.findById(getParaToInt("id"));
        List<Role> roleList = RoleAdminService.me.getAllRoles();
        srv.markAssignedRoles(account, roleList);

        setAttr("account", account);
        setAttr("roleList", roleList);
        render("assign_roles.html");
    }

    /**
     * 添加角色
     */
    public void addRole() {
        Ret ret = srv.addRole(getParaToInt("accountId"), getParaToInt("roleId"));
        renderJson(ret);
    }

    /**
     * 删除角色
     */
    public void deleteRole() {
        Ret ret = srv.deleteRole(getParaToInt("accountId"), getParaToInt("roleId"));
        renderJson(ret);
    }

    public void avatar() {
        keepPara();    // 保持住分页的页号，便于在 ajax 提交后跳转到当前数据所在的页
        Account account = srv.findById(getParaToInt("accountId"));
        setAttr("account", account);
        render("avatar.html");
    }

    /**
     * 上传用户图片，为裁切头像做准备
     */
    public void uploadAvatar() {
        UploadFile uf;
        try {
            uf = getFile("avatar", mySettingSrv.getAvatarTempDir(), mySettingSrv.getAvatarMaxSize());
            if (uf == null) {
                renderJson(Ret.fail("请先选择上传文件"));
                return;
            }
        } catch (Exception e) {
            if (e instanceof ExceededSizeException) {
                renderJson(Ret.fail("文件大小超出范围"));
            } else {
                renderJson(Ret.fail(e.getMessage()));
            }
            return;
        }

        // 注意这里可以更换任意用户的头像，所以并非 getLoginAccountId()
        int accountId = getParaToInt("accountId");
        Ret ret = mySettingSrv.uploadAvatar(accountId, uf);
        if (ret.isOk()) {   // 上传成功则将文件 url 径暂存起来，供下个环节进行裁切
            setSessionAttr("avatarUrl", ret.get("avatarUrl"));
        }
        renderJson(ret.set("id", accountId));
    }

    /**
     * 保存 jcrop 裁切区域为用户头像
     */
    public void saveAvatar() {
        // 注意这里可以更换任意用户的头像，所以并非 getLoginAccountId()
        int accountId = getParaToInt("accountId");
        Account account = AccountService.me.getById(accountId);

        String avatarUrl = getSessionAttr("avatarUrl");
        int x = getParaToInt("x");
        int y = getParaToInt("y");
        int width = getParaToInt("width");
        int height = getParaToInt("height");
        Ret ret = mySettingSrv.saveAvatar(account, avatarUrl, x, y, width, height);
        renderJson(ret.set("id", accountId));
    }

    public void guanli() {
        Integer accountId = getInt("id");
        Account account = srv.findById(accountId);
        List<Record> ywyFollowJson = Db.find("select username_follow name, username_follow value from account_guanli where username in (select username from account where id=?);", accountId);
        List<Record> ywyJson = Db.find("select username name, username value from account order by username");
        setAttr("ywyFollowJson", JsonKit.toJson(ywyFollowJson));
        setAttr("ywyJson", JsonKit.toJson(ywyJson));
        setAttr("id", accountId);
        setAttr("account", account);
        render("guanli.html");
    }

    public void modifyGuanli() {
        Integer accountId = getInt("id");
        String ywyFollowJson = get("ywyFollowJson");
        Account account = srv.findById(accountId);
        if (account == null) {
            renderJson(Ret.fail("找不到对应的账号!").set("id", accountId));
            return;
        }
        List<String> usernameFollowList = GsonKit.fromJson(ywyFollowJson);
        String userName = account.getUserName();
        Db.delete("delete from account_guanli where username=?", userName);
        List<String> sqlList = Lists.newArrayList();
        for (String usernameFollow : usernameFollowList) {
            sqlList.add("insert into account_guanli (username, username_follow) values ('" + userName + "', '" + usernameFollow + "');");
        }
        Db.batch(sqlList, sqlList.size());
        renderJson(Ret.ok("修改成功!").set("id", accountId));
    }
}
