package com.theolympiastone.club._admin.clbm;

import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import com.theolympiastone.club.common.model.Clbm;

public class ClbmAdminService {
    public static final ClbmAdminService me = new ClbmAdminService();
    private final Clbm dao = new Clbm().dao();

    public Page<Record> paginate(int pageNum) {
        return Db.paginate(pageNum, 100, "select b.*, a.mc clmc ", " from clbm b, cl a where b.clid=a.id order by b.id asc");
    }

    public Ret save(Clbm clbm) {
        clbm.save();
        return Ret.ok("msg", "创建成功");
    }

    public Clbm findById(Integer id) {
        return dao.findById(id);
    }

    public Ret update(Clbm clbm) {
        clbm.update();
        return Ret.ok("msg", "修改成功");
    }

    public Ret delete(Integer id) {
        dao.deleteById(id);
        return Ret.ok("msg", "删除成功");
    }
}
