package com.theolympiastone.club._admin.ddlc;

import com.jfinal.kit.Ret;
import com.theolympiastone.club.common.model.Ddlc;

import java.util.List;

public class DdlcAdminService {
    public static final DdlcAdminService me = new DdlcAdminService();
    private final Ddlc dao = new Ddlc().dao();

    public List<Ddlc> find(String lcmc) {
        return dao.find("select * from ddlc where lcmc like '%" + lcmc + "%' order by px");
    }

    public Ret save(Ddlc ddlc) {
        ddlc.save();
        return Ret.ok("msg", "创建成功");
    }

    public Ddlc findById(Integer id) {
        return dao.findById(id);
    }

    public Ret update(Ddlc ddlc) {
        ddlc.update();
        return Ret.ok("msg", "修改成功");
    }

    public Ret delete(Integer id) {
        dao.deleteById(id);
        return Ret.ok("msg", "删除成功");
    }
}
