package com.theolympiastone.club._admin.dzgzdd;

import com.jfinal.aop.Before;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.theolympiastone.club.common.controller.BaseController;
import com.theolympiastone.club.common.model.Dzgzdd;

public class DzgzddAdminController extends BaseController {
    @Inject
    DzgzddAdminService srv;

    public void index() {
        keepPara();
        Page<Dzgzdd> dzgzddPage = srv.paginate(getParaToInt("p", 1));
        setAttr("page", dzgzddPage);
        render("index.html");
    }

    public void add() {
        String ddbh = get("ddbh");
        set("ddbh", ddbh);
        render("add_edit.html");
    }

    @Before(DzgzddAdminValidator.class)
    public void save() {
        Dzgzdd dzgzdd = getBean(Dzgzdd.class, true);
        Integer id = Db.queryInt("select id from dzgzdd where ddbh=?", dzgzdd.getDdbh());
        if (id != null) {
            srv.update(dzgzdd);
        } else {
            dzgzdd.save();
        }
        renderJson(Ret.ok().set("id", dzgzdd.getId()));
    }

    public void edit() {
        keepPara();
        String ddbh = get("ddbh");
        set("ddbh", ddbh);
        Dzgzdd dzgzdd = srv.findById(getParaToInt("id"));
        setAttr("dzgzdd", dzgzdd);
        render("add_edit.html");
    }

    @Before(DzgzddAdminValidator.class)
    public void update() {
        Dzgzdd dzgzdd = getBean(Dzgzdd.class, true);
        Ret ret = srv.update(dzgzdd);
        renderJson(ret.set("id", dzgzdd.getId()));
    }

    public void delete() {
        srv.delete(getParaToInt("id"));
        renderJson(Ret.ok().set("msg", "删除成功!"));
    }
}
