package com.theolympiastone.club._admin.hdlxr;

import com.jfinal.core.Controller;
import com.jfinal.validate.Validator;

public class HdlxrAdminValidator extends Validator {

    @Override
    protected void validate(Controller c) {
        setShortCircuit(true);
//        validateRequiredString("hdlxr.id", "msg", "id不能为空");
        //$[isnotnull]
    }

    @Override
    protected void handleError(Controller c) {
        c.setAttr("state", "fail");
        c.renderJson();
    }
}
