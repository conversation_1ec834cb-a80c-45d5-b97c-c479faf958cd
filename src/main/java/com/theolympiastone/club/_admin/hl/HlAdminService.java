package com.theolympiastone.club._admin.hl;

import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Page;
import com.theolympiastone.club.common.model.Hl;

public class HlAdminService {
    public static final HlAdminService me = new HlAdminService();
    private final Hl dao = new Hl().dao();

    public Page<Hl> paginate(int pageNum) {
        return dao.paginate(pageNum, 100, "select *", "from hl order by id asc");
    }

    public Ret save(Hl hl) {
        hl.save();
        return Ret.ok("msg", "创建成功");
    }

    public Hl findById(Integer id) {
        return dao.findById(id);
    }

    public Ret update(Hl hl) {
        hl.update();
        return Ret.ok("msg", "修改成功");
    }

    public Ret delete(Integer id) {
        dao.deleteById(id);
        return Ret.ok("msg", "删除成功");
    }
}
