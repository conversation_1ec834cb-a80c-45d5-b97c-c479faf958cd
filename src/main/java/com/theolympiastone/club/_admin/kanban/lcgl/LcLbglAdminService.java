package com.theolympiastone.club._admin.kanban.lcgl;

import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import com.jfinal.plugin.activerecord.SqlPara;
import com.theolympiastone.club.common.model.Account;
import com.theolympiastone.club.common.model.Lcxmlb;

import java.util.Date;
import java.util.List;

public class LcLbglAdminService {
    public static final LcLbglAdminService me = new LcLbglAdminService();
    private final Lcxmlb dao = new Lcxmlb().dao();

    public Page<Record> paginate(int pageNum) {
        SqlPara sqlPara = Db.getSqlPara("lcgl.selectLcxmlbPage");
        return Db.paginate(pageNum, 50, sqlPara);
    }

    public Ret save(String loginAccount, Lcxmlb lcxmlb) {
        lcxmlb.setLmmc(lcxmlb.getLmmc().trim());
        lcxmlb.setCjsj(new Date());
        lcxmlb.setCjry(loginAccount);
        lcxmlb.save();
        return Ret.ok("msg", "创建成功");
    }

    public Lcxmlb findById(String xmid, String lmid) {
        return dao.findFirst("select * from lcxmlb where xmid=? and lmid=?", xmid, lmid);
    }

    public Ret update(Account loginAccount, Lcxmlb lcxmlb) {
        lcxmlb.setLmmc(lcxmlb.getLmmc().trim());
        lcxmlb.update();
        return Ret.ok("msg", "修改成功");
    }

    public Ret delete(String xmid, String lmid) {
        Db.delete("delete from lcxmlb where xmid=? and lmid=?", xmid, lmid);
        return Ret.ok("msg", "删除成功");
    }

    public List<Record> getJcsjList() {
        String sql = Db.getSql("lcgl.jcsjList");
        return Db.query(sql);
    }

    public List<Record> getXmList() {
        String sql = Db.getSql("lcgl.xmList");
        return Db.query(sql);
    }
}
