package com.theolympiastone.club._admin.lrhl;

import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Page;
import com.theolympiastone.club.common.model.Lrhl;

public class LrhlAdminService {
    public static final LrhlAdminService me = new LrhlAdminService();
    private final Lrhl dao = new Lrhl().dao();

    public Page<Lrhl> paginate(int pageNum) {
        return dao.paginate(pageNum, 100, "select *", "from lrhl order by id asc");
    }

    public Ret save(Lrhl lrhl) {
        lrhl.save();
        return Ret.ok("msg", "创建成功");
    }

    public Lrhl findById(Integer id) {
        return dao.findById(id);
    }

    public Ret update(Lrhl lrhl) {
        lrhl.update();
        return Ret.ok("msg", "修改成功");
    }

    public Ret delete(Integer id) {
        dao.deleteById(id);
        return Ret.ok("msg", "删除成功");
    }
}
