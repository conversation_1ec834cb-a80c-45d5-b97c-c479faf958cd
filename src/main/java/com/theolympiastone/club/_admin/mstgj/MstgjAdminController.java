package com.theolympiastone.club._admin.mstgj;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import com.jfinal.upload.UploadFile;
import com.theolympiastone.club.common.controller.BaseController;
import com.theolympiastone.club.common.model.Mstgj;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.List;
import java.util.Map;

public class MstgjAdminController extends BaseController {
    @Inject
    MstgjAdminService srv;

    public void index() {
        keepPara();
        String q = getPara("q", "");
        String sc = getPara("sc", "");
        String szid = getPara("szid", "");
        String myfs = getPara("myfs", "");
        String mc = getPara("mc", "");
        String px = getPara("px", " order by z.mc,z.sc,z.myfs,c.mc asc");
        Page<Record> mstgjPage = srv.paginate(getParaToInt("p", 1), q, sc, szid, myfs, mc, px);
        setAttr("szList", Db.find("select id,mc from cl order by mc"));
        setAttr("page", mstgjPage);
        render("index.html");
    }

    public void add() {
        setAttr("clList", Db.find("select id,mc from cl order by mc"));
        render("add_edit.html");
    }

    public void save() {
        Mstgj mstgj = getBean(Mstgj.class);
        Ret ret = srv.save(mstgj);
        renderJson(ret.set("id", mstgj.getId()));
    }

    public void edit() {
        keepPara();
        Mstgj mstgj = srv.findById(getParaToInt("id"));
        setAttr("mstgj", mstgj);
        setAttr("clList", Db.find("select id,mc from cl order by mc"));
        render("add_edit.html");
    }

    public void editJg() {
        Integer id = getParaToInt("id");
        String jg = getPara("jg");
        Mstgj mstgj = srv.findById(id);
        mstgj.setJg(jg);
        mstgj.update();
        renderJson(Ret.ok("修改成功!").set("id", id));
    }

    public void enable() {
        Integer id = getParaToInt("id");
        Mstgj mstgj = srv.findById(id);
        mstgj.setZt(StringUtils.equalsIgnoreCase(mstgj.getZt(), "展示") ? "不展示" : "展示");
        mstgj.update();
        renderJson(Ret.ok().set("msg", "设置成功!").set("id", id));
    }

    public void update() {
        Mstgj mstgj = getBean(Mstgj.class);
        Ret ret = srv.update(mstgj);
        renderJson(ret.set("id", mstgj.getId()));
    }

    public void delete() {
        srv.delete(getParaToInt("id"));
        redirect("/admin/mstgj");
    }

    public void importFile() {
        UploadFile file = getFile();
        List<Record> khs = Db.findAll("kh");
        Map<String, Integer> khMap = Maps.newHashMap();
        for (Record kh : khs) {
            khMap.put(kh.getStr("jc"), kh.getInt("id"));
        }

        List<Record> cls = Db.findAll("cl");
        Map<String, Integer> clMap = Maps.newHashMap();
        for (Record cl : cls) {
            clMap.put(cl.get("bh"), cl.getInt("id"));
        }

        List<String> readLines = null;
        try {
            readLines = FileUtils.readLines(file.getFile(), "UTF-8");
        } catch (IOException e) {
            e.printStackTrace();
        }
        String[] split = new String[0];
        if (readLines != null) {
            split = readLines.get(0).split(",", -1);
        }
        List<Integer> scList = Lists.newArrayList();
        List<String> dwList = Lists.newArrayList();
        for (int i = 2; i < split.length; i++) {
            String[] khHead = split[i].trim().split(" per ");
            scList.add(khMap.get(khHead[0]));
            dwList.add(khHead[1].trim().toUpperCase());
        }
        List<String> sqls = Lists.newArrayList();
        for (int i = 1; i < (readLines != null ? readLines.size() : 0); i++) {
            String[] values = readLines.get(i).split(",", -1);
            String bh = values[0];
            for (int j = 2; j < values.length; j++) {
                String value = values[j];
                Integer sc = scList.get(j - 2);
                Integer clId = clMap.get(bh);
                if (StringUtils.isEmpty(value)) {
                    sqls.add("delete from mstgj where sc=" + sc + " and szid=" + clId + ";");
                } else {
                    String jg = value.trim().replace(",", "");
                    String dw = dwList.get(j - 2);
                    sqls.add("insert into mstgj (sc,szid,jg,dw,kssj,jssj) values (" + sc + ", " + clId + ", '" + jg + "','" + dw + "','2020-02-01','2099-12-31') on DUPLICATE key update jg='" + jg + "', dw='" + dw + "';");
                }
            }
        }
        Db.batch(sqls, 1000);
        redirect("/admin/mstgj");
    }

    public void gx() {
        String ids = getPara("ids");
        String jssj = getPara("jssj");
        Db.update("update mstgj set jssj='" + jssj + "' where id in (" + ids + ")");
        renderJson(Ret.ok().set("msg", "更新成功!"));
    }
}
