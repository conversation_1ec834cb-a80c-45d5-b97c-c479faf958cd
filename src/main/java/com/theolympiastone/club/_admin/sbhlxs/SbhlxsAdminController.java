package com.theolympiastone.club._admin.sbhlxs;

import com.jfinal.aop.Before;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Page;
import com.theolympiastone.club.common.controller.BaseController;
import com.theolympiastone.club.common.model.Sbhlxs;

public class SbhlxsAdminController extends BaseController {
    @Inject
    SbhlxsAdminService srv;

    public void index() {
        keepPara();
        Page<Sbhlxs> sbhlxsPage = srv.paginate(getParaToInt("p", 1));
        setAttr("page", sbhlxsPage);
        render("index.html");
    }

    public void add() {
        render("add_edit.html");
    }

    @Before(SbhlxsAdminValidator.class)
    public void save() {
        Sbhlxs sbhlxs = getBean(Sbhlxs.class, true);
        Ret ret = srv.save(sbhlxs);
        renderJson(ret.set("id", sbhlxs.getId()));
    }

    public void edit() {
        keepPara();
        Sbhlxs sbhlxs = srv.findById(getParaToInt("id"));
        setAttr("sbhlxs", sbhlxs);
        render("add_edit.html");
    }

    @Before(SbhlxsAdminValidator.class)
    public void update() {
        Sbhlxs sbhlxs = getBean(Sbhlxs.class, true);
        Ret ret = srv.update(sbhlxs);
        renderJson(ret.set("id", sbhlxs.getId()));
    }

    public void delete() {
        srv.delete(getParaToInt("id"));
        renderJson(Ret.ok().set("msg", "删除成功!"));
    }
}
