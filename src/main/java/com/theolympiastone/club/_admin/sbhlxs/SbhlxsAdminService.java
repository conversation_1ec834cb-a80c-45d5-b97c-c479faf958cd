package com.theolympiastone.club._admin.sbhlxs;

import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Page;
import com.theolympiastone.club.common.model.Sbhlxs;

public class SbhlxsAdminService {
    public static final SbhlxsAdminService me = new SbhlxsAdminService();
    private final Sbhlxs dao = new Sbhlxs().dao();

    public Page<Sbhlxs> paginate(int pageNum) {
        return dao.paginate(pageNum, 100, "select *", "from sbhlxs order by id asc");
    }

    public Ret save(Sbhlxs sbhlxs) {
        sbhlxs.save();
        return Ret.ok("msg", "创建成功");
    }

    public Sbhlxs findById(Integer id) {
        return dao.findById(id);
    }

    public Ret update(Sbhlxs sbhlxs) {
        sbhlxs.update();
        return Ret.ok("msg", "修改成功");
    }

    public Ret delete(Integer id) {
        dao.deleteById(id);
        return Ret.ok("msg", "删除成功");
    }
}
