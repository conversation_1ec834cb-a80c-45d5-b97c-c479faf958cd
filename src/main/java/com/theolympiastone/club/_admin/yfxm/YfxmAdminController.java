package com.theolympiastone.club._admin.yfxm;

import com.jfinal.aop.Before;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Page;
import com.theolympiastone.club.common.controller.BaseController;
import com.theolympiastone.club.common.model.Yfxm;

public class YfxmAdminController extends BaseController {
    @Inject
    YfxmAdminService srv;

    public void index() {
        keepPara();
        Page<Yfxm> yfxmPage = srv.paginate(getParaToInt("p", 1));
        setAttr("page", yfxmPage);
        render("index.html");
    }

    public void add() {
        render("add_edit.html");
    }

    @Before(YfxmAdminValidator.class)
    public void save() {
        Yfxm yfxm = getBean(Yfxm.class);
        Ret ret = srv.save(yfxm);
        renderJson(ret.set("id", yfxm.getId()));
    }

    public void edit() {
        keepPara();
        Yfxm yfxm = srv.findById(getParaToInt("id"));
        setAttr("yfxm", yfxm);
        render("add_edit.html");
    }

    @Before(YfxmAdminValidator.class)
    public void update() {
        Yfxm yfxm = getBean(Yfxm.class);
        Ret ret = srv.update(yfxm);
        renderJson(ret.set("id", yfxm.getId()));
    }

    public void delete() {
        srv.delete(getParaToInt("id"));
        renderJson(Ret.ok().set("msg", "删除成功!"));
    }
}
