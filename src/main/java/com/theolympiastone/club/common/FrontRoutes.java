package com.theolympiastone.club.common;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.jfinal.config.Routes;
import com.theolympiastone.club.alarm.AlarmController;
import com.theolympiastone.club.common.interceptor.SessionInterceptor;
import com.theolympiastone.club.common.upload.UploadController;
import com.theolympiastone.club.document.DocumentController;
import com.theolympiastone.club.download.DownloadController;
import com.theolympiastone.club.feedback.FeedbackController;
import com.theolympiastone.club.index.ClashController;
import com.theolympiastone.club.index.ForTaiShuangHuiGuanController;
import com.theolympiastone.club.index.IndexController;
import com.theolympiastone.club.kanban.OrderController;
import com.theolympiastone.club.login.LoginController;
import com.theolympiastone.club.my.account.MyAccountController;
import com.theolympiastone.club.my.bb.MyBbController;
import com.theolympiastone.club.my.bb.MyKqBbController;
import com.theolympiastone.club.my.bq.MyBqController;
import com.theolympiastone.club.my.bx.MyBxController;
import com.theolympiastone.club.my.cgcp.MyCgcpController;
import com.theolympiastone.club.my.common.CommonController;
import com.theolympiastone.club.my.common.MyExportController;
import com.theolympiastone.club.my.cp.MyCpController;
import com.theolympiastone.club.my.cpfl.MyCpflController;
import com.theolympiastone.club.my.dd.MyDdController;
import com.theolympiastone.club.my.dp.DpController;
import com.theolympiastone.club.my.dpxs.MyDpxsController;
import com.theolympiastone.club.my.dqyx.MyDqyxController;
import com.theolympiastone.club.my.dzhy.MyDzhyController;
import com.theolympiastone.club.my.favorite.FavoriteController;
import com.theolympiastone.club.my.feedback.MyFeedbackController;
import com.theolympiastone.club.my.friend.MyFriendController;
import com.theolympiastone.club.my.gcbx.MyGcbxController;
import com.theolympiastone.club.my.gys.MyGysController;
import com.theolympiastone.club.my.gz.MyGzController;
import com.theolympiastone.club.my.hdlxr.MyHdlxrController;
import com.theolympiastone.club.my.hdzj.MyHdzjController;
import com.theolympiastone.club.my.hg.MyHgController;
import com.theolympiastone.club.my.hs.HsController;
import com.theolympiastone.club.my.hy.MyHyController;
import com.theolympiastone.club.my.jgbz.MyJgbzController;
import com.theolympiastone.club.my.jtxs.MyJtxsController;
import com.theolympiastone.club.my.jx.MyJxController;
import com.theolympiastone.club.my.kdf.MyKdfController;
import com.theolympiastone.club.my.kh.MyKhController;
import com.theolympiastone.club.my.khtx.MyKhtxController;
import com.theolympiastone.club.my.khtxzq.MyKhtxzqController;
import com.theolympiastone.club.my.khwt.MyKhwtController;
import com.theolympiastone.club.my.khxs.MyKhxsController;
import com.theolympiastone.club.my.kq_dk.MyKq_dkController;
import com.theolympiastone.club.my.kq_qj.MyKq_qjController;
import com.theolympiastone.club.my.like.LikeController;
import com.theolympiastone.club.my.lxrlb.MyLxrlbController;
import com.theolympiastone.club.my.message.MessageController;
import com.theolympiastone.club.my.mp.MyMpController;
import com.theolympiastone.club.my.ndmb.MyNdmbController;
import com.theolympiastone.club.my.newsfeed.NewsFeedController;
import com.theolympiastone.club.my.order.MyOrderController;
import com.theolympiastone.club.my.plrck.MyPlrckController;
import com.theolympiastone.club.my.project.MyProjectController;
import com.theolympiastone.club.my.px.MyPxController;
import com.theolympiastone.club.my.qm.MyQmController;
import com.theolympiastone.club.my.rb.MyRbController;
import com.theolympiastone.club.my.rck.MyRckController;
import com.theolympiastone.club.my.setting.MySettingController;
import com.theolympiastone.club.my.share.MyShareController;
import com.theolympiastone.club.my.sjx.MySjxController;
import com.theolympiastone.club.my.sp.MySpController;
import com.theolympiastone.club.my.sr.MySrController;
import com.theolympiastone.club.my.tc.MyTcController;
import com.theolympiastone.club.my.tip.MyTipController;
import com.theolympiastone.club.my.ts.MyTsController;
import com.theolympiastone.club.my.wcbx.MyWcbxController;
import com.theolympiastone.club.my.wdsxjd.MyWdsXjdController;
import com.theolympiastone.club.my.wdsyxkh.MyWdsYxkhController;
import com.theolympiastone.club.my.wiki.MyWikiController;
import com.theolympiastone.club.my.wjg.MyWjgController;
import com.theolympiastone.club.my.xjd.MyXjdController;
import com.theolympiastone.club.my.yf.MyYfController;
import com.theolympiastone.club.my.yj.MyYjController;
import com.theolympiastone.club.my.yjmb.MyYjmbController;
import com.theolympiastone.club.my.yjtc.MyYjtcController;
import com.theolympiastone.club.my.ylbyj.MyYlbyjController;
import com.theolympiastone.club.my.ylglgz.MyYlglgzController;
import com.theolympiastone.club.my.ylgrgz.MyYlgrgzController;
import com.theolympiastone.club.my.ylgz.MyYlgzController;
import com.theolympiastone.club.my.ylgzzf.MyYlgzzfController;
import com.theolympiastone.club.my.yxhd.MyYxhdController;
import com.theolympiastone.club.my.yxkh.MyYxkhController;
import com.theolympiastone.club.my.zb.MyZbController;
import com.theolympiastone.club.my.zcfz.MyZcfzController;
import com.theolympiastone.club.my.zdhf.MyZdhfController;
import com.theolympiastone.club.my.zf.MyZfController;
import com.theolympiastone.club.my.zh.MyZhController;
import com.theolympiastone.club.my.zt.MyZtController;
import com.theolympiastone.club.project.ProjectController;
import com.theolympiastone.club.reg.RegController;
import com.theolympiastone.club.share.ShareController;
import com.theolympiastone.club.user.feedback.UserFeedbackController;
import com.theolympiastone.club.user.friend.UserFriendController;
import com.theolympiastone.club.user.newsfeed.UserNewsFeedController;
import com.theolympiastone.club.user.project.UserProjectController;
import com.theolympiastone.club.user.share.UserShareController;
import com.theolympiastone.club.wechat.SignatureController;

import java.util.List;
import java.util.Set;

import com.theolympiastone.club.my.cgd.MyCgdController;
import com.theolympiastone.club.my.kc.MyKcController;
import com.theolympiastone.club.my.forum.ForumController;
import com.theolympiastone.club.my.forum.ForumPointsController;
import com.theolympiastone.club.my.question.MyQuestionController;
import com.theolympiastone.club.my.answer.MyAnswerController;
import com.theolympiastone.club.my.questionTemplate.MyQuestionTemplateController;
import com.theolympiastone.club.my.marketresearch.MarketResearchController;
//autoFrontImport

/**
 * 前台路由
 */
public class FrontRoutes extends Routes {
    public static List<String> publicControllerPathList = Lists.newArrayList(
            "/tshg", "/clash", "/dp", "/share", "/feedback", "/project", "/user",
            "/login", "/reg", "/download", "/like", "/doc", "/kanban", "/my/account",
            "/wx", "/alarm", "/my/c", "/my/hs", "/my/project", "/my/share", "/my/feedback",
            "/my/setting", "/my/friend", "/my/message", "/my/favorite", "/my/tip", "/my/yj", "/my/yj/yx",
            "/user/share", "/user/feedback", "/user/project", "/user/friend", "/my/dzhy", "/my/dd", "/my/bb",
            "/my/kq", "/common/upload", "/sq", "/sqB", "/my/yxkh", "/my/yxkh/gb",
            "/my/forum", "/my/points", "/my/forum/admin", "/my/points/admin", "/my/answer",
            "/my/marketresearch"
    );

    public static List<String> publicNoRbControllerPathList = Lists.newArrayList(
            "/", "/tshg", "/clash", "/dp", "/share", "/feedback", "/project", "/user",
            "/login", "/reg", "/download", "/like", "/doc", "/kanban", "/my/account",
            "/wx", "/alarm", "/my/c", "/my/hs", "/my/project", "/my/share", "/my/feedback",
            "/my/setting", "/my/friend", "/my/message", "/my/favorite",
            "/user/share", "/user/feedback", "/user/project", "/user/friend", "/my/kq_dk/dkqd",
            "/my/kq_dk", "/my/kq_qj", "/common/upload", "/sq", "/sqB", "/my/kq_dk/dk", "/my/kq_qj/add",
            "/my/account", "/my/account/gwrcgz", "/my/rb", "/my/rb/add", "/my/rb/edit", "/my/yxkh", "/my/yxkh/gb",
            "/my/forum", "/my/points", "/my/forum/admin", "/my/points/admin",
            "/my/marketresearch"
    );
    public static Set<String> controllerPathList = Sets.newHashSet();

    public void config() {
        addInterceptor(new SessionInterceptor());
        setBaseViewPath("/_view");
        add("/tshg", ForTaiShuangHuiGuanController.class);
        add("/clash", ClashController.class, "/clash");
        add("/my/c", CommonController.class);
        add("/my/hs", HsController.class);
        add("/dp", DpController.class);
        add("/", IndexController.class, "/index");
        add("/share", ShareController.class);
        add("/feedback", FeedbackController.class);
        add("/project", ProjectController.class);
        add("/login", LoginController.class);
        add("/reg", RegController.class);
        add("/common/upload", UploadController.class);
        add("/download", DownloadController.class);
        add("/like", LikeController.class);
        add("/doc", DocumentController.class, "/document");
        add("/kanban", OrderController.class, "/kanban");
        add("/wx", SignatureController.class, "/wx");
        add("/alarm", AlarmController.class, "/alarm");

        // 个人空间:由于共用了相同的拦截器，后续可将其拆分到 MyRoutes 中去，可减少拦截器配置冗余
        add("/my", NewsFeedController.class, "/my/newsfeed");
        add("/my/project", MyProjectController.class);
        add("/my/share", MyShareController.class);
        add("/my/feedback", MyFeedbackController.class);
        add("/my/setting", MySettingController.class);
        add("/my/friend", MyFriendController.class);
        add("/my/message", MessageController.class);
        add("/my/favorite", FavoriteController.class);
        add("/my/order", MyOrderController.class);
        add("/my/tip", MyTipController.class);

        // 用户空间
        add("/user", UserNewsFeedController.class, "/user/newsfeed");
        add("/user/share", UserShareController.class);
        add("/user/feedback", UserFeedbackController.class);
        add("/user/project", UserProjectController.class);
        add("/user/friend", UserFriendController.class);

        //培训相关
        add("/my/px", MyPxController.class);

        add("/my/sp", MySpController.class);
        add("/my/yf", MyYfController.class);
        add("/my/dd", MyDdController.class);
        add("/my/xjd", MyXjdController.class);
        add("/my/wds_xjd", MyWdsXjdController.class);
        add("/my/hg", MyHgController.class);
        add("/my/rb", MyRbController.class);
        add("/my/jx", MyJxController.class);
        add("/my/bx", MyBxController.class);
        add("/my/rck", MyRckController.class);
        add("/my/export", MyExportController.class);
        add("/my/hy", MyHyController.class);
        add("/my/khxs", MyKhxsController.class);
        add("/my/cpfl", MyCpflController.class);
        add("/my/cp", MyCpController.class);
        add("/my/bb", MyBbController.class);
        add("/my/kq", MyKqBbController.class);
        add("/my/zt", MyZtController.class);
        add("/my/zh", MyZhController.class);
        add("/my/tc", MyTcController.class);
        add("/my/wjg", MyWjgController.class);
        add("/my/dzhy", MyDzhyController.class);
        add("/my/hdlxr", MyHdlxrController.class);
        add("/my/yjmb", MyYjmbController.class);
        add("/my/wcbx", MyWcbxController.class);
        add("/my/gcbx", MyGcbxController.class);
        add("/my/gz", MyGzController.class);
        add("/my/kdf", MyKdfController.class);
        add("/my/yj", MyYjController.class);
        add("/my/qm", MyQmController.class);
        add("/my/lxrlb", MyLxrlbController.class);
        add("/my/yxhd", MyYxhdController.class);
        add("/my/zdhf", MyZdhfController.class);
        add("/my/ylgz", MyYlgzController.class);
        add("/my/ylgzzf", MyYlgzzfController.class);
        add("/my/yjtc", MyYjtcController.class);
        add("/my/kh", MyKhController.class);
        add("/my/yxkh", MyYxkhController.class);
        add("/my/wds_yxkh", MyWdsYxkhController.class);
        add("/my/khtx", MyKhtxController.class);
        add("/my/dqyx", MyDqyxController.class);
        add("/my/ylbyj", MyYlbyjController.class);
        add("/my/ylglgz", MyYlglgzController.class);
        add("/my/ylgrgz", MyYlgrgzController.class);
        add("/my/ts", MyTsController.class);
        add("/my/khwt", MyKhwtController.class);
        add("/my/bq", MyBqController.class);
        add("/my/wiki", MyWikiController.class);
        add("/my/khtxzq", MyKhtxzqController.class);
        add("/my/dpxs", MyDpxsController.class);
        add("/my/plrck", MyPlrckController.class);
        add("/my/hdzj", MyHdzjController.class);
        add("/my/zf", MyZfController.class);
        add("/my/sr", MySrController.class);
        add("/my/zb", MyZbController.class);
        add("/my/account", MyAccountController.class);
        add("/my/jgbz", MyJgbzController.class);
        add("/my/sjx", MySjxController.class);
        add("/my/gys", MyGysController.class);
        add("/my/ndmb", MyNdmbController.class);
        add("/my/zcfz", MyZcfzController.class);
        add("/my/jtxs", MyJtxsController.class);
        add("/my/kq_dk", MyKq_dkController.class);
        add("/my/kq_qj", MyKq_qjController.class);
        add("/my/mp", MyMpController.class);
        add("/my/cgcp", MyCgcpController.class);
        add("/my/cgd", MyCgdController.class);
        add("/my/kc", MyKcController.class);
        add("/my/fo", MyKcController.class);

        // Forum routes
        add("/my/forum", ForumController.class);
        add("/my/points", ForumPointsController.class);

        add("/my/question", MyQuestionController.class);
        add("/my/answer", MyAnswerController.class);
        add("/my/question_template", MyQuestionTemplateController.class);

        // 市场调研系统
        add("/my/marketresearch", MarketResearchController.class);
        //autoFrontAdd
        List<Route> routeItemList = this.getRouteItemList();
        for (Route route : routeItemList) {
            controllerPathList.add(route.getControllerPath());
        }
        controllerPathList.remove("/");
        controllerPathList.remove("/my");
    }
}
