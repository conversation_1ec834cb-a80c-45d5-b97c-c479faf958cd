package com.theolympiastone.club.common;

import com.google.common.collect.Lists;
import com.jfinal.plugin.activerecord.Record;

import java.util.List;

public class LayRecordData {
    public int code;
    public String msg;
    public int count;
    public List<Record> data;

    public LayRecordData(List<Record> data, Integer count) {
        this.code = 0;
        this.msg = "";
        this.count = (count == null) ? 0 : count;
        this.data = (data != null) ? data : Lists.newArrayList();
    }

    public LayRecordData(List<Record> data, int count) {
        this.code = 0;
        this.msg = "";
        this.count = count;
        this.data = (data != null) ? data : Lists.newArrayList();
    }

    public LayRecordData(List<Record> data) {
        this.code = 0;
        this.msg = "";
        this.count = data.size();
        this.data = data;
    }

    public LayRecordData(String msg, List<Record> data) {
        this.code = 0;
        this.msg = msg;
        this.count = data.size();
        this.data = data;
    }

    public LayRecordData(int code, String msg, List<Record> data) {
        this.code = code;
        this.msg = msg;
        this.count = data.size();
        this.data = data;
    }

    public LayRecordData(int code, String msg, int count, List<Record> data) {
        this.code = code;
        this.msg = msg;
        this.count = count;
        this.data = (data != null) ? data : Lists.newArrayList();
    }

    public int getTotal() {
        return count;
    }

    public void setTotal(int count) {
        this.count = count;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public List<Record> getData() {
        return data;
    }

    public void setData(List<Record> data) {
        this.data = data;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }
}
