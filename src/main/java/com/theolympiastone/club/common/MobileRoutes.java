package com.theolympiastone.club.common;

import com.jfinal.config.Routes;
import com.theolympiastone.club.common.interceptor.MobileAuthInterceptor;
import com.theolympiastone.club.mobile.IndexController;
import com.theolympiastone.club.mobile.LoginController;

/**
 * 前台路由
 */
public class MobileRoutes extends Routes {

    public void config() {
        setBaseViewPath("/_view/mobile");
        addInterceptor(new MobileAuthInterceptor());

        add("/m", IndexController.class, "/");
        add("/m/login", LoginController.class, "/");
    }
}
