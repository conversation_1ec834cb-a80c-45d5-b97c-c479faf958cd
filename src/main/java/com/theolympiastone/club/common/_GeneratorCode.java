package com.theolympiastone.club.common;

import com.google.common.collect.Lists;
import com.jfinal.kit.PathKit;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.generator.ColumnMeta;
import com.jfinal.plugin.activerecord.generator.TableMeta;
import com.jfinal.plugin.druid.DruidPlugin;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;

import javax.sql.DataSource;
import java.io.File;
import java.io.IOException;
import java.sql.*;
import java.util.ArrayList;
import java.util.List;

public class _GeneratorCode {

    public static final String TABLE_NAME_PATTERN = "question_template";

    public static void main(String[] args) {
        System.out.println("Build TableMeta ...");
        Connection conn = null;
        try {
            conn = getDataSource().getConnection();
            DatabaseMetaData dbMeta = conn.getMetaData();
            ResultSet rs = dbMeta.getTables(conn.getCatalog(), null, TABLE_NAME_PATTERN, new String[]{"TABLE"});

            List<TableMeta> ret = new ArrayList<>();
            buildTableNames(rs, ret);
            for (TableMeta tableMeta : ret) {
                buildPrimaryKey(conn, dbMeta, tableMeta);
                tableMeta.remarks = getCommentByTableName(conn, TABLE_NAME_PATTERN);
                buildColumnMetas(conn, tableMeta);
                String tableName = tableMeta.name;
                String TableName = StringUtils.capitalize(tableName);
//                generatorAdmin(tableMeta, tableName, TableName);
                generatorMy(tableMeta, tableName, TableName);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (conn != null) {
                try {
                    conn.close();
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    private static void generatorMy(TableMeta tableMeta, String tableName, String TableName) throws IOException {
        String frontRoute = PathKit.getWebRootPath() + "/../java/com/theolympiastone/club/common/FrontRoutes.java";
        String myMenu = PathKit.getWebRootPath() + "/_view/my/common/_my_menu_bar.html";
        String packageMyJavaOutput = PathKit.getWebRootPath() + "/../java/com/theolympiastone/club/my/" + tableName + "/";
        String packageMyHtmlOutput = PathKit.getWebRootPath() + "/../webapp/_view/my/" + tableName + "/";
        String myControllerTemplate = PathKit.getWebRootPath() + "/../resources/tpl/my/My$[TableName]Controller.tpl";
        String myServiceTemplate = PathKit.getWebRootPath() + "/../resources/tpl/my/My$[TableName]Service.tpl";
        String myValidatorTemplate = PathKit.getWebRootPath() + "/../resources/tpl/my/My$[TableName]Validator.tpl";
        String indexMyHtmlTemplate = PathKit.getWebRootPath() + "/../resources/tpl/my/index.html";
        String add_editMyHtmlTemplate = PathKit.getWebRootPath() + "/../resources/tpl/my/add_edit.html";

        FileUtils.forceMkdir(new File(packageMyJavaOutput));
        FileUtils.forceMkdir(new File(packageMyHtmlOutput));

        File myControllerFile = new File(packageMyJavaOutput + "My" + TableName + "Controller.java");
        FileUtils.copyFile(new File(myControllerTemplate), myControllerFile);
        File myServiceFile = new File(packageMyJavaOutput + "My" + TableName + "Service.java");
        FileUtils.copyFile(new File(myServiceTemplate), myServiceFile);
        File myValidatorFile = new File(packageMyJavaOutput + "My" + TableName + "Validator.java");
        FileUtils.copyFile(new File(myValidatorTemplate), myValidatorFile);
        File myIndexFile = new File(packageMyHtmlOutput + "index.html");
        FileUtils.copyFile(new File(indexMyHtmlTemplate), myIndexFile);
        File myAdd_EditFile = new File(packageMyHtmlOutput + "add_edit.html");
        FileUtils.copyFile(new File(add_editMyHtmlTemplate), myAdd_EditFile);

        replaceTemplateFile(myControllerFile, tableMeta);
        replaceTemplateFile(myServiceFile, tableMeta);
        replaceTemplateFile(myValidatorFile, tableMeta);
        replaceTemplateFile(myIndexFile, tableMeta);
        replaceTemplateFile(myAdd_EditFile, tableMeta);

        replaceTemplateFile(new File(frontRoute), tableMeta);
        replaceTemplateFile(new File(myMenu), tableMeta);
    }

    private static void generatorAdmin(TableMeta tableMeta, String tableName, String TableName) throws IOException {
        String adminRoute = PathKit.getWebRootPath() + "/../java/com/theolympiastone/club/_admin/common/AdminRoutes.java";
        String adminMenu = PathKit.getWebRootPath() + "/_view/_admin/common/_menu.html";

        String packageAdminJavaOutput = PathKit.getWebRootPath() + "/../java/com/theolympiastone/club/_admin/" + tableName + "/";
        String packageAdminHtmlOutput = PathKit.getWebRootPath() + "/_view/_admin/" + tableName + "/";
        //C:\Users\<USER>\IdeaProjects\osclub\target\classes\tpl\admin\$[TableName]AdminController.java
        //C:\Users\<USER>\IdeaProjects\osclub\src\main\webapp\src\main\resources\tpl\admin\$[TableName]AdminController.java
        String adminControllerTemplate = PathKit.getRootClassPath() + "/tpl/admin/$[TableName]AdminController.tpl";
        String adminServiceTemplate = PathKit.getRootClassPath() + "/tpl/admin/$[TableName]AdminService.tpl";
        String adminValidatorTemplate = PathKit.getRootClassPath() + "/tpl/admin/$[TableName]AdminValidator.tpl";
        String indexAdminHtmlTemplate = PathKit.getRootClassPath() + "/tpl/admin/index.html";
        String add_editAdminHtmlTemplate = PathKit.getRootClassPath() + "/tpl/admin/add_edit.html";

        FileUtils.forceMkdir(new File(packageAdminJavaOutput));
        FileUtils.forceMkdir(new File(packageAdminHtmlOutput));

        File adminControllerFile = new File(packageAdminJavaOutput + TableName + "AdminController.java");
        FileUtils.copyFile(new File(adminControllerTemplate), adminControllerFile);
        File adminServiceFile = new File(packageAdminJavaOutput + TableName + "AdminService.java");
        FileUtils.copyFile(new File(adminServiceTemplate), adminServiceFile);
        File adminValidatorFile = new File(packageAdminJavaOutput + TableName + "AdminValidator.java");
        FileUtils.copyFile(new File(adminValidatorTemplate), adminValidatorFile);
        File adminIndexFile = new File(packageAdminHtmlOutput + "index.html");
        FileUtils.copyFile(new File(indexAdminHtmlTemplate), adminIndexFile);
        File adminAdd_EditFile = new File(packageAdminHtmlOutput + "add_edit.html");
        FileUtils.copyFile(new File(add_editAdminHtmlTemplate), adminAdd_EditFile);

        replaceTemplateFile(adminControllerFile, tableMeta);
        replaceTemplateFile(adminServiceFile, tableMeta);
        replaceTemplateFile(adminValidatorFile, tableMeta);
        replaceTemplateFile(adminIndexFile, tableMeta);
        replaceTemplateFile(adminAdd_EditFile, tableMeta);

        replaceTemplateFile(new File(adminRoute), tableMeta);
        replaceTemplateFile(new File(adminMenu), tableMeta);
    }

    public static String parse(String all) {
        String comment;
        int index = all.indexOf("COMMENT='");
        if (index < 0) {
            return "";
        }
        comment = all.substring(index + 9);
        comment = comment.substring(0, comment.length() - 1);
        return comment;
    }

    public static String getCommentByTableName(Connection conn, String tableName) {
        String comment = "";
        Statement stmt = null;
        ResultSet rs = null;
        try {
            stmt = conn.createStatement();
            rs = stmt.executeQuery("SHOW CREATE TABLE " + tableName);
            if (rs != null && rs.next()) {
                String createDDL = rs.getString(2);
                comment = parse(createDDL);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (rs != null) {
                try {
                    rs.close();
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
            if (stmt != null) {
                try {
                    stmt.close();
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
        }
        return comment;
    }

    private static void replaceTemplateFile(File templateFile, TableMeta tableMeta) throws IOException {
        List<String> ignoreList = Lists.newArrayList("cjr", "cjsj");
        String primaryKey = tableMeta.primaryKey;
        String tableName = tableMeta.name;
        String TableName = StringUtils.capitalize(tableName);
        String templateContent = FileUtils.readFileToString(templateFile, "UTF-8");
        List<ColumnMeta> columnMetaList = tableMeta.columnMetas;
        StringBuilder th_columns = new StringBuilder();
        StringBuilder td_columns = new StringBuilder();
        StringBuilder add_columns = new StringBuilder();
        StringBuilder all_columns = new StringBuilder();
        for (ColumnMeta columnMeta : columnMetaList) {
            if (!columnMeta.name.equalsIgnoreCase(primaryKey)) {
                th_columns.append("<th>").append(columnMeta.remarks).append("</th>\n\t\t\t\t\t");
                td_columns.append("<td>#(x.").append(columnMeta.name).append("??)</td>\n\t\t\t\t\t");
                if (!ignoreList.contains(columnMeta.name)) {
                    add_columns.append("<div class=\"layui-form-item\">\n" +
                            "               <div class=\"form-inline\">\n" +
                            "                   <label class=\"layui-form-label\">").append(columnMeta.remarks).append(":</label>\n" +
                            "                   <div class=\"layui-input-inline\" style=\"width: 300px;\">\n" +
                            "                       <input type=\"text\" name=\"").append(tableName).append(".").append(columnMeta.name).append("\" value=\"#(").append(tableName).append(".").append(columnMeta.name).append("??)\"").append(" lay-verify=\"required\" placeholder=\"请输入").append(columnMeta.remarks).append("\" autocomplete=\"off\" class=\"layui-input\">\n").append("                   </div>\n").append("                </div>\n").append("        </div>\n");
                }
                all_columns.append("\"").append(columnMeta.name).append("\",");
            }
        }
        String allColumns = all_columns.toString();
        allColumns = allColumns.substring(0, all_columns.length() - 2);
        String resultContent = templateContent.replace("$[TableName]", TableName)
                .replace("$[tableName]", tableName)
                .replace("$[tableNameCN]", tableMeta.remarks)
                .replace("$[key]", primaryKey)
                .replace("//autoAdd", "add(\"/admin/" + tableName + "\", " + TableName + "AdminController.class, \"/" + tableName + "\");\n        //autoAdd")
                .replace("//autoFrontAdd", "add(\"/my/" + tableName + "\", My" + TableName + "Controller.class);\n        //autoFrontAdd")
                .replace("//autoImport", "import com.theolympiastone.club._admin." + tableName + "." + TableName + "AdminController;\n//autoImport")
                .replace("//autoFrontImport", "import com.theolympiastone.club.my." + tableName + ".My" + TableName + "Controller;\n//autoFrontImport")
                .replace("<!-- autoMyMenu -->", "<dd><a href=\"/my/" + tableName + "\">" + tableMeta.remarks + "管理</a></dd>\n                    <!-- autoMyMenu -->")
                .replace("<!-- autoAdminMenu -->", "<dd><a data-pjax href=\"/admin/" + tableName + "\">" + tableMeta.remarks + "</a></dd>\n                    <!-- autoAdminMenu -->")
                .replace("$[th_columns]", th_columns.toString())
                .replace("$[td_columns]", td_columns.toString())
                .replace("$[add-columns]", add_columns.toString())
                .replace("$[all_columns]", allColumns);
        FileUtils.write(templateFile, resultContent, "UTF-8", false);
    }

    private static DataSource getDataSource() {
        DruidPlugin druidPlugin = OSClubConfig.getDruidPlugin();
        druidPlugin.start();
        return druidPlugin.getDataSource();
    }

    public static void buildTableNames(ResultSet rs, List<TableMeta> ret) throws SQLException {
        while (rs.next()) {
            String tableName = rs.getString("TABLE_NAME");
            TableMeta tableMeta = new TableMeta();
            tableMeta.name = tableName;
            tableMeta.remarks = rs.getString("REMARKS");
            ret.add(tableMeta);
        }
        rs.close();
    }

    public static void buildPrimaryKey(Connection conn, DatabaseMetaData dbMeta, TableMeta tableMeta) throws SQLException {
        ResultSet rs = dbMeta.getPrimaryKeys(conn.getCatalog(), null, tableMeta.name);

        StringBuilder primaryKey = new StringBuilder();
        int index = 0;
        while (rs.next()) {
            if (index++ > 0) {
                primaryKey.append(",");
            }
            primaryKey.append(rs.getString("COLUMN_NAME"));
        }
        tableMeta.primaryKey = primaryKey.toString();
        rs.close();
    }

    public static void buildColumnMetas(Connection conn, TableMeta tableMeta) throws Exception {
        String sql = "select `column_name` as name, data_type as type, character_maximum_length as maxLength,column_comment as remarks,column_key as isPrimaryKey,is_nullable as isNullable from information_schema.columns where table_schema='osclub' AND  table_name = '" + TABLE_NAME_PATTERN + "' order by ORDINAL_POSITION asc";
        Statement statement = conn.createStatement();
        ResultSet resultSet = statement.executeQuery(sql);
        while (resultSet.next()) {

            ColumnMeta cm = new ColumnMeta();
            cm.name = resultSet.getString("name");
            cm.attrName = StrKit.toCamelCase(cm.name);
            cm.type = resultSet.getString("type");
            cm.remarks = resultSet.getString("remarks");
            cm.isPrimaryKey = resultSet.getString("isPrimaryKey");
            cm.isNullable = resultSet.getString("isNullable");
            tableMeta.columnMetas.add(cm);
        }
        resultSet.close();
        statement.close();
    }
}
