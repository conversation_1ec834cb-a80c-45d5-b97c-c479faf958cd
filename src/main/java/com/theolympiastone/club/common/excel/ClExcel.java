package com.theolympiastone.club.common.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;

import java.net.URL;

@ContentRowHeight(100)
public class ClExcel {
    @ColumnWidth(20)
    @ExcelProperty({"Stone Color", "图片/Picture"})
    private URL url;
    @ColumnWidth(13)
    @ExcelProperty({"Stone Color", "名称/Chinese Name"})
    private String mc;
    @ColumnWidth(13)
    @ExcelProperty({"Stone Color", "名称/Name"})
    private String ywmc;
    @ColumnWidth(13)
    @ExcelProperty({"Stone Color", "名称/Other Name"})
    private String bzm;

    @ColumnWidth(13)
    @ExcelProperty({"Stone Color", "颜色/Chinese Color"})
    private String ys;
    @ColumnWidth(13)
    @ExcelProperty({"Stone Color", "颜色/Color"})
    private String ywys;

    @ColumnWidth(13)
    @ExcelProperty({"Stone Color", "顺序/Order"})
    private String px;
    @ColumnWidth(20)
    @ExcelProperty({"Stone Color", "图片/Picture"})
    private URL url1;
    @ColumnWidth(20)
    @ExcelProperty({"Stone Color", "图片/Picture"})
    private URL url2;
    @ColumnWidth(20)
    @ExcelProperty({"Stone Color", "图片/Picture"})
    private URL url3;
    @ColumnWidth(20)
    @ExcelProperty({"Stone Color", "图片/Picture"})
    private URL url4;
    @ColumnWidth(20)
    @ExcelProperty({"Stone Color", "图片/Picture"})
    private URL url5;

    public URL getUrl1() {
        return url1;
    }

    public void setUrl1(URL url1) {
        this.url1 = url1;
    }

    public URL getUrl2() {
        return url2;
    }

    public void setUrl2(URL url2) {
        this.url2 = url2;
    }

    public URL getUrl3() {
        return url3;
    }

    public void setUrl3(URL url3) {
        this.url3 = url3;
    }

    public URL getUrl4() {
        return url4;
    }

    public void setUrl4(URL url4) {
        this.url4 = url4;
    }

    public URL getUrl5() {
        return url5;
    }

    public void setUrl5(URL url5) {
        this.url5 = url5;
    }

    public URL getUrl() {
        return url;
    }

    public void setUrl(URL url) {
        this.url = url;
    }

    public String getMc() {
        return mc;
    }

    public void setMc(String mc) {
        this.mc = mc;
    }

    public String getYwmc() {
        return ywmc;
    }

    public void setYwmc(String ywmc) {
        this.ywmc = ywmc;
    }

    public String getYs() {
        return ys;
    }

    public void setYs(String ys) {
        this.ys = ys;
    }

    public String getYwys() {
        return ywys;
    }

    public void setYwys(String ywys) {
        this.ywys = ywys;
    }

    public String getPx() {
        return px;
    }

    public void setPx(String px) {
        this.px = px;
    }

    public String getBzm() {
        return bzm;
    }

    public void setBzm(String bzm) {
        this.bzm = bzm;
    }
}
