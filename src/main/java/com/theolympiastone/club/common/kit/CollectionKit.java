package com.theolympiastone.club.common.kit;

import com.google.common.collect.Lists;
import com.jfinal.kit.JsonKit;
import com.jfinal.plugin.activerecord.Record;

import java.util.List;

public class CollectionKit {
    public static List<String> mapper(List<String> stringList1, List<String> stringList2) {
        List<String> resultList = Lists.newArrayList();
        for (String s1 : stringList1) {
            for (String s2 : stringList2) {
                resultList.add(s1 + s2);
            }
        }
        return resultList;
    }

    public static List<String> join(List<String> stringList1, List<String> stringList2) {
        stringList1.addAll(stringList2);
        return stringList1;
    }

    public static String listToJson(List<Record> recordList) {
        return JsonKit.toJson(recordList);
    }

    public static String toJsonEscapeApostrophe(List<Record> recordList) {
        String json = listToJson(recordList)
                .replace("'", "\\'")
                .replace("\"", "\\\"")
                .replace("\\n", "\\\\n")
                .replace("\\t", "    ")
                .replace("\\r", "\\\\r");
        return json;
    }

    public static String listToJson(List<Record> recordList, String column) {
        List<String> stringList = Lists.newArrayList();
        for (Record record : recordList) {
            stringList.add(record.getStr(column));
        }
        return JsonKit.toJson(stringList);
    }

    public static void main(String[] args) {
        Record record = new Record();
        record.set("abc", "a");
        record.set("baa", 1);
        record.set("ccc", 1.0);
        List<Record> recordList = Lists.newArrayList(record);
        System.out.println(record.toJson());
        System.out.println(JsonKit.toJson(recordList));
    }
}
