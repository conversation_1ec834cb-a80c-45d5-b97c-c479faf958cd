package com.theolympiastone.club.common.kit;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

import com.jfinal.kit.Kv;
import com.jfinal.plugin.activerecord.Record;

/**
 * 装柜模拟类
 * 用于模拟货物在集装箱中的装载情况
 */
public class ContainerLoadingSimulation {

    /**
     * 标准集装箱内部尺寸（毫米）
     */
    public static final Map<String, double[]> STANDARD_CONTAINERS = new HashMap<>();

    static {
        // 初始化标准集装箱内部尺寸 (长, 宽, 高) 单位：毫米
        STANDARD_CONTAINERS.put("20gp", new double[]{5896, 2350, 2393});
        STANDARD_CONTAINERS.put("40gp", new double[]{12032, 2350, 2393});
        STANDARD_CONTAINERS.put("40hq", new double[]{12032, 2350, 2698});
        STANDARD_CONTAINERS.put("45hq", new double[]{13556, 2350, 2698});
        STANDARD_CONTAINERS.put("20ot", new double[]{5896, 2350, 2393}); // Open Top
    }

    // 货物列表
    private final List<CargoItem> cargoItems;

    // 集装箱尺寸
    private final double containerLength;
    private final double containerWidth;
    private final double containerHeight;

    // 货物间隙（毫米）
    private final double gap;

    // 装箱算法
    private PackingAlgorithm algorithm = PackingAlgorithm.GREEDY;

    // 装载结果
    private boolean canFit = false;
    private List<PlacedItem> placedItems = new ArrayList<>();
    private List<CargoItem> unplacedItems = new ArrayList<>(); // 未装载的货物
    private double volumeUtilization = 0.0;
    private double weightUtilization = 0.0;
    private String message = "";

    /**
     * 装箱算法枚举
     */
    public enum PackingAlgorithm {
        GREEDY("贪心算法"),
        MAXIMAL_SPACE("极大空间算法"),
        GENETIC("遗传算法"),
        LAYER_BASED("层叠装箱算法"),
        HYBRID("混合启发式算法");

        private final String displayName;

        PackingAlgorithm(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }

    /**
     * 构造函数
     *
     * @param cargoItems    货物列表
     * @param containerType 集装箱类型 (20gp, 40gp, 40hq, 45hq)
     * @param gap           货物间隙（毫米）
     */
    public ContainerLoadingSimulation(List<CargoItem> cargoItems, String containerType, double gap) {
        this(cargoItems,
                STANDARD_CONTAINERS.get(containerType.toLowerCase())[0],
                STANDARD_CONTAINERS.get(containerType.toLowerCase())[1],
                STANDARD_CONTAINERS.get(containerType.toLowerCase())[2],
                gap);
    }

    /**
     * 构造函数
     *
     * @param cargoItems    货物列表
     * @param containerType 集装箱类型 (20gp, 40gp, 40hq, 45hq)
     * @param gap           货物间隙（毫米）
     * @param algorithm     装箱算法
     */
    public ContainerLoadingSimulation(List<CargoItem> cargoItems, String containerType, double gap, PackingAlgorithm algorithm) {
        this(cargoItems,
                STANDARD_CONTAINERS.get(containerType.toLowerCase())[0],
                STANDARD_CONTAINERS.get(containerType.toLowerCase())[1],
                STANDARD_CONTAINERS.get(containerType.toLowerCase())[2],
                gap,
                algorithm);
    }

    /**
     * 构造函数
     *
     * @param cargoItems      货物列表
     * @param containerLength 集装箱长度（毫米）
     * @param containerWidth  集装箱宽度（毫米）
     * @param containerHeight 集装箱高度（毫米）
     * @param gap             货物间隙（毫米）
     */
    public ContainerLoadingSimulation(List<CargoItem> cargoItems, double containerLength,
                                      double containerWidth, double containerHeight, double gap) {
        this(cargoItems, containerLength, containerWidth, containerHeight, gap, PackingAlgorithm.GREEDY);
    }

    /**
     * 构造函数
     *
     * @param cargoItems      货物列表
     * @param containerLength 集装箱长度（毫米）
     * @param containerWidth  集装箱宽度（毫米）
     * @param containerHeight 集装箱高度（毫米）
     * @param gap             货物间隙（毫米）
     * @param algorithm       装箱算法
     */
    public ContainerLoadingSimulation(List<CargoItem> cargoItems, double containerLength,
                                      double containerWidth, double containerHeight, double gap,
                                      PackingAlgorithm algorithm) {
        this.cargoItems = new ArrayList<>(cargoItems);
        this.containerLength = containerLength;
        this.containerWidth = containerWidth;
        this.containerHeight = containerHeight;
        this.gap = gap;
        this.algorithm = algorithm;

        // 按体积从大到小排序货物
        this.cargoItems.sort((a, b) -> Double.compare(
                b.getLength() * b.getWidth() * b.getHeight(),
                a.getLength() * a.getWidth() * a.getHeight()
        ));
    }

    /**
     * 执行装载模拟
     *
     * @return 是否能够装载所有货物
     */
    public boolean simulate() {
        // 根据选择的算法执行不同的装箱策略
        switch (algorithm) {
            case MAXIMAL_SPACE:
                return simulateWithMaximalSpace();
            case GENETIC:
                return simulateWithGenetic();
            case LAYER_BASED:
                return simulateWithLayerBased();
            case HYBRID:
                return simulateWithHybrid();
            case GREEDY:
            default:
                return simulateWithGreedy();
        }
    }

    /**
     * 使用贪心算法执行装载模拟
     *
     * @return 是否能够装载所有货物
     */
    private boolean simulateWithGreedy() {
        // 初始化空间
        Space space = new Space(containerLength, containerWidth, containerHeight);

        // 尝试放置所有货物
        List<PlacedItem> tempPlacedItems = new ArrayList<>();
        List<CargoItem> unplacedItems = new ArrayList<>();

        for (CargoItem item : cargoItems) {
            // 尝试放置当前货物
            PlacedItem placedItem = tryPlaceItem(space, item);

            if (placedItem != null) {
                tempPlacedItems.add(placedItem);
                // 更新可用空间
                space.updateAvailableSpace(placedItem);
            } else {
                unplacedItems.add(item);
            }
        }

        // 计算利用率
        double totalCargoVolume = tempPlacedItems.stream()
                .mapToDouble(item -> item.getLength() * item.getWidth() * item.getHeight())
                .sum();

        double containerVolume = containerLength * containerWidth * containerHeight;
        double utilization = totalCargoVolume / containerVolume * 100;

        // 设置结果
        this.placedItems = tempPlacedItems;
        this.unplacedItems = new ArrayList<>(unplacedItems); // 保存未装载的货物
        this.volumeUtilization = utilization;
        this.canFit = unplacedItems.isEmpty();

        if (canFit) {
            this.message = "所有货物可以装入集装箱，空间利用率: " + String.format("%.2f", utilization) + "%";
        } else {
            this.message = "无法装入所有货物，还有 " + unplacedItems.size() + " 件货物无法装入，当前空间利用率: " + String.format("%.2f", utilization) + "%";
        }

        return canFit;
    }

    /**
     * 使用极大空间算法执行装载模拟
     *
     * @return 是否能够装载所有货物
     */
    private boolean simulateWithMaximalSpace() {
        // 初始化空间
        MaximalSpace space = new MaximalSpace(containerLength, containerWidth, containerHeight);

        // 尝试放置所有货物
        List<PlacedItem> tempPlacedItems = new ArrayList<>();
        List<CargoItem> unplacedItems = new ArrayList<>();

        for (CargoItem item : cargoItems) {
            // 尝试放置当前货物
            PlacedItem placedItem = tryPlaceItemMaximalSpace(space, item);

            if (placedItem != null) {
                tempPlacedItems.add(placedItem);
                // 更新可用空间
                space.updateAvailableSpace(placedItem);
            } else {
                unplacedItems.add(item);
            }
        }

        // 计算利用率和设置结果
        calculateAndSetResults(tempPlacedItems, unplacedItems);

        return canFit;
    }

    /**
     * 使用遗传算法执行装载模拟
     *
     * @return 是否能够装载所有货物
     */
    private boolean simulateWithGenetic() {
        // 遗传算法参数
        int populationSize = 50;
        int generations = 100;
        double mutationRate = 0.1;
        double crossoverRate = 0.8;

        // 初始化种群
        List<List<PlacedItem>> population = initializePopulation(populationSize);

        // 进化多代
        List<PlacedItem> bestSolution = null;
        double bestFitness = 0;

        for (int i = 0; i < generations; i++) {
            // 评估适应度
            List<Double> fitness = evaluatePopulation(population);

            // 找到最佳解
            int bestIndex = 0;
            for (int j = 0; j < fitness.size(); j++) {
                if (fitness.get(j) > fitness.get(bestIndex)) {
                    bestIndex = j;
                }
            }

            if (bestSolution == null || fitness.get(bestIndex) > bestFitness) {
                bestSolution = new ArrayList<>(population.get(bestIndex));
                bestFitness = fitness.get(bestIndex);
            }

            // 选择、交叉和变异
            population = evolvePopulation(population, fitness, mutationRate, crossoverRate);
        }

        // 计算未放置的货物
        List<CargoItem> unplacedItems = new ArrayList<>(cargoItems);
        for (PlacedItem placedItem : bestSolution) {
            unplacedItems.removeIf(item -> item.getId().equals(placedItem.getId()));
        }

        // 计算利用率和设置结果
        this.placedItems = bestSolution;
        calculateAndSetResults(bestSolution, unplacedItems);

        return canFit;
    }

    /**
     * 使用层叠装箱算法执行装载模拟
     *
     * @return 是否能够装载所有货物
     */
    private boolean simulateWithLayerBased() {
        // 初始化层结构
        List<Layer> layers = new ArrayList<>();
        layers.add(new Layer(0, containerLength, containerWidth));

        // 尝试放置所有货物
        List<PlacedItem> tempPlacedItems = new ArrayList<>();
        List<CargoItem> unplacedItems = new ArrayList<>();

        for (CargoItem item : cargoItems) {
            boolean placed = false;

            // 尝试在现有层放置
            for (Layer layer : layers) {
                PlacedItem placedItem = tryPlaceItemInLayer(layer, item);
                if (placedItem != null) {
                    tempPlacedItems.add(placedItem);
                    placed = true;
                    break;
                }
            }

            // 如果无法放置，创建新层
            if (!placed) {
                double currentHeight = layers.get(layers.size() - 1).getHeight();
                double newLayerHeight = currentHeight + item.getHeight() + gap;

                if (newLayerHeight <= containerHeight) {
                    Layer newLayer = new Layer(currentHeight + gap, containerLength, containerWidth);
                    layers.add(newLayer);

                    PlacedItem placedItem = tryPlaceItemInLayer(newLayer, item);
                    if (placedItem != null) {
                        tempPlacedItems.add(placedItem);
                        placed = true;
                    }
                }
            }

            if (!placed) {
                unplacedItems.add(item);
            }
        }

        // 计算利用率和设置结果
        calculateAndSetResults(tempPlacedItems, unplacedItems);

        return canFit;
    }

    /**
     * 使用混合启发式算法执行装载模拟
     *
     * @return 是否能够装载所有货物
     */
    private boolean simulateWithHybrid() {
        // 尝试多种策略
        List<SimulationResult> results = new ArrayList<>();

        // 策略 1: 贪心算法
        PackingAlgorithm originalAlgorithm = this.algorithm;
        this.algorithm = PackingAlgorithm.GREEDY;
        simulateWithGreedy();
        results.add(new SimulationResult(new ArrayList<>(placedItems), volumeUtilization, canFit));

        // 策略 2: 极大空间算法
        this.algorithm = PackingAlgorithm.MAXIMAL_SPACE;
        simulateWithMaximalSpace();
        results.add(new SimulationResult(new ArrayList<>(placedItems), volumeUtilization, canFit));

        // 策略 3: 层叠装箱算法
        this.algorithm = PackingAlgorithm.LAYER_BASED;
        simulateWithLayerBased();
        results.add(new SimulationResult(new ArrayList<>(placedItems), volumeUtilization, canFit));

        // 选择最佳结果
        SimulationResult bestResult = results.stream()
                .max(Comparator.comparingDouble(SimulationResult::getUtilization))
                .orElse(results.get(0));

        // 计算未放置的货物
        List<CargoItem> unplacedItems = new ArrayList<>(cargoItems);
        for (PlacedItem placedItem : bestResult.getPlacedItems()) {
            unplacedItems.removeIf(item -> item.getId().equals(placedItem.getId()));
        }

        // 设置结果
        this.placedItems = bestResult.getPlacedItems();
        this.volumeUtilization = bestResult.getUtilization();
        this.canFit = bestResult.isCanFit();

        if (canFit) {
            this.message = "所有货物可以装入集装箱，空间利用率: " + String.format("%.2f", volumeUtilization) + "%";
        } else {
            this.message = "无法装入所有货物，还有 " + unplacedItems.size() + " 件货物无法装入，当前空间利用率: " + String.format("%.2f", volumeUtilization) + "%";
        }

        // 恢复原始算法
        this.algorithm = originalAlgorithm;

        return canFit;
    }

    /**
     * 计算利用率并设置结果
     */
    private void calculateAndSetResults(List<PlacedItem> tempPlacedItems, List<CargoItem> unplacedItems) {
        // 计算利用率
        double totalCargoVolume = tempPlacedItems.stream()
                .mapToDouble(item -> item.getLength() * item.getWidth() * item.getHeight())
                .sum();

        double containerVolume = containerLength * containerWidth * containerHeight;
        double utilization = totalCargoVolume / containerVolume * 100;

        // 设置结果
        this.placedItems = tempPlacedItems;
        this.unplacedItems = new ArrayList<>(unplacedItems); // 保存未装载的货物
        this.volumeUtilization = utilization;
        this.canFit = unplacedItems.isEmpty();

        if (canFit) {
            this.message = "所有货物可以装入集装箱，空间利用率: " + String.format("%.2f", utilization) + "%";
        } else {
            this.message = "无法装入所有货物，还有 " + unplacedItems.size() + " 件货物无法装入，当前空间利用率: " + String.format("%.2f", utilization) + "%";
        }
    }

    /**
     * 尝试放置货物
     *
     * @param space 可用空间
     * @param item  货物
     * @return 放置后的货物位置，如果无法放置则返回null
     */
    private PlacedItem tryPlaceItem(Space space, CargoItem item) {
        // 考虑6种不同的旋转方式
        double[][] rotations = {
                {item.getLength(), item.getWidth(), item.getHeight()},
                {item.getLength(), item.getHeight(), item.getWidth()},
                {item.getWidth(), item.getLength(), item.getHeight()},
                {item.getWidth(), item.getHeight(), item.getLength()},
                {item.getHeight(), item.getLength(), item.getWidth()},
                {item.getHeight(), item.getWidth(), item.getLength()}
        };

        for (double[] rotation : rotations) {
            double rotatedLength = rotation[0];
            double rotatedWidth = rotation[1];
            double rotatedHeight = rotation[2];

            // 考虑间隙
            double effectiveLength = rotatedLength + gap;
            double effectiveWidth = rotatedWidth + gap;
            double effectiveHeight = rotatedHeight + gap;

            // 检查是否有足够空间放置
            for (AvailableSpace availableSpace : space.getAvailableSpaces()) {
                if (availableSpace.getLength() >= effectiveLength &&
                        availableSpace.getWidth() >= effectiveWidth &&
                        availableSpace.getHeight() >= effectiveHeight) {

                    // 可以放置，创建放置后的货物
                    PlacedItem placedItem = new PlacedItem(
                            item.getId(),
                            item.getName(),
                            rotatedLength,
                            rotatedWidth,
                            rotatedHeight,
                            availableSpace.getX(),
                            availableSpace.getY(),
                            availableSpace.getZ(),
                            item.getWeight()
                    );

                    return placedItem;
                }
            }
        }

        // 无法放置
        return null;
    }

    /**
     * 使用极大空间算法尝试放置货物
     */
    private PlacedItem tryPlaceItemMaximalSpace(MaximalSpace space, CargoItem item) {
        // 考虑六种不同的旋转方式
        double[][] rotations = {
                {item.getLength(), item.getWidth(), item.getHeight()},
                {item.getLength(), item.getHeight(), item.getWidth()},
                {item.getWidth(), item.getLength(), item.getHeight()},
                {item.getWidth(), item.getHeight(), item.getLength()},
                {item.getHeight(), item.getLength(), item.getWidth()},
                {item.getHeight(), item.getWidth(), item.getLength()}
        };

        // 计算最佳放置位置
        PlacedItem bestPlacement = null;
        double minWastedSpace = Double.MAX_VALUE;

        for (double[] rotation : rotations) {
            double rotatedLength = rotation[0];
            double rotatedWidth = rotation[1];
            double rotatedHeight = rotation[2];

            // 考虑间隙
            double effectiveLength = rotatedLength + gap;
            double effectiveWidth = rotatedWidth + gap;
            double effectiveHeight = rotatedHeight + gap;

            for (AvailableSpace availableSpace : space.getMaximalSpaces()) {
                if (availableSpace.getLength() >= effectiveLength &&
                        availableSpace.getWidth() >= effectiveWidth &&
                        availableSpace.getHeight() >= effectiveHeight) {

                    // 计算浪费空间
                    double wastedSpace = (availableSpace.getLength() - effectiveLength) *
                            (availableSpace.getWidth() - effectiveWidth) *
                            (availableSpace.getHeight() - effectiveHeight);

                    // 如果浪费空间更少，更新最佳放置
                    if (wastedSpace < minWastedSpace) {
                        minWastedSpace = wastedSpace;
                        bestPlacement = new PlacedItem(
                                item.getId(),
                                item.getName(),
                                rotatedLength,
                                rotatedWidth,
                                rotatedHeight,
                                availableSpace.getX(),
                                availableSpace.getY(),
                                availableSpace.getZ(),
                                item.getWeight()
                        );
                    }
                }
            }
        }

        return bestPlacement;
    }

    /**
     * 在层中尝试放置货物
     */
    private PlacedItem tryPlaceItemInLayer(Layer layer, CargoItem item) {
        // 考虑两种旋转方式（保持高度不变）
        double[][] rotations = {
                {item.getLength(), item.getWidth(), item.getHeight()},
                {item.getWidth(), item.getLength(), item.getHeight()}
        };

        // 已放置的货物
        List<PlacedItem> placedItems = layer.getPlacedItems();

        // 尝试不同的旋转方式
        for (double[] rotation : rotations) {
            double rotatedLength = rotation[0];
            double rotatedWidth = rotation[1];
            double rotatedHeight = rotation[2];

            // 考虑间隙
            double effectiveLength = rotatedLength + gap;
            double effectiveWidth = rotatedWidth + gap;

            // 尝试不同的放置位置
            for (double x = 0; x <= layer.getLength() - effectiveLength; x += 10) {
                for (double y = 0; y <= layer.getWidth() - effectiveWidth; y += 10) {
                    boolean canPlace = true;

                    // 检查是否与已放置的货物重叠
                    for (PlacedItem placedItem : placedItems) {
                        if (isOverlapping(x, y, layer.getHeight(), rotatedLength, rotatedWidth, rotatedHeight,
                                placedItem.getX(), placedItem.getY(), placedItem.getZ(),
                                placedItem.getLength(), placedItem.getWidth(), placedItem.getHeight())) {
                            canPlace = false;
                            break;
                        }
                    }

                    if (canPlace) {
                        PlacedItem newItem = new PlacedItem(
                                item.getId(),
                                item.getName(),
                                rotatedLength,
                                rotatedWidth,
                                rotatedHeight,
                                x,
                                y,
                                layer.getHeight(),
                                item.getWeight()
                        );

                        layer.addPlacedItem(newItem);
                        return newItem;
                    }
                }
            }
        }

        return null;
    }

    /**
     * 检查两个物体是否重叠
     */
    private boolean isOverlapping(double x1, double y1, double z1, double l1, double w1, double h1,
                                 double x2, double y2, double z2, double l2, double w2, double h2) {
        return !(x1 + l1 <= x2 || x2 + l2 <= x1 ||
                y1 + w1 <= y2 || y2 + w2 <= y1 ||
                z1 + h1 <= z2 || z2 + h2 <= z1);
    }

    /**
     * 初始化遗传算法种群
     */
    private List<List<PlacedItem>> initializePopulation(int populationSize) {
        List<List<PlacedItem>> population = new ArrayList<>();

        // 创建初始种群
        for (int i = 0; i < populationSize; i++) {
            // 使用贪心算法生成一个解
            Space space = new Space(containerLength, containerWidth, containerHeight);
            List<PlacedItem> solution = new ArrayList<>();

            // 随机打乱货物顺序
            List<CargoItem> shuffledItems = new ArrayList<>(cargoItems);
            Collections.shuffle(shuffledItems);

            for (CargoItem item : shuffledItems) {
                PlacedItem placedItem = tryPlaceItem(space, item);
                if (placedItem != null) {
                    solution.add(placedItem);
                    space.updateAvailableSpace(placedItem);
                }
            }

            population.add(solution);
        }

        return population;
    }

    /**
     * 评估种群适应度
     */
    private List<Double> evaluatePopulation(List<List<PlacedItem>> population) {
        List<Double> fitness = new ArrayList<>();

        for (List<PlacedItem> solution : population) {
            // 计算利用率
            double totalVolume = solution.stream()
                    .mapToDouble(item -> item.getLength() * item.getWidth() * item.getHeight())
                    .sum();

            double containerVolume = containerLength * containerWidth * containerHeight;
            double utilization = totalVolume / containerVolume;

            // 计算货物数量比例
            double itemRatio = (double) solution.size() / cargoItems.size();

            // 组合适应度
            double combinedFitness = utilization * 0.7 + itemRatio * 0.3;
            fitness.add(combinedFitness);
        }

        return fitness;
    }

    /**
     * 进化种群
     */
    private List<List<PlacedItem>> evolvePopulation(List<List<PlacedItem>> population, List<Double> fitness,
                                                 double mutationRate, double crossoverRate) {
        List<List<PlacedItem>> newPopulation = new ArrayList<>();
        int populationSize = population.size();

        // 精英主义：保留最佳解
        int bestIndex = 0;
        for (int i = 1; i < fitness.size(); i++) {
            if (fitness.get(i) > fitness.get(bestIndex)) {
                bestIndex = i;
            }
        }
        newPopulation.add(new ArrayList<>(population.get(bestIndex)));

        // 生成新种群
        while (newPopulation.size() < populationSize) {
            // 选择两个父代
            int parent1Index = selectParent(fitness);
            int parent2Index = selectParent(fitness);

            List<PlacedItem> parent1 = population.get(parent1Index);
            List<PlacedItem> parent2 = population.get(parent2Index);

            // 交叉
            List<PlacedItem> child;
            if (Math.random() < crossoverRate) {
                child = crossover(parent1, parent2);
            } else {
                child = new ArrayList<>(Math.random() < 0.5 ? parent1 : parent2);
            }

            // 变异
            if (Math.random() < mutationRate) {
                mutate(child);
            }

            // 修复解（确保没有重叠）
            child = repairSolution(child);

            newPopulation.add(child);
        }

        return newPopulation;
    }

    /**
     * 选择父代（轮盘赛选择）
     */
    private int selectParent(List<Double> fitness) {
        double totalFitness = fitness.stream().mapToDouble(Double::doubleValue).sum();
        double randomValue = Math.random() * totalFitness;

        double sum = 0;
        for (int i = 0; i < fitness.size(); i++) {
            sum += fitness.get(i);
            if (sum >= randomValue) {
                return i;
            }
        }

        return fitness.size() - 1;
    }

    /**
     * 交叉操作
     */
    private List<PlacedItem> crossover(List<PlacedItem> parent1, List<PlacedItem> parent2) {
        List<PlacedItem> child = new ArrayList<>();

        // 随机选择交叉点
        int crossoverPoint = (int) (Math.random() * Math.min(parent1.size(), parent2.size()));

        // 从第一个父代获取前半部分
        for (int i = 0; i < crossoverPoint; i++) {
            child.add(parent1.get(i));
        }

        // 从第二个父代获取后半部分（避免重复）
        for (PlacedItem item : parent2) {
            boolean isDuplicate = false;
            for (PlacedItem childItem : child) {
                if (item.getId().equals(childItem.getId())) {
                    isDuplicate = true;
                    break;
                }
            }

            if (!isDuplicate) {
                child.add(item);
            }
        }

        return child;
    }

    /**
     * 变异操作
     */
    private void mutate(List<PlacedItem> solution) {
        if (solution.isEmpty()) {
            return;
        }

        // 随机选择一个货物进行变异
        int index = (int) (Math.random() * solution.size());
        PlacedItem item = solution.get(index);

        // 随机调整位置
        double newX = Math.max(0, Math.min(containerLength - item.getLength(), item.getX() + (Math.random() - 0.5) * 100));
        double newY = Math.max(0, Math.min(containerWidth - item.getWidth(), item.getY() + (Math.random() - 0.5) * 100));
        double newZ = Math.max(0, Math.min(containerHeight - item.getHeight(), item.getZ() + (Math.random() - 0.5) * 100));

        // 创建新的变异货物
        PlacedItem mutatedItem = new PlacedItem(
                item.getId(),
                item.getName(),
                item.getLength(),
                item.getWidth(),
                item.getHeight(),
                newX,
                newY,
                newZ,
                item.getWeight()
        );

        solution.set(index, mutatedItem);
    }

    /**
     * 修复解（移除重叠的货物）
     */
    private List<PlacedItem> repairSolution(List<PlacedItem> solution) {
        List<PlacedItem> repairedSolution = new ArrayList<>();

        for (PlacedItem item : solution) {
            boolean overlapping = false;

            // 检查是否超出集装箱边界
            if (item.getX() < 0 || item.getX() + item.getLength() > containerLength ||
                    item.getY() < 0 || item.getY() + item.getWidth() > containerWidth ||
                    item.getZ() < 0 || item.getZ() + item.getHeight() > containerHeight) {
                overlapping = true;
            }

            // 检查是否与已修复的解中的货物重叠
            if (!overlapping) {
                for (PlacedItem placedItem : repairedSolution) {
                    if (isOverlapping(
                            item.getX(), item.getY(), item.getZ(),
                            item.getLength(), item.getWidth(), item.getHeight(),
                            placedItem.getX(), placedItem.getY(), placedItem.getZ(),
                            placedItem.getLength(), placedItem.getWidth(), placedItem.getHeight())) {
                        overlapping = true;
                        break;
                    }
                }
            }

            if (!overlapping) {
                repairedSolution.add(item);
            }
        }

        return repairedSolution;
    }

    /**
     * 获取装载可视化数据
     *
     * @return 可视化数据
     */
    public Kv getVisualizationData() {
        List<Kv> itemsData = new ArrayList<>();

        for (PlacedItem item : placedItems) {
            Kv itemData = Kv.create();
            itemData.set("id", item.getId());
            itemData.set("name", item.getName());
            itemData.set("length", item.getLength());
            itemData.set("width", item.getWidth());
            itemData.set("height", item.getHeight());
            itemData.set("x", item.getX());
            itemData.set("y", item.getY());
            itemData.set("z", item.getZ());

            // 随机颜色，用于可视化
            String color = generateRandomColor();
            itemData.set("color", color);

            itemsData.add(itemData);
        }

        Kv containerData = Kv.create();
        containerData.set("length", containerLength);
        containerData.set("width", containerWidth);
        containerData.set("height", containerHeight);

        // 添加未装载的货物信息
        List<Kv> unplacedItemsData = new ArrayList<>();
        for (CargoItem item : unplacedItems) {
            Kv itemData = Kv.create();
            itemData.set("id", item.getId());
            itemData.set("name", item.getName());
            itemData.set("length", item.getLength());
            itemData.set("width", item.getWidth());
            itemData.set("height", item.getHeight());
            itemData.set("weight", item.getWeight());

            // 随机颜色，用于可视化
            String color = generateRandomColor();
            itemData.set("color", color);

            unplacedItemsData.add(itemData);
        }

        Kv result = Kv.create();
        result.set("container", containerData);
        result.set("items", itemsData);
        result.set("unplacedItems", unplacedItemsData); // 添加未装载的货物
        result.set("utilization", volumeUtilization);
        result.set("canFit", canFit);
        result.set("message", message);

        return result;
    }

    /**
     * 获取装载优化建议
     *
     * @return 优化建议
     */
    public Kv getOptimizationSuggestions() {
        Kv result = Kv.create();
        List<String> suggestions = new ArrayList<>();

        // 基本建议
        suggestions.add("按体积从大到小装载货物，可以提高空间利用率");
        suggestions.add("重物放底部，轻物放顶部，可以提高稳定性");
        suggestions.add("相同尺寸的货物放在一起，可以减少空隙");

        // 根据利用率给出建议
        if (volumeUtilization < 60) {
            suggestions.add("当前空间利用率较低，建议考虑使用更小的集装箱或合并货物");
        } else if (volumeUtilization > 85) {
            suggestions.add("当前空间利用率较高，装载可能较为紧凑，建议预留更多间隙以便操作");
        }

        // 如果无法装入所有货物
        if (!canFit) {
            suggestions.add("无法装入所有货物，建议考虑使用更大的集装箱或分批装运");
        }

        result.set("suggestions", suggestions);
        return result;
    }

    /**
     * 生成随机颜色
     *
     * @return 十六进制颜色代码
     */
    private String generateRandomColor() {
        Random random = new Random();
        int r = random.nextInt(200) + 55; // 避免太暗的颜色
        int g = random.nextInt(200) + 55;
        int b = random.nextInt(200) + 55;
        return String.format("#%02x%02x%02x", r, g, b);
    }

    public static List<CargoItem> createCargoItemsFromRecords(List<Record> records) {
        return createCargoItemsFromRecords(records, "mm");
    }

    /**
     * 从Record列表创建货物列表
     *
     * @param records Record列表
     * @return 货物列表
     */
    public static List<CargoItem> createCargoItemsFromRecords(List<Record> records, String unit) {
        List<CargoItem> cargoItems = new ArrayList<>();

        for (Record record : records) {
            try {
                double length = Double.parseDouble(record.getStr("sizea"));
                double width = Double.parseDouble(record.getStr("sizeb"));
                double height = Double.parseDouble(record.getStr("sizec"));
                if ("cm".equalsIgnoreCase(unit)) {
                    length *= 10;
                    width *= 10;
                    height *= 10;
                } else if ("inch".equalsIgnoreCase(unit)) {
                    length *= 25.4;
                    width *= 25.4;
                    height *= 25.4;
                } else if ("m".equalsIgnoreCase(unit)) {
                    length *= 1000;
                    width *= 1000;
                    height *= 1000;
                }
                double weight = 0;
                if (record.getStr("jz") != null) {
                    weight = Double.parseDouble(record.getStr("jz"));
                }

                String name = record.getStr("id");

                CargoItem item = new CargoItem(
                        record.getStr("id"),
                        name,
                        length,
                        width,
                        height,
                        weight
                );

                cargoItems.add(item);
            } catch (NumberFormatException e) {
                // 忽略无效的尺寸数据
            }
        }

        return cargoItems;
    }

    // Getter方法
    public boolean canFit() {
        return canFit;
    }

    public List<PlacedItem> getPlacedItems() {
        return placedItems;
    }

    /**
     * 获取未装载的货物
     * @return 未装载的货物列表
     */
    public List<CargoItem> getUnplacedItems() {
        return unplacedItems;
    }

    public double getVolumeUtilization() {
        return volumeUtilization;
    }

    public String getMessage() {
        return message;
    }

    /**
     * 货物项
     */
    public static class CargoItem {
        private final String id;
        private final String name;
        private final double length;
        private final double width;
        private final double height;
        private final double weight;

        public CargoItem(String id, String name, double length, double width, double height, double weight) {
            this.id = id;
            this.name = name;
            this.length = length;
            this.width = width;
            this.height = height;
            this.weight = weight;
        }

        public String getId() {
            return id;
        }

        public String getName() {
            return name;
        }

        public double getLength() {
            return length;
        }

        public double getWidth() {
            return width;
        }

        public double getHeight() {
            return height;
        }

        public double getWeight() {
            return weight;
        }

        public double getVolume() {
            return length * width * height;
        }
    }

    /**
     * 模拟结果
     */
    private static class SimulationResult {
        private final List<PlacedItem> placedItems;
        private final double utilization;
        private final boolean canFit;

        public SimulationResult(List<PlacedItem> placedItems, double utilization, boolean canFit) {
            this.placedItems = placedItems;
            this.utilization = utilization;
            this.canFit = canFit;
        }

        public List<PlacedItem> getPlacedItems() {
            return placedItems;
        }

        public double getUtilization() {
            return utilization;
        }

        public boolean isCanFit() {
            return canFit;
        }
    }

    /**
     * 层结构，用于层叠装箱算法
     */
    private static class Layer {
        private final double height;
        private final double length;
        private final double width;
        private final List<PlacedItem> placedItems = new ArrayList<>();

        public Layer(double height, double length, double width) {
            this.height = height;
            this.length = length;
            this.width = width;
        }

        public double getHeight() {
            return height;
        }

        public double getLength() {
            return length;
        }

        public double getWidth() {
            return width;
        }

        public List<PlacedItem> getPlacedItems() {
            return placedItems;
        }

        public void addPlacedItem(PlacedItem item) {
            placedItems.add(item);
        }
    }

    /**
     * 极大空间类，用于极大空间算法
     */
    private static class MaximalSpace {
        private final List<AvailableSpace> maximalSpaces = new ArrayList<>();

        public MaximalSpace(double containerLength, double containerWidth, double containerHeight) {
            // 初始化为整个集装箱空间
            maximalSpaces.add(new AvailableSpace(0, 0, 0, containerLength, containerWidth, containerHeight));
        }

        public List<AvailableSpace> getMaximalSpaces() {
            return maximalSpaces;
        }

        /**
         * 更新极大空间
         */
        public void updateAvailableSpace(PlacedItem placedItem) {
            // 移除被放置货物覆盖的空间
            List<AvailableSpace> overlappingSpaces = new ArrayList<>();
            List<AvailableSpace> nonOverlappingSpaces = new ArrayList<>();

            for (AvailableSpace space : maximalSpaces) {
                if (isOverlapping(space, placedItem)) {
                    overlappingSpaces.add(space);
                } else {
                    nonOverlappingSpaces.add(space);
                }
            }

            // 为每个被覆盖的空间创建新的极大空间
            List<AvailableSpace> newSpaces = new ArrayList<>();
            for (AvailableSpace space : overlappingSpaces) {
                generateNewMaximalSpaces(space, placedItem, newSpaces);
            }

            // 合并所有空间
            maximalSpaces.clear();
            maximalSpaces.addAll(nonOverlappingSpaces);
            maximalSpaces.addAll(newSpaces);

            // 移除被其他空间完全包含的空间
            removeRedundantSpaces();

            // 按体积从大到小排序
            maximalSpaces.sort((a, b) -> Double.compare(
                    b.getLength() * b.getWidth() * b.getHeight(),
                    a.getLength() * a.getWidth() * a.getHeight()
            ));
        }

        /**
         * 检查空间和货物是否重叠
         */
        private boolean isOverlapping(AvailableSpace space, PlacedItem item) {
            return !(item.getX() >= space.getX() + space.getLength() ||
                    item.getX() + item.getLength() <= space.getX() ||
                    item.getY() >= space.getY() + space.getWidth() ||
                    item.getY() + item.getWidth() <= space.getY() ||
                    item.getZ() >= space.getZ() + space.getHeight() ||
                    item.getZ() + item.getHeight() <= space.getZ());
        }

        /**
         * 生成新的极大空间
         */
        private void generateNewMaximalSpaces(AvailableSpace space, PlacedItem item, List<AvailableSpace> newSpaces) {
            // 生成六个方向的新空间
            // 1. 右侧空间
            if (space.getX() + space.getLength() > item.getX() + item.getLength()) {
                newSpaces.add(new AvailableSpace(
                        item.getX() + item.getLength(),
                        space.getY(),
                        space.getZ(),
                        space.getX() + space.getLength() - (item.getX() + item.getLength()),
                        space.getWidth(),
                        space.getHeight()
                ));
            }

            // 2. 左侧空间
            if (item.getX() > space.getX()) {
                newSpaces.add(new AvailableSpace(
                        space.getX(),
                        space.getY(),
                        space.getZ(),
                        item.getX() - space.getX(),
                        space.getWidth(),
                        space.getHeight()
                ));
            }

            // 3. 前侧空间
            if (space.getY() + space.getWidth() > item.getY() + item.getWidth()) {
                newSpaces.add(new AvailableSpace(
                        space.getX(),
                        item.getY() + item.getWidth(),
                        space.getZ(),
                        space.getLength(),
                        space.getY() + space.getWidth() - (item.getY() + item.getWidth()),
                        space.getHeight()
                ));
            }

            // 4. 后侧空间
            if (item.getY() > space.getY()) {
                newSpaces.add(new AvailableSpace(
                        space.getX(),
                        space.getY(),
                        space.getZ(),
                        space.getLength(),
                        item.getY() - space.getY(),
                        space.getHeight()
                ));
            }

            // 5. 上方空间
            if (space.getZ() + space.getHeight() > item.getZ() + item.getHeight()) {
                newSpaces.add(new AvailableSpace(
                        space.getX(),
                        space.getY(),
                        item.getZ() + item.getHeight(),
                        space.getLength(),
                        space.getWidth(),
                        space.getZ() + space.getHeight() - (item.getZ() + item.getHeight())
                ));
            }

            // 6. 下方空间
            if (item.getZ() > space.getZ()) {
                newSpaces.add(new AvailableSpace(
                        space.getX(),
                        space.getY(),
                        space.getZ(),
                        space.getLength(),
                        space.getWidth(),
                        item.getZ() - space.getZ()
                ));
            }
        }

        /**
         * 移除冗余空间
         */
        private void removeRedundantSpaces() {
            List<AvailableSpace> spacesToRemove = new ArrayList<>();

            for (int i = 0; i < maximalSpaces.size(); i++) {
                for (int j = 0; j < maximalSpaces.size(); j++) {
                    if (i != j && !spacesToRemove.contains(maximalSpaces.get(i)) &&
                            isContained(maximalSpaces.get(i), maximalSpaces.get(j))) {
                        spacesToRemove.add(maximalSpaces.get(i));
                        break;
                    }
                }
            }

            maximalSpaces.removeAll(spacesToRemove);
        }

        /**
         * 检查空间a是否被空间b完全包含
         */
        private boolean isContained(AvailableSpace a, AvailableSpace b) {
            return a.getX() >= b.getX() &&
                   a.getY() >= b.getY() &&
                   a.getZ() >= b.getZ() &&
                   a.getX() + a.getLength() <= b.getX() + b.getLength() &&
                   a.getY() + a.getWidth() <= b.getY() + b.getWidth() &&
                   a.getZ() + a.getHeight() <= b.getZ() + b.getHeight();
        }
    }

    /**
     * 已放置的货物
     */
    public static class PlacedItem extends CargoItem {
        private final double x;
        private final double y;
        private final double z;

        public PlacedItem(String id, String name, double length, double width, double height,
                          double x, double y, double z, double weight) {
            super(id, name, length, width, height, weight);
            this.x = x;
            this.y = y;
            this.z = z;
        }

        public double getX() {
            return x;
        }

        public double getY() {
            return y;
        }

        public double getZ() {
            return z;
        }
    }

    /**
     * 可用空间管理
     */
    private static class Space {
        private final List<AvailableSpace> availableSpaces = new ArrayList<>();

        public Space(double containerLength, double containerWidth, double containerHeight) {
            // 初始化为整个集装箱空间
            availableSpaces.add(new AvailableSpace(0, 0, 0, containerLength, containerWidth, containerHeight));
        }

        public List<AvailableSpace> getAvailableSpaces() {
            return availableSpaces;
        }

        /**
         * 更新可用空间
         *
         * @param placedItem 已放置的货物
         */
        public void updateAvailableSpace(PlacedItem placedItem) {
            // 找到放置货物的空间
            AvailableSpace usedSpace = null;
            for (AvailableSpace space : availableSpaces) {
                if (space.getX() == placedItem.getX() &&
                        space.getY() == placedItem.getY() &&
                        space.getZ() == placedItem.getZ()) {
                    usedSpace = space;
                    break;
                }
            }

            if (usedSpace == null) {
                return;
            }

            // 移除已使用的空间
            availableSpaces.remove(usedSpace);

            // 创建新的可用空间（分割）
            // 1. 右侧空间
            if (usedSpace.getX() + placedItem.getLength() < usedSpace.getX() + usedSpace.getLength()) {
                availableSpaces.add(new AvailableSpace(
                        usedSpace.getX() + placedItem.getLength(),
                        usedSpace.getY(),
                        usedSpace.getZ(),
                        usedSpace.getLength() - placedItem.getLength(),
                        usedSpace.getWidth(),
                        usedSpace.getHeight()
                ));
            }

            // 2. 前侧空间
            if (usedSpace.getY() + placedItem.getWidth() < usedSpace.getY() + usedSpace.getWidth()) {
                availableSpaces.add(new AvailableSpace(
                        usedSpace.getX(),
                        usedSpace.getY() + placedItem.getWidth(),
                        usedSpace.getZ(),
                        placedItem.getLength(),
                        usedSpace.getWidth() - placedItem.getWidth(),
                        usedSpace.getHeight()
                ));
            }

            // 3. 上方空间
            if (usedSpace.getZ() + placedItem.getHeight() < usedSpace.getZ() + usedSpace.getHeight()) {
                availableSpaces.add(new AvailableSpace(
                        usedSpace.getX(),
                        usedSpace.getY(),
                        usedSpace.getZ() + placedItem.getHeight(),
                        placedItem.getLength(),
                        placedItem.getWidth(),
                        usedSpace.getHeight() - placedItem.getHeight()
                ));
            }

            // 按体积从大到小排序可用空间
            availableSpaces.sort((a, b) -> Double.compare(
                    b.getLength() * b.getWidth() * b.getHeight(),
                    a.getLength() * a.getWidth() * a.getHeight()
            ));
        }
    }

    /**
     * 可用空间
     */
    private static class AvailableSpace {
        private final double x;
        private final double y;
        private final double z;
        private final double length;
        private final double width;
        private final double height;

        public AvailableSpace(double x, double y, double z, double length, double width, double height) {
            this.x = x;
            this.y = y;
            this.z = z;
            this.length = length;
            this.width = width;
            this.height = height;
        }

        public double getX() {
            return x;
        }

        public double getY() {
            return y;
        }

        public double getZ() {
            return z;
        }

        public double getLength() {
            return length;
        }

        public double getWidth() {
            return width;
        }

        public double getHeight() {
            return height;
        }
    }
}
