package com.theolympiastone.club.common.kit;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.List;
import java.util.Map;

public class DataStringMapListener<T> extends AnalysisEventListener<T> {

    private final List<Map<String, String>> list = Lists.newArrayList();
    private final List<String> keyList;

    public DataStringMapListener(List<String> keyList) {
        this.keyList = keyList;
    }

    public List<Map<String, String>> getList() {
        return list;
    }

    @Override
    public void invoke(T data, AnalysisContext context) {
        Map<String, String> map = Maps.newHashMap();
        for (int i = 0; i < keyList.size(); i++) {
            map.put(keyList.get(i), ((Map<Integer, String>) data).get(i));
        }
        list.add(map);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {

    }
}
