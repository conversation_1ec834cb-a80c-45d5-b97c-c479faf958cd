package com.theolympiastone.club.common.kit;

import com.arronlong.httpclientutil.HttpClientUtil;
import com.arronlong.httpclientutil.common.HttpConfig;
import com.arronlong.httpclientutil.exception.HttpProcessException;
import com.google.common.collect.Maps;
import com.google.gson.Gson;

import java.text.DecimalFormat;
import java.util.List;
import java.util.Map;

public class HlJson {
    /**
     * reason : 查询成功
     * result : {"update":"2020-08-27 17:25:26","list":[["美元","100","687.11","681.52","690.02","690.02"],["瑞典克朗","100","78.63","76.21","79.64","79.27"],["卢布","100","9.13","8.57","9.56","9.21"],["新西兰元","100","455.02","440.98","464.52","458.22"],["韩元","100","0.5786","0.5582","0.6046","0.5832"],["日元","100","6.47","6.269","6.5277","6.5176"],["港币","100","88.64","87.94","89","89"],["英镑","100","906.12","877.96","916.83","912.79"],["欧元","100","811.78","786.56","820.4","817.76"],["瑞士法郎","100","755.25","731.94","763.81","760.55"],["加拿大元","100","521.58","505.12","527.75","525.43"],["澳大利亚元","100","497.45","482","503.33","501.11"]]}
     * error_code : 0
     */

    private String reason;
    /**
     * update : 2020-08-27 17:25:26
     * list : [["美元","100","687.11","681.52","690.02","690.02"],["瑞典克朗","100","78.63","76.21","79.64","79.27"],["卢布","100","9.13","8.57","9.56","9.21"],["新西兰元","100","455.02","440.98","464.52","458.22"],["韩元","100","0.5786","0.5582","0.6046","0.5832"],["日元","100","6.47","6.269","6.5277","6.5176"],["港币","100","88.64","87.94","89","89"],["英镑","100","906.12","877.96","916.83","912.79"],["欧元","100","811.78","786.56","820.4","817.76"],["瑞士法郎","100","755.25","731.94","763.81","760.55"],["加拿大元","100","521.58","505.12","527.75","525.43"],["澳大利亚元","100","497.45","482","503.33","501.11"]]
     */

    private ResultBean result;
    private int error_code;

    public static void main(String[] args) throws HttpProcessException {
        String url = "http://op.juhe.cn/onebox/exchange/query?key=********************************";
        HttpConfig httpConfig = HttpsKit.httpConfig.url(url);
        String resp = HttpClientUtil.get(httpConfig);
        HlJson hlJson = new Gson().fromJson(resp, HlJson.class);
        System.out.println(resp);
        Map<String, Double> hlMap = Maps.newHashMap();
        if (0 == hlJson.getError_code()) {
            for (List<String> strings : hlJson.getResult().getList()) {
                hlMap.put(strings.get(0), Double.parseDouble(strings.get(2)));
            }
            DecimalFormat df = new DecimalFormat("0.000");
            System.out.println(df.format(hlMap.get("美元") * 0.97 / 100));
            System.out.println(df.format(hlMap.get("美元") / hlMap.get("欧元")));
            System.out.println(df.format(hlMap.get("美元") / hlMap.get("澳大利亚元")));
            System.out.println(df.format(hlMap.get("美元") / hlMap.get("英镑")));
        }
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public ResultBean getResult() {
        return result;
    }

    public void setResult(ResultBean result) {
        this.result = result;
    }

    public int getError_code() {
        return error_code;
    }

    public void setError_code(int error_code) {
        this.error_code = error_code;
    }

    public static class ResultBean {
        private String update;
        private List<List<String>> list;

        public String getUpdate() {
            return update;
        }

        public void setUpdate(String update) {
            this.update = update;
        }

        public List<List<String>> getList() {
            return list;
        }

        public void setList(List<List<String>> list) {
            this.list = list;
        }
    }
}
