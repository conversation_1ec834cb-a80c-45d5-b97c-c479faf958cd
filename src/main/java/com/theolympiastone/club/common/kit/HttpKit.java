package com.theolympiastone.club.common.kit;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.arronlong.httpclientutil.HttpClientUtil;
import com.arronlong.httpclientutil.common.HttpConfig;
import com.arronlong.httpclientutil.common.HttpHeader;
import com.arronlong.httpclientutil.exception.HttpProcessException;
import com.google.common.collect.Maps;
import org.apache.http.Header;
import org.apache.http.HttpResponse;
import org.apache.http.util.EntityUtils;

import java.io.FileOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class HttpKit {
    public static void download(String src, String fileName) throws IOException {
        try (FileOutputStream outputStream = new FileOutputStream(fileName)) {
            try {
                HttpConfig httpConfig = HttpConfig.custom().url(src).out(outputStream);
                HttpClientUtil.down(httpConfig);
            } catch (HttpProcessException e) {
                e.printStackTrace();
            }
        }
    }

    public static void main(String[] args) throws HttpProcessException, InterruptedException {
        String text = getMeiriyiyan();
        wechatWork(text);
    }

    public static void wechatWork(String everyDayContent) throws HttpProcessException, InterruptedException {
        HttpConfig httpConfig = HttpConfig.custom();
        String content = HttpClientUtil.get(httpConfig.url("https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=ww632d8b0dcd2c0119&corpsecret=A6L6s3A4GvSJ9Qjb98aJH9VM_EMWIQXPwBbrkvSSsg4"));
        JSONObject contentJson = JSON.parseObject(content);
        String access_token = contentJson.getString("access_token");
//        String access_token = "zlXCHyBRSrOriQO17KilUYckLXgEXxehGqJlbjwa3jfsOcIjv7Murpe1UzEliVmFE_JZQtnXL4idEnamxHtAMOIAx7ltz4qTV3d5CpKNMGSsS-UZkSSdeAlK-PTfJgiiXeednvHPkRHuRXsvpro0M_Zm2HJJdOzqaEeOWRAmmQgE-6BDrWEPCj-0x0OyYGR5Yg30TKdyUzKBu_heTOgC2w";
        System.out.println("access_token: " + access_token);

        String[] files = new String[1];
//        files[0] = "/root/OIP-C.jpeg";
        files[0] = "/Users/<USER>/Pictures/OIP-C.jpeg";
        Header[] headers = HttpHeader.custom()
                .other("Content-Type", "multipart/form-data")
                .other("filename", "挖沙")
                .other("filelength", "28554")
                .build();
        Map<String, Object> map = Maps.newHashMap();
        map.put("Content-Type", "multipart/form-data");
        map.put("filename", "挖沙");
        map.put("filelength", "28554");
        String fileContent = HttpClientUtil.post(HttpsKit.httpConfig.url("https://qyapi.weixin.qq.com/cgi-bin/media/upload?access_token=" + access_token + "&type=image")
                .files(files)
                .headers(headers)
                .map(map)
        );
        String media_id = JSON.parseObject(fileContent).getString("media_id");
//        String media_id = "3fUyX14W-FrhLnGOjkyQXY09dVT7s3wSGSV70NxbSpnjmxpIh30cEopAd1aD0dpp_";
        System.out.println("media_id: " + media_id);

        httpConfig = HttpConfig.custom();
        String text = "{" +
//                "            \"touser\":\"LiuYangBin|WuZhiFang|GaiHaoYiQiYong\"," +
                "            \"touser\":\"LiuYangBin|WuZhiFang\"," +
                "            \"agentid\":\"1000002\"," +
                "            \"msgtype\":\"text\"," +
                "            \"text\":{" +
                "                \"content\":\"挖沙了，铁子们\\n" + everyDayContent + "\"" +
                "            }," +
                "            \"duplicate_check_interval\":500" +
                "        }";
        System.out.println(text);
        String textContent = HttpClientUtil.post(HttpsKit.httpConfig.url("https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token=" + access_token).json(text));
        System.out.println("textContent: " + textContent);

        String pictureJson = "{" +
//                "            \"touser\":\"LiuYangBin|WuZhiFang|GaiHaoYiQiYong\"," +
                "            \"touser\":\"LiuYangBin|WuZhiFang\"," +
                "            \"agentid\":\"1000002\"," +
                "            \"msgtype\":\"image\"," +
                "            \"image\":{" +
                "                \"media_id\":\"" + media_id + "\"" +
                "            }," +
                "            \"safe\":0," +
                "            \"enable_duplicate_check\":0," +
                "            \"duplicate_check_interval\":1800" +
                "        }";
        String pictureContent = HttpClientUtil.post(HttpsKit.httpConfig.url("https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token=" + access_token).json(pictureJson));
        System.out.println("pictureContent: " + pictureContent);
    }

    public static String getMeiriyiyan() throws HttpProcessException {
        //http://guozhivip.com/yy/api/api.php
        HttpConfig httpConfig = HttpConfig.custom().url("http://guozhivip.com/yy/api/api.php");
        String content = HttpClientUtil.get(httpConfig);
        content = content.replace("document.write('", "").replace("');", "");
        content = content.replace("document.write(\"", "").replace("\");", "");
        content = content.replace("\"", "'");
        System.out.println(content);
        return content;
    }

    public static void getWeather() throws HttpProcessException {
        String host = "http://saweather.market.alicloudapi.com";
        String path = "/hour24";
        String appcode = "b5510050b0104fdeabed973f8f9cf9a2";
        Map<String, String> headers = new HashMap<>();
        //最后在header中的格式(中间是英文空格)为Authorization:APPCODE 83359fd73fe94948385f570e3c139105
        headers.put("Authorization", "APPCODE " + appcode);
        Map<String, String> querys = new HashMap<>();
        querys.put("areaid", "101230205");
        try {
            /**
             * 重要提示如下:
             * HttpUtils请从
             * https://github.com/aliyun/api-gateway-demo-sign-java/blob/master/src/main/java/com/aliyun/api/gateway/demo/util/HttpUtils.java
             * 下载
             *
             * 相应的依赖请参照
             * https://github.com/aliyun/api-gateway-demo-sign-java/blob/master/pom.xml
             */
            HttpResponse response = HttpUtils.doGet(host, path, headers, querys);
//            System.out.println(response.toString());
            //获取response的body
            String weatherContent = EntityUtils.toString(response.getEntity());
            System.out.println(weatherContent);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
