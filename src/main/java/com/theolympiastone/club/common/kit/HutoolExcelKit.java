package com.theolympiastone.club.common.kit;

import cn.hutool.poi.excel.ExcelWriter;
import org.apache.poi.ss.usermodel.ClientAnchor;
import org.apache.poi.ss.usermodel.Drawing;
import org.apache.poi.ss.usermodel.Sheet;

public class HutoolExcelKit {
    public static void writePic(ExcelWriter writer, int col1, int row1, int col2, int row2, byte[] pictureData, int picType) {
        Sheet sheet = writer.getSheet();
        Drawing<?> drawingPatriarch = sheet.createDrawingPatriarch();
        ClientAnchor anchor = drawingPatriarch.createAnchor(0, 0, 0, 0, col1, row1, col2, row2);
        anchor.setAnchorType(ClientAnchor.AnchorType.MOVE_AND_RESIZE);
        int pictureIndex = sheet.getWorkbook().addPicture(pictureData, picType);
        drawingPatriarch.createPicture(anchor, pictureIndex);
    }

}
