package com.theolympiastone.club.common.kit;

import com.google.common.collect.Lists;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.List;

public class SimpleTree {
    private String title;
    private int pid;
    private SimpleTree parent;
    private int id;
    private String href;
    private String bts;
    private List<SimpleTree> children;


    public SimpleTree(String title, int id) {
        this.title = title;
        this.id = id;
        this.href = "";
        this.children = Lists.newArrayList();
    }

    public SimpleTree(String title, int id, int pid) {
        this.title = title;
        this.id = id;
        this.pid = pid;
        this.href = "";
        this.children = Lists.newArrayList();
    }

    public SimpleTree(String title, int id, String wl, String bts) {
        this.title = title;
        this.id = id;
        this.bts = bts;
        StringBuilder href = new StringBuilder(wl);
        if (!StringUtils.isEmpty(bts)) {
            String[] split = bts.split("/");
            for (String s : split) {
                try {
                    href.append("/").append(URLEncoder.encode(s, "UTF-8").replace("%2F", "/").replace("+", "%20"));
                } catch (UnsupportedEncodingException e) {
                    throw new RuntimeException(e);
                }
            }
        }
        this.href = href.toString();
        this.children = Lists.newArrayList();
    }

    public SimpleTree(String title, int id, String href) {
        this.title = title;
        this.id = id;
        this.href = href;
        this.children = Lists.newArrayList();
    }

    public SimpleTree(String title, int id, List<SimpleTree> children) {
        this.title = title;
        this.id = id;
        this.href = "";
        this.children = children;
    }


    public SimpleTree(String title, int id, String href, List<SimpleTree> children) {
        this.title = title;
        this.id = id;
        this.href = href;
        this.children = children;
    }

    public static void main(String[] args) {
        SimpleTree tree6 = new SimpleTree("六级", 1, "http://www.六级.com", "");
        SimpleTree tree5 = new SimpleTree("五级", 1, "http://www.五级.com", Lists.newArrayList(tree6));
        SimpleTree tree4 = new SimpleTree("四级", 1, "http://www.四级.com", Lists.newArrayList(tree5));
        SimpleTree tree3 = new SimpleTree("三级", 1, "http://www.三级.com", Lists.newArrayList(tree4));
        SimpleTree tree2 = new SimpleTree("二级", 1, "http://www.二级.com", Lists.newArrayList(tree3));
        SimpleTree tree1 = new SimpleTree("一级", 1, "http://www.一级.com", Lists.newArrayList(tree2));
        System.out.println(new Gson().toJson(tree1));
    }

    public String getBts() {
        return bts;
    }

    public void setBts(String bts) {
        this.bts = bts;
    }

    public int getPid() {
        return pid;
    }

    public void setPid(int pid) {
        this.pid = pid;
    }

    public SimpleTree getParent() {
        return parent;
    }

    public void setParent(SimpleTree parent) {
        this.parent = parent;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getHref() {
        return href;
    }

    public void setHref(String href) {
        this.href = href;
    }

    public List<SimpleTree> getChildren() {
        return children;
    }

    public void setChildren(List<SimpleTree> children) {
        this.children = children;
    }

    public SimpleTree addChild(SimpleTree tree) {
        List<SimpleTree> children = this.getChildren();
        children.add(tree);
        return this;
    }
}
