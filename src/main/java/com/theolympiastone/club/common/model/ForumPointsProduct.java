package com.theolympiastone.club.common.model;

import com.jfinal.plugin.activerecord.Page;
import com.theolympiastone.club.common.model.base.BaseForumPointsProduct;

import java.util.List;

/**
 * Generated by JFinal.
 */
@SuppressWarnings("serial")
public class ForumPointsProduct extends BaseForumPointsProduct<ForumPointsProduct> {
    public static final ForumPointsProduct dao = new ForumPointsProduct().dao();

    /**
     * 商品类型枚举
     */
    public static final String TYPE_VIRTUAL = "VIRTUAL";   // 虚拟商品
    public static final String TYPE_PHYSICAL = "PHYSICAL"; // 实物商品

    /**
     * 获取所有上架的商品
     */
    public List<ForumPointsProduct> getAllOnSale() {
        return dao.find("select * from forum_points_product where status = 1 order by sort_order asc");
    }

    /**
     * 检查库存是否充足
     */
    public boolean hasEnoughStock() {
        return getInt("stock") > 0;
    }

    /**
     * 检查用户是否达到兑换限制
     */
    public boolean checkExchangeLimit(Long userId) {
        int limit = getInt("exchange_limit");
        if (limit == 0) {
            return true;
        }

        // 查询用户已兑换次数
        long exchangeCount = ForumPointsOrder.dao.findFirst(
                "select count(*) as cnt from forum_points_order where user_id = ? and product_id = ? and status != 3",
                userId, getId()
        ).getLong("cnt");

        return exchangeCount < limit;
    }

    /**
     * 扣减库存
     */
    public boolean reduceStock() {
        int rows = dao.findFirst(
                "update forum_points_product set stock = stock - 1 where id = ? and stock > 0",
                getId()
        ).getInt("rows");
        return rows > 0;
    }

    /**
     * 恢复库存(订单取消时)
     */
    public boolean restoreStock() {
        int rows = dao.findFirst(
                "update forum_points_product set stock = stock + 1 where id = ?",
                getId()
        ).getInt("rows");
        return rows > 0;
    }

    /**
     * 是否是虚拟商品
     */
    public boolean isVirtualProduct() {
        return TYPE_VIRTUAL.equals(getProductType());
    }

    /**
     * 是否是实物商品
     */
    public boolean isPhysicalProduct() {
        return TYPE_PHYSICAL.equals(getProductType());
    }

    /**
     * 分页查询商品列表
     */
    public Page<ForumPointsProduct> getProducts(int pageNumber, int pageSize) {
        return dao.paginate(pageNumber, pageSize,
                "select *",
                "from forum_points_product where status = 1 order by created_time desc");
    }
}

