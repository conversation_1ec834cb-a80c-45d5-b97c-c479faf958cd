package com.theolympiastone.club.common.model;

import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import com.theolympiastone.club.common.model.base.BaseForumReply;

import java.util.Date;
import java.util.List;

/**
 * Generated by JFinal.
 */
@SuppressWarnings("serial")
public class ForumReply extends BaseForumReply<ForumReply> {
    public static final ForumReply dao = new ForumReply().dao();

    // 获取回复人信息（考虑匿名）
    public String getReplierName(Integer userId) {
        if (getBoolean("anonymous")) {
            return "匿名用户";
        }
        // 获取用户信息
        if (userId != null) {
            Record user = Db.findFirst("select * from account where id=?", userId);
            if (user != null) {
                return user.getStr("xm");
            }
        }
        return "未知用户";
    }

    // 获取回复时间的友好显示
    public String getFriendlyCreatedTime() {
        Date createdAt = getDate("created_at");
        if (createdAt == null) {
            return "";
        }
        long diff = System.currentTimeMillis() - createdAt.getTime();
        long minutes = diff / (60 * 1000);
        long hours = minutes / 60;
        long days = hours / 24;

        if (minutes < 1) {
            return "刚刚";
        } else if (minutes < 60) {
            return minutes + "分钟前";
        } else if (hours < 24) {
            return hours + "小时前";
        } else if (days < 30) {
            return days + "天前";
        } else {
            return new java.text.SimpleDateFormat("yyyy-MM-dd").format(createdAt);
        }
    }

    // 获取子回复列表
    public List<ForumReply> getChildReplies() {
        return dao.find("select * from forum_reply where parent_id = ? and status = 1 order by created_at asc", getInt("id"));
    }

    // 检查用户是否可以编辑回复
    public boolean canEdit(Integer userId) {
        return userId != null && userId.equals(getInt("user_id"));
    }

    // 检查是否是最佳答案
    public boolean isBestAnswer() {
        return getBoolean("is_best");
    }

    // 获取用户的真实ID（即使是匿名回复）
    public Integer getRealUserId() {
        return getInt("user_id");
    }

    /**
     * 检查用户是否已经投过票
     * @param userId 用户ID
     * @param voteType 投票类型 1:赞 2:踩
     * @return 是否已投票
     */
    public boolean hasVoted(Object userId, int voteType) {
        if (userId == null) {
            return false;
        }
        return ForumVote.dao.findFirst(
            "SELECT * FROM forum_vote WHERE reply_id = ? AND user_id = ? AND vote_type = ?",
            getLong("id"), userId, voteType
        ) != null;
    }
}

