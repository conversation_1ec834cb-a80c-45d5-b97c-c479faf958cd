package com.theolympiastone.club.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by JFinal, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseCgcp<M extends BaseCgcp<M>> extends Model<M> implements IBean {

	public void setId(java.lang.Integer id) {
		set("id", id);
	}
	
	public java.lang.Integer getId() {
		return getInt("id");
	}
	
	public void setMc(java.lang.String mc) {
		set("mc", mc);
	}
	
	public java.lang.String getMc() {
		return getStr("mc");
	}
	
	public void setWj(java.lang.String wj) {
		set("wj", wj);
	}
	
	public java.lang.String getWj() {
		return getStr("wj");
	}
	
	public void setBz(java.lang.String bz) {
		set("bz", bz);
	}
	
	public java.lang.String getBz() {
		return getStr("bz");
	}
	
}

