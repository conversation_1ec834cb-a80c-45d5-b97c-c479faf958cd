package com.theolympiastone.club.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by JFinal, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseDdlc<M extends BaseDdlc<M>> extends Model<M> implements IBean {

	public void setId(java.lang.Integer id) {
		set("id", id);
	}
	
	public java.lang.Integer getId() {
		return getInt("id");
	}
	
	public void setLcmc(java.lang.String lcmc) {
		set("lcmc", lcmc);
	}
	
	public java.lang.String getLcmc() {
		return getStr("lcmc");
	}
	
	public void setXzx(java.lang.String xzx) {
		set("xzx", xzx);
	}
	
	public java.lang.String getXzx() {
		return getStr("xzx");
	}
	
	public void setZt(java.lang.String zt) {
		set("zt", zt);
	}
	
	public java.lang.String getZt() {
		return getStr("zt");
	}
	
	public void setPx(java.lang.String px) {
		set("px", px);
	}
	
	public java.lang.String getPx() {
		return getStr("px");
	}
	
}

