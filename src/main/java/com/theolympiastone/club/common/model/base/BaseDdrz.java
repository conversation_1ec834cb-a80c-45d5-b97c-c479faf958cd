package com.theolympiastone.club.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by JFinal, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseDdrz<M extends BaseDdrz<M>> extends Model<M> implements IBean {

	public void setId(java.lang.Integer id) {
		set("id", id);
	}
	
	public java.lang.Integer getId() {
		return getInt("id");
	}
	
	public void setDdbh(java.lang.String ddbh) {
		set("ddbh", ddbh);
	}
	
	public java.lang.String getDdbh() {
		return getStr("ddbh");
	}
	
	public void setSj(java.lang.String sj) {
		set("sj", sj);
	}
	
	public java.lang.String getSj() {
		return getStr("sj");
	}
	
	public void setCzrid(java.lang.Integer czrid) {
		set("czrid", czrid);
	}
	
	public java.lang.Integer getCzrid() {
		return getInt("czrid");
	}
	
	public void setCzsj(java.lang.String czsj) {
		set("czsj", czsj);
	}
	
	public java.lang.String getCzsj() {
		return getStr("czsj");
	}
	
	public void setBz(java.lang.String bz) {
		set("bz", bz);
	}
	
	public java.lang.String getBz() {
		return getStr("bz");
	}
	
	public void setIp(java.lang.String ip) {
		set("ip", ip);
	}
	
	public java.lang.String getIp() {
		return getStr("ip");
	}
	
}

