package com.theolympiastone.club.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by JFinal, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseDpxs<M extends BaseDpxs<M>> extends Model<M> implements IBean {

	public void setId(java.lang.Integer id) {
		set("id", id);
	}
	
	public java.lang.Integer getId() {
		return getInt("id");
	}
	
	public void setBt(java.lang.String bt) {
		set("bt", bt);
	}
	
	public java.lang.String getBt() {
		return getStr("bt");
	}
	
	public void setNr(java.lang.String nr) {
		set("nr", nr);
	}
	
	public java.lang.String getNr() {
		return getStr("nr");
	}
	
	public void setZt(java.lang.String zt) {
		set("zt", zt);
	}
	
	public java.lang.String getZt() {
		return getStr("zt");
	}
	
	public void setCjr(java.lang.String cjr) {
		set("cjr", cjr);
	}
	
	public java.lang.String getCjr() {
		return getStr("cjr");
	}
	
	public void setCjsj(java.lang.String cjsj) {
		set("cjsj", cjsj);
	}
	
	public java.lang.String getCjsj() {
		return getStr("cjsj");
	}
	
	public void setPx(java.lang.String px) {
		set("px", px);
	}
	
	public java.lang.String getPx() {
		return getStr("px");
	}
	
	public void setYs(java.lang.String ys) {
		set("ys", ys);
	}
	
	public java.lang.String getYs() {
		return getStr("ys");
	}
	
	public void setSy(java.lang.String sy) {
		set("sy", sy);
	}
	
	public java.lang.String getSy() {
		return getStr("sy");
	}
	
}

