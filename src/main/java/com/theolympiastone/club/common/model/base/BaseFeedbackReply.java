package com.theolympiastone.club.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by JFinal, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseFeedbackReply<M extends BaseFeedbackReply<M>> extends Model<M> implements IBean {

	public void setId(java.lang.Integer id) {
		set("id", id);
	}
	
	public java.lang.Integer getId() {
		return getInt("id");
	}
	
	public void setFeedbackId(java.lang.Integer feedbackId) {
		set("feedbackId", feedbackId);
	}
	
	public java.lang.Integer getFeedbackId() {
		return getInt("feedbackId");
	}
	
	public void setAccountId(java.lang.Integer accountId) {
		set("accountId", accountId);
	}
	
	public java.lang.Integer getAccountId() {
		return getInt("accountId");
	}
	
	public void setContent(java.lang.String content) {
		set("content", content);
	}
	
	public java.lang.String getContent() {
		return getStr("content");
	}
	
	public void setCreateAt(java.util.Date createAt) {
		set("createAt", createAt);
	}
	
	public java.util.Date getCreateAt() {
		return getDate("createAt");
	}
	
	public void setReport(java.lang.Integer report) {
		set("report", report);
	}
	
	public java.lang.Integer getReport() {
		return getInt("report");
	}
	
}

