package com.theolympiastone.club.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by JF<PERSON>, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseForumPost<M extends BaseForumPost<M>> extends Model<M> implements IBean {

	public void setId(java.lang.Long id) {
		set("id", id);
	}
	
	public java.lang.Long getId() {
		return getLong("id");
	}
	
	public void setCategoryId(java.lang.Long categoryId) {
		set("category_id", categoryId);
	}
	
	public java.lang.Long getCategoryId() {
		return getLong("category_id");
	}
	
	public void setTitle(java.lang.String title) {
		set("title", title);
	}
	
	public java.lang.String getTitle() {
		return getStr("title");
	}
	
	public void setContent(java.lang.String content) {
		set("content", content);
	}
	
	public java.lang.String getContent() {
		return getStr("content");
	}
	
	public void setUserId(java.lang.Long userId) {
		set("user_id", userId);
	}
	
	public java.lang.Long getUserId() {
		return getLong("user_id");
	}
	
	public void setAnonymous(java.lang.Boolean anonymous) {
		set("anonymous", anonymous);
	}
	
	public java.lang.Boolean getAnonymous() {
		return get("anonymous");
	}
	
	public void setViewCount(java.lang.Integer viewCount) {
		set("view_count", viewCount);
	}
	
	public java.lang.Integer getViewCount() {
		return getInt("view_count");
	}
	
	public void setReplyCount(java.lang.Integer replyCount) {
		set("reply_count", replyCount);
	}
	
	public java.lang.Integer getReplyCount() {
		return getInt("reply_count");
	}
	
	public void setLikeCount(java.lang.Integer likeCount) {
		set("like_count", likeCount);
	}
	
	public java.lang.Integer getLikeCount() {
		return getInt("like_count");
	}
	
	public void setDislikeCount(java.lang.Integer dislikeCount) {
		set("dislike_count", dislikeCount);
	}
	
	public java.lang.Integer getDislikeCount() {
		return getInt("dislike_count");
	}
	
	public void setShareCount(java.lang.Integer shareCount) {
		set("share_count", shareCount);
	}
	
	public java.lang.Integer getShareCount() {
		return getInt("share_count");
	}
	
	public void setBestReplyId(java.lang.Long bestReplyId) {
		set("best_reply_id", bestReplyId);
	}
	
	public java.lang.Long getBestReplyId() {
		return getLong("best_reply_id");
	}
	
	public void setIsEssence(java.lang.Boolean isEssence) {
		set("is_essence", isEssence);
	}
	
	public java.lang.Boolean getIsEssence() {
		return get("is_essence");
	}
	
	public void setStatus(java.lang.Integer status) {
		set("status", status);
	}
	
	public java.lang.Integer getStatus() {
		return getInt("status");
	}
	
	public void setCreatedAt(java.util.Date createdAt) {
		set("created_at", createdAt);
	}
	
	public java.util.Date getCreatedAt() {
		return getDate("created_at");
	}
	
	public void setUpdatedAt(java.util.Date updatedAt) {
		set("updated_at", updatedAt);
	}
	
	public java.util.Date getUpdatedAt() {
		return getDate("updated_at");
	}
	
}

