package com.theolympiastone.club.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by JF<PERSON>, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseForumScoreLog<M extends BaseForumScoreLog<M>> extends Model<M> implements IBean {

	public void setId(java.lang.Long id) {
		set("id", id);
	}
	
	public java.lang.Long getId() {
		return getLong("id");
	}
	
	public void setUserId(java.lang.Long userId) {
		set("user_id", userId);
	}
	
	public java.lang.Long getUserId() {
		return getLong("user_id");
	}
	
	public void setScore(java.lang.Integer score) {
		set("score", score);
	}
	
	public java.lang.Integer getScore() {
		return getInt("score");
	}
	
	public void setRuleId(java.lang.Long ruleId) {
		set("rule_id", ruleId);
	}
	
	public java.lang.Long getRuleId() {
		return getLong("rule_id");
	}
	
	public void setPostId(java.lang.Long postId) {
		set("post_id", postId);
	}
	
	public java.lang.Long getPostId() {
		return getLong("post_id");
	}
	
	public void setReplyId(java.lang.Long replyId) {
		set("reply_id", replyId);
	}
	
	public java.lang.Long getReplyId() {
		return getLong("reply_id");
	}
	
	public void setRemark(java.lang.String remark) {
		set("remark", remark);
	}
	
	public java.lang.String getRemark() {
		return getStr("remark");
	}
	
	public void setCreatedAt(java.util.Date createdAt) {
		set("created_at", createdAt);
	}
	
	public java.util.Date getCreatedAt() {
		return getDate("created_at");
	}
	
}

