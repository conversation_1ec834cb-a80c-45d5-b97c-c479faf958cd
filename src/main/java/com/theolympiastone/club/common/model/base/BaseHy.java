package com.theolympiastone.club.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by JFinal, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseHy<M extends BaseHy<M>> extends Model<M> implements IBean {

	public void setId(java.lang.Integer id) {
		set("id", id);
	}
	
	public java.lang.Integer getId() {
		return getInt("id");
	}
	
	public void setSj(java.lang.String sj) {
		set("sj", sj);
	}
	
	public java.lang.String getSj() {
		return getStr("sj");
	}
	
	public void setBt(java.lang.String bt) {
		set("bt", bt);
	}
	
	public java.lang.String getBt() {
		return getStr("bt");
	}
	
	public void setZc(java.lang.String zc) {
		set("zc", zc);
	}
	
	public java.lang.String getZc() {
		return getStr("zc");
	}
	
	public void setRy(java.lang.String ry) {
		set("ry", ry);
	}
	
	public java.lang.String getRy() {
		return getStr("ry");
	}
	
	public void setYc(java.lang.String yc) {
		set("yc", yc);
	}
	
	public java.lang.String getYc() {
		return getStr("yc");
	}
	
	public void setJg(java.lang.String jg) {
		set("jg", jg);
	}
	
	public java.lang.String getJg() {
		return getStr("jg");
	}
	
	public void setWj(java.lang.String wj) {
		set("wj", wj);
	}
	
	public java.lang.String getWj() {
		return getStr("wj");
	}
	
	public void setYgsj(java.lang.String ygsj) {
		set("ygsj", ygsj);
	}
	
	public java.lang.String getYgsj() {
		return getStr("ygsj");
	}
	
	public void setCdr(java.lang.String cdr) {
		set("cdr", cdr);
	}
	
	public java.lang.String getCdr() {
		return getStr("cdr");
	}
	
	public void setSqr(java.lang.String sqr) {
		set("sqr", sqr);
	}
	
	public java.lang.String getSqr() {
		return getStr("sqr");
	}
	
	public void setSqsj(java.lang.String sqsj) {
		set("sqsj", sqsj);
	}
	
	public java.lang.String getSqsj() {
		return getStr("sqsj");
	}
	
}

