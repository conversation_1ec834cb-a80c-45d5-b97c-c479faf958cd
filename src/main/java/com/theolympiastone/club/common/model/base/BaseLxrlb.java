package com.theolympiastone.club.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by JFinal, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseLxrlb<M extends BaseLxrlb<M>> extends Model<M> implements IBean {

	public void setId(java.lang.Integer id) {
		set("id", id);
	}
	
	public java.lang.Integer getId() {
		return getInt("id");
	}
	
	public void setMc(java.lang.String mc) {
		set("mc", mc);
	}
	
	public java.lang.String getMc() {
		return getStr("mc");
	}
	
	public void setCjr(java.lang.String cjr) {
		set("cjr", cjr);
	}
	
	public java.lang.String getCjr() {
		return getStr("cjr");
	}
	
	public void setCjsj(java.lang.String cjsj) {
		set("cjsj", cjsj);
	}
	
	public java.lang.String getCjsj() {
		return getStr("cjsj");
	}
	
}

