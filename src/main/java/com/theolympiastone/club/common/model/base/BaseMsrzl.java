package com.theolympiastone.club.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by JFinal, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseMsrzl<M extends BaseMsrzl<M>> extends Model<M> implements IBean {

	public void setId(java.lang.Integer id) {
		set("id", id);
	}
	
	public java.lang.Integer getId() {
		return getInt("id");
	}
	
	public void setMsrid(java.lang.Integer msrid) {
		set("msrid", msrid);
	}
	
	public java.lang.Integer getMsrid() {
		return getInt("msrid");
	}
	
	public void setZlmc(java.lang.String zlmc) {
		set("zlmc", zlmc);
	}
	
	public java.lang.String getZlmc() {
		return getStr("zlmc");
	}
	
	public void setZllj(java.lang.String zllj) {
		set("zllj", zllj);
	}
	
	public java.lang.String getZllj() {
		return getStr("zllj");
	}
	
}

