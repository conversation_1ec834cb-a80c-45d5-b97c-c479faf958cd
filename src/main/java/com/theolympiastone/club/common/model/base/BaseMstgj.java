package com.theolympiastone.club.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by JFinal, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseMstgj<M extends BaseMstgj<M>> extends Model<M> implements IBean {

	public void setId(java.lang.Integer id) {
		set("id", id);
	}
	
	public java.lang.Integer getId() {
		return getInt("id");
	}
	
	public void setMc(java.lang.String mc) {
		set("mc", mc);
	}
	
	public java.lang.String getMc() {
		return getStr("mc");
	}
	
	public void setSc(java.lang.String sc) {
		set("sc", sc);
	}
	
	public java.lang.String getSc() {
		return getStr("sc");
	}
	
	public void setMyfs(java.lang.String myfs) {
		set("myfs", myfs);
	}
	
	public java.lang.String getMyfs() {
		return getStr("myfs");
	}
	
	public void setSzid(java.lang.Integer szid) {
		set("szid", szid);
	}
	
	public java.lang.Integer getSzid() {
		return getInt("szid");
	}
	
	public void setJg(java.lang.String jg) {
		set("jg", jg);
	}
	
	public java.lang.String getJg() {
		return getStr("jg");
	}
	
	public void setDw(java.lang.String dw) {
		set("dw", dw);
	}
	
	public java.lang.String getDw() {
		return getStr("dw");
	}
	
	public void setKssj(java.lang.String kssj) {
		set("kssj", kssj);
	}
	
	public java.lang.String getKssj() {
		return getStr("kssj");
	}
	
	public void setJssj(java.lang.String jssj) {
		set("jssj", jssj);
	}
	
	public java.lang.String getJssj() {
		return getStr("jssj");
	}
	
	public void setBz(java.lang.String bz) {
		set("bz", bz);
	}
	
	public java.lang.String getBz() {
		return getStr("bz");
	}
	
	public void setZt(java.lang.String zt) {
		set("zt", zt);
	}
	
	public java.lang.String getZt() {
		return getStr("zt");
	}
	
}

