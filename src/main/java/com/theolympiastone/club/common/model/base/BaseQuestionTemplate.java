package com.theolympiastone.club.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by JF<PERSON>, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseQuestionTemplate<M extends BaseQuestionTemplate<M>> extends Model<M> implements IBean {

	public void setId(java.lang.Integer id) {
		set("id", id);
	}
	
	public java.lang.Integer getId() {
		return getInt("id");
	}
	
	public void setName(java.lang.String name) {
		set("name", name);
	}
	
	public java.lang.String getName() {
		return getStr("name");
	}
	
	public void setDepartment(java.lang.String department) {
		set("department", department);
	}
	
	public java.lang.String getDepartment() {
		return getStr("department");
	}
	
	public void setCronExpression(java.lang.String cronExpression) {
		set("cron_expression", cronExpression);
	}
	
	public java.lang.String getCronExpression() {
		return getStr("cron_expression");
	}
	
	public void setStatus(java.lang.Integer status) {
		set("status", status);
	}
	
	public java.lang.Integer getStatus() {
		return getInt("status");
	}
	
	public void setCreateAt(java.lang.String createAt) {
		set("create_at", createAt);
	}
	
	public java.lang.String getCreateAt() {
		return getStr("create_at");
	}
	
	public void setUpdateAt(java.lang.String updateAt) {
		set("update_at", updateAt);
	}
	
	public java.lang.String getUpdateAt() {
		return getStr("update_at");
	}
	
}

