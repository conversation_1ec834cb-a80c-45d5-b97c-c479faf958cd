package com.theolympiastone.club.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by JFinal, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseSq<M extends BaseSq<M>> extends Model<M> implements IBean {

	public void setId(java.lang.Integer id) {
		set("id", id);
	}
	
	public java.lang.Integer getId() {
		return getInt("id");
	}
	
	public void setFzid(java.lang.Integer fzid) {
		set("fzid", fzid);
	}
	
	public java.lang.Integer getFzid() {
		return getInt("fzid");
	}
	
	public void setMc(java.lang.String mc) {
		set("mc", mc);
	}
	
	public java.lang.String getMc() {
		return getStr("mc");
	}
	
	public void setLj(java.lang.String lj) {
		set("lj", lj);
	}
	
	public java.lang.String getLj() {
		return getStr("lj");
	}
	
	public void setTp(java.lang.String tp) {
		set("tp", tp);
	}
	
	public java.lang.String getTp() {
		return getStr("tp");
	}
	
	public void setMs(java.lang.String ms) {
		set("ms", ms);
	}
	
	public java.lang.String getMs() {
		return getStr("ms");
	}
	
	public void setPx(java.lang.Integer px) {
		set("px", px);
	}
	
	public java.lang.Integer getPx() {
		return getInt("px");
	}
	
	public void setTag(java.lang.String tag) {
		set("tag", tag);
	}
	
	public java.lang.String getTag() {
		return getStr("tag");
	}
	
}

