package com.theolympiastone.club.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by JFinal, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseWcskr<M extends BaseWcskr<M>> extends Model<M> implements IBean {

	public void setId(java.lang.Integer id) {
		set("id", id);
	}
	
	public java.lang.Integer getId() {
		return getInt("id");
	}
	
	public void setSkr(java.lang.String skr) {
		set("skr", skr);
	}
	
	public java.lang.String getSkr() {
		return getStr("skr");
	}
	
	public void setKhh(java.lang.String khh) {
		set("khh", khh);
	}
	
	public java.lang.String getKhh() {
		return getStr("khh");
	}
	
	public void setKhzh(java.lang.String khzh) {
		set("khzh", khzh);
	}
	
	public java.lang.String getKhzh() {
		return getStr("khzh");
	}
	
}

