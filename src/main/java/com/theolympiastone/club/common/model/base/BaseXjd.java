package com.theolympiastone.club.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by JFinal, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseXjd<M extends BaseXjd<M>> extends Model<M> implements IBean {

	public void setId(java.lang.Integer id) {
		set("id", id);
	}
	
	public java.lang.Integer getId() {
		return getInt("id");
	}
	
	public void setDdbh(java.lang.String ddbh) {
		set("ddbh", ddbh);
	}
	
	public java.lang.String getDdbh() {
		return getStr("ddbh");
	}
	
	public void setKhid(java.lang.Integer khid) {
		set("khid", khid);
	}
	
	public java.lang.Integer getKhid() {
		return getInt("khid");
	}
	
	public void setHb(java.lang.String hb) {
		set("hb", hb);
	}
	
	public java.lang.String getHb() {
		return getStr("hb");
	}
	
	public void setWj(java.lang.String wj) {
		set("wj", wj);
	}
	
	public java.lang.String getWj() {
		return getStr("wj");
	}
	
	public void setBz(java.lang.String bz) {
		set("bz", bz);
	}
	
	public java.lang.String getBz() {
		return getStr("bz");
	}
	
	public void setLrfs(java.lang.String lrfs) {
		set("lrfs", lrfs);
	}
	
	public java.lang.String getLrfs() {
		return getStr("lrfs");
	}
	
	public void setLrsj(java.lang.String lrsj) {
		set("lrsj", lrsj);
	}
	
	public java.lang.String getLrsj() {
		return getStr("lrsj");
	}
	
	public void setLrr(java.lang.String lrr) {
		set("lrr", lrr);
	}
	
	public java.lang.String getLrr() {
		return getStr("lrr");
	}
	
	public void setCkkh(java.lang.Integer ckkh) {
		set("ckkh", ckkh);
	}
	
	public java.lang.Integer getCkkh() {
		return getInt("ckkh");
	}
	
	public void setDdje(java.lang.String ddje) {
		set("ddje", ddje);
	}
	
	public java.lang.String getDdje() {
		return getStr("ddje");
	}
	
	public void setDdjermb(java.lang.String ddjermb) {
		set("ddjermb", ddjermb);
	}
	
	public java.lang.String getDdjermb() {
		return getStr("ddjermb");
	}
	
	public void setClcb(java.lang.String clcb) {
		set("clcb", clcb);
	}
	
	public java.lang.String getClcb() {
		return getStr("clcb");
	}
	
	public void setKhzt(java.lang.String khzt) {
		set("khzt", khzt);
	}
	
	public java.lang.String getKhzt() {
		return getStr("khzt");
	}
	
	public void setHlysbj(java.lang.String hlysbj) {
		set("hlysbj", hlysbj);
	}
	
	public java.lang.String getHlysbj() {
		return getStr("hlysbj");
	}
	
	public void setJyddys(java.lang.String jyddys) {
		set("jyddys", jyddys);
	}
	
	public java.lang.String getJyddys() {
		return getStr("jyddys");
	}
	
	public void setHl(java.lang.String hl) {
		set("hl", hl);
	}
	
	public java.lang.String getHl() {
		return getStr("hl");
	}
	
	public void setTstkbz(java.lang.String tstkbz) {
		set("tstkbz", tstkbz);
	}
	
	public java.lang.String getTstkbz() {
		return getStr("tstkbz");
	}
	
	public void setKhdqgys(java.lang.String khdqgys) {
		set("khdqgys", khdqgys);
	}
	
	public java.lang.String getKhdqgys() {
		return getStr("khdqgys");
	}
	
	public void setXjly(java.lang.String xjly) {
		set("xjly", xjly);
	}
	
	public java.lang.String getXjly() {
		return getStr("xjly");
	}
	
	public void setKhszg(java.lang.String khszg) {
		set("khszg", khszg);
	}
	
	public java.lang.String getKhszg() {
		return getStr("khszg");
	}
	
	public void setKhszcs(java.lang.String khszcs) {
		set("khszcs", khszcs);
	}
	
	public java.lang.String getKhszcs() {
		return getStr("khszcs");
	}
	
	public void setFkfs(java.lang.String fkfs) {
		set("fkfs", fkfs);
	}
	
	public java.lang.String getFkfs() {
		return getStr("fkfs");
	}
	
	public void setBjrq(java.lang.String bjrq) {
		set("bjrq", bjrq);
	}
	
	public java.lang.String getBjrq() {
		return getStr("bjrq");
	}
	
	public void setBjyxq(java.lang.String bjyxq) {
		set("bjyxq", bjyxq);
	}
	
	public java.lang.String getBjyxq() {
		return getStr("bjyxq");
	}
	
	public void setYgjq(java.lang.String ygjq) {
		set("ygjq", ygjq);
	}
	
	public java.lang.String getYgjq() {
		return getStr("ygjq");
	}
	
	public void setDdujsjl(java.lang.String ddujsjl) {
		set("ddujsjl", ddujsjl);
	}
	
	public java.lang.String getDdujsjl() {
		return getStr("ddujsjl");
	}
	
	public void setJydd(java.lang.String jydd) {
		set("jydd", jydd);
	}
	
	public java.lang.String getJydd() {
		return getStr("jydd");
	}
	
	public void setZlyq(java.lang.String zlyq) {
		set("zlyq", zlyq);
	}
	
	public java.lang.String getZlyq() {
		return getStr("zlyq");
	}
	
	public void setFhd(java.lang.String fhd) {
		set("fhd", fhd);
	}
	
	public java.lang.String getFhd() {
		return getStr("fhd");
	}
	
	public void setFftk(java.lang.String fftk) {
		set("fftk", fftk);
	}
	
	public java.lang.String getFftk() {
		return getStr("fftk");
	}
	
}

