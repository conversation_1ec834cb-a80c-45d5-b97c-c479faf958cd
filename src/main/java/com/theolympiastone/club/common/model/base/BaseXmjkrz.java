package com.theolympiastone.club.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by JFinal, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseXmjkrz<M extends BaseXmjkrz<M>> extends Model<M> implements IBean {

	public void setId(java.lang.Integer id) {
		set("id", id);
	}
	
	public java.lang.Integer getId() {
		return getInt("id");
	}
	
	public void setJkid(java.lang.String jkid) {
		set("jkid", jkid);
	}
	
	public java.lang.String getJkid() {
		return getStr("jkid");
	}
	
	public void setLmid(java.lang.String lmid) {
		set("lmid", lmid);
	}
	
	public java.lang.String getLmid() {
		return getStr("lmid");
	}
	
	public void setNr(java.lang.String nr) {
		set("nr", nr);
	}
	
	public java.lang.String getNr() {
		return getStr("nr");
	}
	
	public void setJlr(java.lang.String jlr) {
		set("jlr", jlr);
	}
	
	public java.lang.String getJlr() {
		return getStr("jlr");
	}
	
	public void setJlsj(java.util.Date jlsj) {
		set("jlsj", jlsj);
	}
	
	public java.util.Date getJlsj() {
		return getDate("jlsj");
	}
	
}

