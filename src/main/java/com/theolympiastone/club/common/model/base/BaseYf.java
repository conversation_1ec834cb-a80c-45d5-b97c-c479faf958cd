package com.theolympiastone.club.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by JFinal, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseYf<M extends BaseYf<M>> extends Model<M> implements IBean {

	public void setId(java.lang.Integer id) {
		set("id", id);
	}
	
	public java.lang.Integer getId() {
		return getInt("id");
	}
	
	public void setMc(java.lang.String mc) {
		set("mc", mc);
	}
	
	public java.lang.String getMc() {
		return getStr("mc");
	}
	
	public void setHd(java.lang.String hd) {
		set("hd", hd);
	}
	
	public java.lang.String getHd() {
		return getStr("hd");
	}
	
	public void setMdg(java.lang.String mdg) {
		set("mdg", mdg);
	}
	
	public java.lang.String getMdg() {
		return getStr("mdg");
	}
	
	public void setMyxz(java.lang.String myxz) {
		set("myxz", myxz);
	}
	
	public java.lang.String getMyxz() {
		return getStr("myxz");
	}
	
	public void setLx(java.lang.String lx) {
		set("lx", lx);
	}
	
	public java.lang.String getLx() {
		return getStr("lx");
	}
	
	public void setZje(java.lang.String zje) {
		set("zje", zje);
	}
	
	public java.lang.String getZje() {
		return getStr("zje");
	}
	
}

