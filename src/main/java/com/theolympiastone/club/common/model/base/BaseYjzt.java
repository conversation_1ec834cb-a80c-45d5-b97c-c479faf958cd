package com.theolympiastone.club.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by JFinal, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseYjzt<M extends BaseYjzt<M>> extends Model<M> implements IBean {

	public void setYx(java.lang.String yx) {
		set("yx", yx);
	}
	
	public java.lang.String getYx() {
		return getStr("yx");
	}
	
	public void setZt(java.lang.String zt) {
		set("zt", zt);
	}
	
	public java.lang.String getZt() {
		return getStr("zt");
	}
	
}

