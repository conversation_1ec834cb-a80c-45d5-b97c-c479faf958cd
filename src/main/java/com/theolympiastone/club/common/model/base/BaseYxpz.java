package com.theolympiastone.club.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by JF<PERSON>, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseYxpz<M extends BaseYxpz<M>> extends Model<M> implements IBean {

	public void setId(java.lang.Integer id) {
		set("id", id);
	}
	
	public java.lang.Integer getId() {
		return getInt("id");
	}
	
	public void setYx(java.lang.String yx) {
		set("yx", yx);
	}
	
	public java.lang.String getYx() {
		return getStr("yx");
	}
	
	public void setLx(java.lang.String lx) {
		set("lx", lx);
	}
	
	public java.lang.String getLx() {
		return getStr("lx");
	}
	
	public void setFwq(java.lang.String fwq) {
		set("fwq", fwq);
	}
	
	public java.lang.String getFwq() {
		return getStr("fwq");
	}
	
	public void setDk(java.lang.String dk) {
		set("dk", dk);
	}
	
	public java.lang.String getDk() {
		return getStr("dk");
	}
	
	public void setMm(java.lang.String mm) {
		set("mm", mm);
	}
	
	public java.lang.String getMm() {
		return getStr("mm");
	}
	
	public void setYt(java.lang.String yt) {
		set("yt", yt);
	}
	
	public java.lang.String getYt() {
		return getStr("yt");
	}
	
	public void setEd(java.lang.Integer ed) {
		set("ed", ed);
	}
	
	public java.lang.Integer getEd() {
		return getInt("ed");
	}
	
	public void setDlmm(java.lang.String dlmm) {
		set("dlmm", dlmm);
	}
	
	public java.lang.String getDlmm() {
		return getStr("dlmm");
	}
	
	public void setZt(java.lang.String zt) {
		set("zt", zt);
	}
	
	public java.lang.String getZt() {
		return getStr("zt");
	}
	
}

