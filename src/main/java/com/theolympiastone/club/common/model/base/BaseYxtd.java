package com.theolympiastone.club.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by JFinal, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseYxtd<M extends BaseYxtd<M>> extends Model<M> implements IBean {

	public void setId(java.lang.Integer id) {
		set("id", id);
	}
	
	public java.lang.Integer getId() {
		return getInt("id");
	}
	
	public void setYx(java.lang.String yx) {
		set("yx", yx);
	}
	
	public java.lang.String getYx() {
		return getStr("yx");
	}
	
	public void setXm(java.lang.String xm) {
		set("xm", xm);
	}
	
	public java.lang.String getXm() {
		return getStr("xm");
	}
	
	public void setYy(java.lang.String yy) {
		set("yy", yy);
	}
	
	public java.lang.String getYy() {
		return getStr("yy");
	}
	
}

