package com.theolympiastone.club.data;

import com.alibaba.druid.util.StringUtils;
import com.google.gson.Gson;
import org.apache.commons.io.FileUtils;
import redis.clients.jedis.Jedis;

import java.io.File;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.util.Set;

public class YdtParser {
    public static void main(String[] args) throws IOException, NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        try (Jedis jedis = new Jedis()) {
            Set<String> keys = jedis.keys("us_*");
            for (String key : keys) {
                String companyName = jedis.hget(key, "companyName");
                String companyType = jedis.hget(key, "companyType");
                String lastId = jedis.hget(key, "lastId");
                System.out.println("https://ydt.onloon.net/#/data/cuntomsDetail?id=" + lastId + "&companyName=" + URLEncoder.encode(companyName, "UTF-8") + "&companyType=" + URLEncoder.encode(companyType, "UTF-8") + "&country=United%20States&isFuzzy=1&product=");
            }
            if (true) {
                return;
            }
            File file = new File("d:\\onloon-us-420212-1000.json");
            String s = FileUtils.readFileToString(file, Charset.defaultCharset());
            YdtCgs ydtCgs = new Gson().fromJson(s, YdtCgs.class);
            for (YdtCgs.DataBean.RecordsBean record : ydtCgs.getData().getRecords()) {
                Field[] declaredFields = record.getClass().getDeclaredFields();
                for (Field declaredField : declaredFields) {
                    declaredField.setAccessible(true);
                    String name = declaredField.getName();
                    String value = String.valueOf(declaredField.get(record));
                    if (!StringUtils.isEmpty(value)) {
                        jedis.hset("us_420212_" + record.getLastId(), name, value);
                    }
                }
            }
        }
    }
}
