package com.theolympiastone.club.dbutil;

import org.apache.commons.dbutils.DbUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;

/**
 * Created by LiuYB
 * date: 2019/2/25
 * time: 17:19
 */
public class EntDBUtil {
    private static final Logger logger = LoggerFactory.getLogger(EntsysDB.class);

    private static final String dirverClassName = "net.sourceforge.jtds.jdbc.Driver";

    public static Connection makeConnection(String url, String user, String password) {
        Connection conn = null;
        try {
            DbUtils.loadDriver(dirverClassName);
            conn = DriverManager.getConnection(url, user, password);
        } catch (SQLException e) {
            logger.error("Error when make connection.", e);
        }
        return conn;
    }
}
