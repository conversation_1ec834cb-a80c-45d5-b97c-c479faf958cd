package com.theolympiastone.club.kanban;

import com.alibaba.druid.util.StringUtils;
import com.dingtalk.api.response.OapiUserSimplelistResponse;
import com.jfinal.plugin.cron4j.ITask;
import com.taobao.api.ApiException;
import com.theolympiastone.club.common.OSConstants;
import com.theolympiastone.club.common.kit.DingTalkKit;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.theolympiastone.club.common.kit.DingTalkKit.getToken;

public class DingTalkUserIdsTask implements ITask {
    public static Map<String, String> usersMap;
    public static List<String> userIds;

    static {
        try {
            usersMap = getUsersMap();
            userIds = getUserIds();
        } catch (ApiException | InterruptedException e) {
            e.printStackTrace();
        }
    }

    private static Map<String, String> getUsersMap() throws ApiException, InterruptedException {
        String token = getToken();
        List<OapiUserSimplelistResponse.Userlist> userList = DingTalkKit.getUserList(token);
        return userList.stream().collect(Collectors.toMap(OapiUserSimplelistResponse.Userlist::getUserid, OapiUserSimplelistResponse.Userlist::getName));
    }

    private static List<String> getUserIds() throws ApiException, InterruptedException {
        String token = getToken();
        List<OapiUserSimplelistResponse.Userlist> userList = DingTalkKit.getUserList(token);
        return userList.stream().map(OapiUserSimplelistResponse.Userlist::getUserid).collect(Collectors.toList());
    }

    @Override
    public void run() {
        try {
            if (StringUtils.isEmpty(OSConstants.p.get("DingTalk"))) {
                usersMap = getUsersMap();
                userIds = getUserIds();
            }
        } catch (ApiException | InterruptedException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void stop() {

    }
}
