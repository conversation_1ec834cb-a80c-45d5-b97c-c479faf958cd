package com.theolympiastone.club.my.cgd;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jfinal.aop.Before;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import com.jfinal.upload.UploadFile;
import com.theolympiastone.club.common.controller.BaseController;
import com.theolympiastone.club.common.interceptor.FrontAuthInterceptor;
import com.theolympiastone.club.common.kit.DataMapListener;
import com.theolympiastone.club.common.model.Cgd;
import com.theolympiastone.club.common.model.Cgdmx;
import com.theolympiastone.club.common.model.Kc;
import com.theolympiastone.club.common.model.Kcdmx;
import com.theolympiastone.club.my.dd.ExportDdData;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

import static com.theolympiastone.club.common.kit.CollectionKit.toJsonEscapeApostrophe;
import static com.theolympiastone.club.common.kit.ControllerKit.genFile;
import static com.theolympiastone.club.common.kit.StringKit.*;
import static com.theolympiastone.club.my.dd.DdmxLikeExportData.toMx;

@Before({FrontAuthInterceptor.class})
public class MyCgdController extends BaseController {
    private static final List<Map<String, String>> pxList = Lists.newArrayList();

    static {
        pxList.add(ImmutableMap.of("name", "按录入逆序", "value", " order by id desc"));
        pxList.add(ImmutableMap.of("name", "按录入顺序", "value", " order by id asc"));
    }

    @Inject
    MyCgdService srv;

    public void index() {
        keepPara();
        String query = getPara("query", "");
        String queryGcdd = getPara("queryGcdd", "");
        String queryKhdd = getPara("queryKhdd", "");
        String queryPm = getPara("queryPm", "");
        String querySz = getPara("querySz", "");
        String queryLx = getPara("queryLx", "");
        String queryLjg = getPara("queryLjg", "");
        String khid = getPara("khid", "");
        String queryJgfs = getPara("queryJgfs", "");
        String queryBzbz = getPara("queryBzbz", "");
        Date today = new Date();
        String queryLrKsrq = getPara("queryLrKsrq", plusDay(today, -366));
        String queryLrJsrq = getPara("queryLrJsrq");
        String queryCqKsrq = getPara("queryCqKsrq");
        String queryCqJsrq = getPara("queryCqJsrq");
        setAttr("query", query);
        setAttr("queryGcdd", queryGcdd);
        setAttr("queryKhdd", queryKhdd);
        setAttr("queryPm", queryPm);
        setAttr("querySz", querySz);
        setAttr("queryLx", queryLx);
        setAttr("queryLjg", queryLjg);
        setAttr("queryJgfs", queryJgfs);
        setAttr("queryBzbz", queryBzbz);
        setAttr("khid", khid);
        setAttr("queryLrKsrq", queryLrKsrq);
        setAttr("queryLrJsrq", queryLrJsrq);
        setAttr("queryCqKsrq", queryCqKsrq);
        setAttr("queryCqJsrq", queryCqJsrq);
        Page<Record> ddPage;
        setAttr("khList", Db.find("select id,jc from kh order by jc"));
        ddPage = srv.paginate(getParaToInt("p", 1), query, khid, queryGcdd, queryKhdd, queryPm, querySz, queryLx, queryJgfs, queryBzbz, queryLrKsrq, queryLrJsrq, queryCqKsrq, queryCqJsrq, queryLjg);
        setAttr("page", ddPage);
        render("index.html");
    }

    public void add() {
        keepPara();
        setAttr("dataObject", "[{}]");
        setAttr("isAdd", true);
        render("add_edit.html");
    }

    @Before(MyCgdValidator.class)
    public void save() {
        try {
            String data = getPara("data");
            Cgd cgd = getModel(Cgd.class);
            if (cgd.getId() == null) {
                cgd.setLrfs("录入");
            }
            if (cgd.getId() == null) {
                cgd.save();
            } else {
                cgd.update();
            }
            List<Cgdmx> cgdmxList = srv.findCgdmxList(cgd.getCgdh());
            Set<Integer> idList = Sets.newHashSet();
            for (Cgdmx cgdmx : cgdmxList) {
                idList.add(cgdmx.getId());
            }
            List<ExportDdData> dataList = JSON.parseArray(data, ExportDdData.class);

            List<Record> szList = Db.find("select ywmc, mc from cl where (qyzt<>'禁用' or qyzt is null) order by mc");
            List<Record> pmList = Db.find("select ywmc, mc from zl order by mc");
            List<Record> jgfsList = Db.find("select ywmc, mc from jgfs order by mc");
            Map<String, String> szMap = Maps.newHashMap();
            Map<String, String> pmMap = Maps.newHashMap();
            Map<String, String> jgfsMap = Maps.newHashMap();

            for (Record record : szList) {
                szMap.put(record.get("mc"), record.get("ywmc"));
            }

            for (Record record : pmList) {
                pmMap.put(record.get("mc"), record.get("ywmc"));
            }
            for (Record record : jgfsList) {
                jgfsMap.put(record.get("mc"), record.get("ywmc"));
            }
            List<Cgdmx> ddmxSaveList = Lists.newArrayList();
            List<Cgdmx> ddmxModifyList = Lists.newArrayList();
            for (ExportDdData exportDdData : dataList) {
                String gcdd = exportDdData.getGcdd();
                if (StringUtils.isEmpty(gcdd) || (!StringUtils.isEmpty(gcdd) && "订单总计".equalsIgnoreCase(gcdd))) {
                    continue;
                }
                Cgdmx ddmx = new Cgdmx();
                ddmx = (Cgdmx) toMx(exportDdData, ddmx);
                ddmx.setCgdh(cgd.getCgdh());
                ddmx.setDdbh(cgd.getDdbh());
                ddmx.setYwszm(szMap.get(exportDdData.getZwszm()));
                ddmx.setYwpm(pmMap.get(exportDdData.getZwpm()));
                if (StringUtils.isEmpty(exportDdData.getYwjgff())) {
                    ddmx.setYwjgff(jgfsMap.get(exportDdData.getZwjgff()));
                }
//                ddmx.removeNullValueAttrs();
                Integer id = ddmx.getId();
                if (id != null) {
                    if (ddmxModifyList.isEmpty()) {
                        addAllAttrs(ddmx);
                    }
                    idList.remove(id);
                    ddmxModifyList.add(ddmx);
                } else {
                    if (ddmxSaveList.isEmpty()) {
                        addAllAttrs(ddmx);
                    }
                    ddmxSaveList.add(ddmx);
                }
            }

            Db.batchSave(ddmxSaveList, ddmxSaveList.size());
            Db.batchUpdate(ddmxModifyList, ddmxModifyList.size());
            if (!idList.isEmpty()) {
                Db.delete("delete from cgdmx where id in (" + StringUtils.join(idList, ",") + ")");
            }
            renderJson(Ret.ok().set("cgdh", cgd.getCgdh()).set("msg", "保存成功!").set("id", cgd.getId()));
        } catch (Exception e) {
            renderJson(Ret.fail().set("msg", e.getMessage()));
            e.printStackTrace();
        }

    }

    public void edit() {
        keepPara();
        Integer id = getParaToInt("id");
        String cgdh = getPara("cgdh");
        String px = getPara("px", "按工程名称顺序");
        setAttr("px", px);
        Cgd cgd;
        if (id != null) {
            cgd = srv.findDdById(id);
        } else if (!StringUtils.isEmpty(cgdh)) {
            cgd = srv.findDdByCgdh(cgdh);
        } else {
            renderJson("查不到该采购单号。联系老柳。<EMAIL>");
            return;
        }
        String jsonEscapeApostrophe = toJsonEscapeApostrophe(srv.findRecordList(cgd.getCgdh()));
        setAttr("ddmxListJson", jsonEscapeApostrophe);
        setAttr("cgd", cgd);
        setAttr("isAdd", false);
        List<Record> szList = Db.find("select c.mc,c.ywmc from cl c where (c.qyzt<>'禁用' or c.qyzt is null) order by c.mc");
        setAttr("szList", szList);
        setAttr("szListJson", toJsonEscapeApostrophe(szList));
        List<Record> pmList = Db.find("select mc,ywmc from zl order by mc");
        setAttr("pmList", pmList);
        setAttr("pmListJson", toJsonEscapeApostrophe(pmList));
        List<Record> jgfsList = Db.find("select mc,ywmc from jgfs order by ifnull(if(bh='', 999999, bh),999999)*1 asc");
        setAttr("jgfsList", jgfsList);
        String jgfsListJson = toJsonEscapeApostrophe(jgfsList);
        setAttr("jgfsListJson", jgfsListJson);

        render("add_edit.html");
    }

    @Before(MyCgdValidator.class)
    public void update() {
        Cgd cgd = getBean(Cgd.class, true);
        Ret ret = srv.update(cgd);
        renderJson(ret.set("msg", "更新成功!").set("id", cgd.getId()));
    }

    public void delete() {
        srv.delete(getParaToInt("id"));
        renderJson(Ret.ok().set("msg", "删除成功!"));
    }

    public void uploadFile() {
        UploadFile file = getFile();
        DataMapListener<Map<Integer, Object>> dataListener = new DataMapListener<>(Lists.newArrayList(
                "ddbh", "kh", "scs", "hb", "wj", "bz", "lrfs", "lrsj", "scsj", "chsj", "lrr", "ckkh", "lx", "hl", "gxsl", "mdg", "po", "gclx", "cgje", "jsje", "jsqk"
        ));
        List<List<String>> head = new ArrayList<>();
        EasyExcel.read(file.getFile(), dataListener).head(head).sheet(0).doRead();
        List<Map<String, Object>> list = dataListener.getList();
        List<Cgd> cgdList = Lists.newArrayList();
        for (Map<String, Object> map : list) {
            Cgd cgd = new Cgd();
            cgd._setOrPut(map);
            cgdList.add(cgd);
        }
        Db.batchSave(cgdList, 100);
        renderJson(Ret.ok().set("msg", "导入成功!"));
    }

    public void export() {
        String downloadFile = genFile("采购单", "select * from cgd order by id");
        redirect(downloadFile);
    }

    public void rc() throws Exception {
        String cgdh = get("cgdh");
        Record r = Db.findFirst("select * from kc where ckbh=?", cgdh);
        if (r != null) {
            renderJson(Ret.ok("这个采购单已经入仓过了").set("id", r.getInt("id")));
            return;
        }
        Integer ck = getInt("ck");
        Cgd cgd = srv.findDdByCgdh(cgdh);
        Kc kc = new Kc();
        setModelA(kc, cgd);
        kc.setCkbh(cgd.getCgdh());
        kc.setCk(ck);
        kc.setLrsj(yyyy_MM_dd());
        kc.save();
        List<Cgdmx> cgdmxList = srv.findCgdmxList(cgdh);
        List<Kcdmx> kcdmxList = Lists.newArrayList();
        for (Cgdmx cgdmx : cgdmxList) {
            Kcdmx kcdmx = new Kcdmx();
            setModelA(kcdmx, cgdmx);
            kcdmx.setCkbh(kc.getCkbh());
            kcdmxList.add(kcdmx);
        }
        Db.batchSave(kcdmxList, kcdmxList.size());
        renderJson(Ret.ok("入仓成功").set("id", kc.getId()));
    }

    public void removeRow() {
        String id = getPara("id");
        if (!StringUtils.isEmpty(id)) {
            Db.update("delete from cgdmx where id in " + id);
        }
        renderJson(Ret.ok().set("msg", "删除成功！"));
    }

}
