package com.theolympiastone.club.my.cgd;

import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import com.theolympiastone.club.common.model.Cgd;
import com.theolympiastone.club.common.model.Cgdmx;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

public class MyCgdService {
    public static final MyCgdService me = new MyCgdService();
    private final Cgd dao = new Cgd().dao();
    private final Cgdmx mxDao = new Cgdmx().dao();

    public Page<Record> paginate(int pageNum, String query, String khid, String queryGcdd, String queryKhdd, String queryPm, String querySz, String queryLx, String queryJgfs, String queryBzbz, String queryLrKsrq, String queryLrJsrq, String queryCqKsrq, String queryCqJsrq, String queryLjg) {
        String gcdd = StringUtils.isEmpty(queryGcdd) ? "" : " and a.ddbh in (select distinct ddbh from cgdmx where gcdd like '%" + queryGcdd + "%') ";
        String khdd = StringUtils.isEmpty(queryKhdd) ? "" : " and a.ddbh in (select distinct ddbh from cgdmx where khddh like '%" + queryKhdd + "%') ";
        String pmSql = StringUtils.isEmpty(queryPm) ? "" : " and a.ddbh in (select distinct ddbh from cgdmx where concat(ifnull(zwpm, ''), ifnull(ywpm, ''), ifnull(khpm, '')) like '%" + queryPm + "%') ";
        String jgfsSql = StringUtils.isEmpty(queryJgfs) ? "" : " and a.ddbh in (select distinct ddbh from cgdmx where concat(ifnull(ywjgff, ''), ifnull(zwjgff, ''), ifnull(khjg, '')) like '%" + queryJgfs + "%') ";
        String szSql = StringUtils.isEmpty(querySz) ? "" : " and a.ddbh in (select distinct ddbh from cgdmx where concat(ifnull(zwszm, ''), ifnull(ywszm, ''), ifnull(khsz, '')) like '%" + querySz + "%') ";
        String bzbzSql = StringUtils.isEmpty(queryBzbz) ? "" : " and a.ddbh in (select distinct ddbh from cgdmx where bzbz like '%" + queryBzbz + "%') ";
        String lxSql = StringUtils.isEmpty(queryLx) ? "" : " and a.lx='" + queryLx + "'";
        String ljgSql = "";
        if (!StringUtils.isEmpty(queryLjg)) {
            switch (queryLjg) {
                case "含零价格工程": {
                    ljgSql = " and a.ddbh in (select distinct ddbh from cgdmx where abs(mhzj)=0) ";
                    break;
                }
                case "含零价格工程有索赔": {
                    ljgSql = " and a.ddbh in (select distinct ddbh from cgdmx where abs(mhzj)=0) and (a.ddbh in (select ifnull(jkid, '') ddbh from sp) or a.ddbh in (select ifnull(dzddbh,'') from sp)) ";
                    break;
                }
                case "含零价格工程无索赔": {
                    ljgSql = " and a.ddbh in (select distinct ddbh from cgdmx where abs(mhzj)=0) and a.ddbh not in (select ifnull(jkid, '') ddbh from sp) and a.ddbh not in (select ifnull(dzddbh,'') from sp) ";
                    break;
                }
                default:
                    break;
            }
        }
        boolean sjBool = StringUtils.isEmpty(query) && StringUtils.isEmpty(queryGcdd) && StringUtils.isEmpty(queryKhdd) && StringUtils.isEmpty(queryPm) && StringUtils.isEmpty(querySz) && StringUtils.isEmpty(queryJgfs) && StringUtils.isEmpty(queryBzbz) && StringUtils.isEmpty(queryLjg);
        String queryLrKsrqSql = !StringUtils.isEmpty(queryLrKsrq) && sjBool ? " and a.lrsj>='" + queryLrKsrq + "'" : "";
        String queryLrJsrqSql = !StringUtils.isEmpty(queryLrJsrq) && sjBool ? " and a.lrsj<='" + queryLrJsrq + "'" : "";
        String queryCqKsrqSql = !StringUtils.isEmpty(queryCqKsrq) && sjBool ? " and a.cq>='" + queryCqKsrq + "'" : "";
        String queryCqJsrqSql = !StringUtils.isEmpty(queryCqJsrq) && sjBool ? " and a.cq<='" + queryCqJsrq + "'" : "";
        String whereSql = gcdd + khdd + pmSql + szSql + jgfsSql + bzbzSql + lxSql + ljgSql + queryLrKsrqSql + queryLrJsrqSql + queryCqKsrqSql + queryCqJsrqSql;
        if (StringUtils.isEmpty(khid)) {
            return Db.paginate(pageNum, 100, "select a.*,c.zcs zcs, c.zzl zzl, c.zje zje, c.count_gcdd from cgd a left join kh b on a.kh = b.id left join (select ddbh, round(sum(cs), 2) zcs, round(sum(zl), 2) zzl, CONVERT(round(sum(mhzj), 2), char) zje, COUNT(DISTINCT gcdd) AS count_gcdd from cgdmx group by ddbh) c on a.ddbh = c.ddbh where a.ddbh like '%" + query + "%' " + whereSql + " order by a.id desc ", "");
        } else {
            String select = "select a.*,c.zcs zcs, c.zzl zzl, c.zje zje, c.count_gcdd from cgd a left join kh b on a.kh = b.id left join (select ddbh, round(sum(cs), 2) zcs, round(sum(zl), 2) zzl, CONVERT(round(sum(mhzj), 2), char) zje, COUNT(DISTINCT gcdd) AS count_gcdd from cgdmx group by ddbh) c on a.ddbh = c.ddbh where a.kh=" + khid + " and a.ddbh like '%" + query + "%' " + whereSql + " order by a.id desc ";
            return Db.paginate(pageNum, 100, select, "");
        }
    }

    public Ret save(Cgd cgd) {
        cgd.save();
        return Ret.ok("msg", "创建成功");
    }

    public Cgd findById(Integer id) {
        return dao.findById(id);
    }

    public Ret update(Cgd cgd) {
        cgd.update();
        return Ret.ok("msg", "修改成功");
    }

    public Ret delete(Integer id) {
        Cgd cgd = findById(id);
        String cgdh = cgd.getCgdh();
        cgd.delete();
        Db.update("delete from cgdmx where cgdh=?", cgdh);
        return Ret.ok("msg", "删除成功");
    }

    public Cgd findDdById(Integer id) {
        return dao.findById(id);
    }

    public Cgd findDdByCgdh(String cgdh) {
        return dao.findFirst("select * from cgd where cgdh=?", cgdh);
    }


    public List<Cgdmx> findCgdmxList(String cgdh) {
        return mxDao.find("select * from cgdmx where cgdh=? order by gcdd,khddh,zwszm,id", cgdh);
    }

    public List<Record> findRecordList(String cgdh) {
        return Db.find("select * from cgdmx where cgdh=? order by gcdd,khddh,zwszm,id", cgdh);
    }
}
