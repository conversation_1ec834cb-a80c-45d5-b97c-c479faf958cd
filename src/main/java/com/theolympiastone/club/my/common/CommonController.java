package com.theolympiastone.club.my.common;

import com.alibaba.excel.EasyExcel;
import com.google.common.collect.Lists;
import com.jfinal.aop.Before;
import com.jfinal.aop.Inject;
import com.jfinal.kit.LogKit;
import com.jfinal.kit.PathKit;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import com.jfinal.upload.UploadFile;
import com.theolympiastone.club._admin.cl.ClAdminService;
import com.theolympiastone.club._admin.jgfs.JgfsAdminService;
import com.theolympiastone.club._admin.zl.ZlAdminService;
import com.theolympiastone.club.common.OSConstants;
import com.theolympiastone.club.common.controller.BaseController;
import com.theolympiastone.club.common.excel.ClExcel;
import com.theolympiastone.club.common.interceptor.FrontAuthInterceptor;
import com.theolympiastone.club.common.kit.ControllerKit;
import com.theolympiastone.club.common.model.Account;
import com.theolympiastone.club.common.model.Cl;
import com.theolympiastone.club.common.model.Jgfs;
import com.theolympiastone.club.common.model.Zl;
import com.twilio.Twilio;
import com.twilio.rest.api.v2010.account.Call;
import com.twilio.rest.api.v2010.account.Recording;
import com.twilio.type.PhoneNumber;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.net.MalformedURLException;
import java.net.URI;
import java.net.URL;
import java.util.List;

import static com.theolympiastone.club.common.kit.StringKit.trueString;
import static com.theolympiastone.club.common.kit.StringKit.yyyy_MM_dd;

@Before({FrontAuthInterceptor.class})
public class CommonController extends BaseController {
    @Inject
    ClAdminService srv;
    @Inject
    ZlAdminService zlSrv;
    @Inject
    JgfsAdminService jgfsSrv;

    static {
        Twilio.init("USba91ef6b11f7c4d58e537d9921eaf2e8", "87c68631a8e33af2c4df15aa44ee2cad");
    }

    public void cl() {
        Account loginAccount = getLoginAccount();
        String bq = loginAccount.getBq();
        String query = get("query", "");
        String queryYs = get("queryYs", "");
        String querySc = get("querySc", "");
        String queryBz = get("queryBz", "");
        keepPara();
        String sqlSelect = "select * from cl where (zt is null or zt='启用') ";
        String sqlOrder = " order by if(isnull(xh) or xh='',999999,xh+0),mc";
        if (!StringUtils.isEmpty(query)) {
            sqlSelect += " and (mc like '%" + query + "%' or ywmc like '%" + query + "%') ";
        }
        if (!StringUtils.isEmpty(queryYs)) {
            sqlSelect += " and (ys like '%" + queryYs + "%' or ywys like '%" + queryYs + "%') ";
        }
        if (!StringUtils.isEmpty(querySc)) {
            sqlSelect += " and rmsc like '%" + querySc + "%' ";
        }
        if (!StringUtils.isEmpty(queryBz)) {
            sqlSelect += " and bz like '%" + queryBz + "%' ";
        }
        String sql = sqlSelect + sqlOrder;
        System.out.println(sql);
        setAttr("clList", Db.find(sql));
        setAttr("wjList", Db.find("select * from download where descr='clwj' order by fileName"));
        render("cl.html");
    }

    public void khcl() {
        setAttr("tx", Db.queryStr("select sz from jcsj where id='szzstx' limit 1"));
        setAttr("clList", Db.find(
                "select lpad((@i:=@i+1), 3, 0) pm,c.* from cl c,(select @i:=0) t order by if(isnull(c.xh) or c.xh='',999999,c.xh+0),c.mc"));
        render("khcl.html");
    }

    public void delectClwj() {
        Db.delete("delete from download where id=?", get("id"));
        renderJson(Ret.ok("msg", "删除成功!"));
    }

    public void deleteCltp() {
        try {
            Integer id = getParaToInt("id");
            String wj = getPara("wj");
            Cl cl = srv.findById(id);
            String clWj = cl.getWj();
            cl.setWj(StringUtils.replace(clWj, wj + ";", ""));
            cl.update();
            renderJson(Ret.ok().set("msg", "删除成功！手动刷新界面!").set("id", id));
        } catch (Exception e) {
            renderJson(Ret.fail().set("msg", e.getMessage()));
            e.printStackTrace();
        }
    }

    public void pm() {
        String q = get("q", "");
        setAttr("list", Db.find("select * from zl where mc like '%" + q + "%' or ywmc like '%" + q + "%' or bz like '%"
                + q + "%' order by ifnull(if(bh='', 999999, bh),999999)*1 asc"));
        render("pm.html");
    }

    public void updatePmBz() {
        Integer id = getInt("id");
        String bz = get("bz", "");
        if (id == null) {
            renderJson(Ret.fail().set("msg", "没有取到ID"));
            return;
        }
        Zl zl = zlSrv.findById(id);
        zl.setBz(bz);
        if (zl.update()) {
            renderJson(Ret.ok().set("msg", "更新成功!").set("id", zl.getId()));
        } else {
            renderJson(Ret.fail().set("msg", "id: " + id + "更新失败"));
        }
    }

    public void jgfs() {
        String q = get("q", "");
        setAttr("list", Db.find("select * from jgfs where mc like '%" + q + "%' or ywmc like '%" + q
                + "%' or bz like '%" + q + "%' order by ifnull(if(bh='', 999999, bh),999999)*1 asc"));
        render("jgfs.html");
    }

    public void uploadJgfstp() {
        List<UploadFile> files = getFiles();
        Integer id = getInt("id");
        Jgfs jgfs = jgfsSrv.findById(id);
        jgfs.setWj(ControllerKit.upLoadFiles(files, "jgfs", String.valueOf(jgfs.getId()), jgfs.getWj()));
        jgfs.update();
        renderJson(Ret.ok().set("msg", "上传成功!").set("id", id));
    }

    public void deleteJgfstp() {
        try {
            Integer id = getParaToInt("id");
            String wj = getPara("wj");
            Jgfs jgfs = jgfsSrv.findById(id);
            String jgfsWj = jgfs.getWj();
            jgfs.setWj(StringUtils.replace(jgfsWj, wj + ";", ""));
            jgfs.update();
            renderJson(Ret.ok().set("msg", "删除成功！手动刷新界面!").set("id", id));
        } catch (Exception e) {
            renderJson(Ret.fail().set("msg", e.getMessage()));
            e.printStackTrace();
        }
    }

    public void jgbz() {
        String q = get("q", "");
        setAttr("list", Db.find("select * from jgbz where mc like '%" + q + "%' or ywmc like '%" + q
                + "%' or dwmc like '%" + q + "%' order by zwmc"));
        render("jgbz.html");
    }

    public void exportCl() {
        String ids = get("ids");
        List<Record> records;
        if (StringUtils.isEmpty(ids)) {
            records = Db.find("select * from cl order by if(isnull(xh) or xh='',999999,xh+0),mc");
        } else {
            records = Db
                    .find("select * from cl where id in (" + ids + ") order by if(isnull(xh) or xh='',999999,xh+0),mc");
        }
        List<ClExcel> clExcels = Lists.newArrayList();
        for (int i = 0; i < records.size(); i++) {
            Record record = records.get(i);
            String wj = trueString(record.getStr("wj"));
            String[] split = wj.split(";");
            ClExcel clExcel = new ClExcel();
            if (!StringUtils.isEmpty(wj) && split.length > 0) {
                for (int j = 0; j < split.length && j < 6; j++) {
                    try {
                        // 判断文件是否存在
                        File file = new File(PathKit.getWebRootPath() + "/upload/cl/" + record.getStr("id") + "/thumb_" + split[j]);
                        if (!file.exists()) {
                            continue; // 文件不存在，跳过当前循环
                        }

                        URL url = new URL(OSConstants.url + "upload/cl/" + record.getStr("id") + "/thumb_" + split[j]);
                        switch (j) {
                            case 0:
                                clExcel.setUrl(url);
                                break;
                            case 1:
                                clExcel.setUrl1(url);
                                break;
                            case 2:
                                clExcel.setUrl2(url);
                                break;
                            case 3:
                                clExcel.setUrl3(url);
                                break;
                            case 4:
                                clExcel.setUrl4(url);
                                break;
                            case 5:
                                clExcel.setUrl5(url);
                                break;
                            default:
                                break;
                        }
                    } catch (MalformedURLException e) {
                        e.printStackTrace();
                    }
                }
            }
            clExcel.setMc(trueString(record.getStr("mc")));
            clExcel.setYwmc(trueString(record.getStr("ywmc")));
            clExcel.setBzm(trueString(record.getStr("bz")));
            clExcel.setYs(trueString(record.getStr("ys")));
            clExcel.setYwys(trueString(record.getStr("ywys")));
            clExcel.setPx(String.format("%03d", (i + 1)));
            clExcels.add(clExcel);
        }
        String wj = PathKit.getWebRootPath() + "/upload/cl_" + yyyy_MM_dd() + ".xls";
        EasyExcel.write(wj, ClExcel.class).sheet().doWrite(clExcels);
        renderFile(new File(wj));
    }

    public void uploadClFile() {
        List<UploadFile> files = getFiles();
        if (files == null || files.isEmpty()) {
            renderJson(Ret.fail().set("msg", "上传失败!"));
        }
        String clwj = ControllerKit.upLoadFiles(files, "clwj", "");
        List<String> sqlList = Lists.newArrayList();
        for (String s : clwj.split(";")) {
            int dotIndex = s.lastIndexOf(".");
            String suffix = s.substring(dotIndex + 1);
            sqlList.add("insert into download (fileName, descr, fileType, path) values ('" + s + "', 'clwj', '" + suffix
                    + "', '/upload/clwj/" + s + "');");
        }
        Db.batch(sqlList, sqlList.size());
        renderJson(Ret.ok().set("msg", "上传成功!"));
    }

    public void uploadCltp() {
        List<UploadFile> files = getFiles();
        Integer id = getInt("id");
        Cl cl = srv.findById(id);
        cl.setWj(ControllerKit.upLoadFiles(files, "cl", String.valueOf(cl.getId()), cl.getWj()));
        cl.update();
        renderJson(Ret.ok().set("msg", "上传成功!").set("id", id));
    }

    public void modifySc() {
        Integer id = getParaToInt("id");
        String value = getPara("value");
        Db.update("update cl set rmsc=? where id=?", value, id);
        renderJson(Ret.ok().set("msg", "修改成功!").set("id", id));
    }

    public void gp() {
        render("gp.html");
    }

    public void yjsj() {
        render("yjsj.html");
    }

    public void search() {
        String mc = getPara("mc");
        String bm = getPara("bm");
        String ywmc = Db.queryFirst("select ywmc from " + bm + " where mc='" + mc + "'");
        renderJson(Ret.ok().set("data", ywmc));
    }

    public void needWriteDaily() {
        Account loginAccount = getLoginAccount();
        Integer zwh = Db.queryInt("select zwh from account where id = ?", loginAccount.getId());
        if (zwh == null || zwh == 0) {
            renderJson(Ret.ok(loginAccount.getXm() + ", " + yyyy_MM_dd() + " 无需填写早晚报!").set("need", false));
            return;
        }
        Integer isRest = Db.queryInt("select 1 from m_dim_day where day_short_desc = ? and fj='休息';", yyyy_MM_dd());
        if (isRest != null && isRest == 1) {
            renderJson(Ret.ok(" 今天是休息日，不执行任务!").set("need", false));
            return;
        }
        String today = Db.queryStr("select DATE_FORMAT(create_at, '%Y-%m-%d') today from (select * from notice where user_id = ? and status <> 2 and type='早晚会' order by create_at desc) a limit 1", loginAccount.getId());
        if (today == null) {
            renderJson(Ret.ok(loginAccount.getXm() + ", 没有需要填写早晚报!").set("need", false));
        } else {
            renderJson(Ret.ok("需要填写早晚报!").set("id", loginAccount.getId()).set("today", today).set("need", true));
        }
    }

    public void needNotice() {
        Account loginAccount = getLoginAccount();
        Record notice = Db.findFirst("select * from notice where user_id=? and showed = 0 order by create_at desc", loginAccount.getId());
        renderJson(Ret.ok("获取提示成功!").set("notice", notice));
    }

    public void noticeList() {
        Account loginAccount = getLoginAccount();
        List<Record> noticeList = Db.find("select * from notice where user_id=? order by create_at desc", loginAccount.getId());
        renderJson(Ret.ok("获取提示成功!").set("noticeList", noticeList));
    }

    public void markNoticeAsShowed() {
        Integer id = getParaToInt("id");
        Db.update("update notice set showed = 1 where id=? and status<>2", id);
        renderJson(Ret.ok("更新成功!"));
    }

    public void markNoticeAsRead() {
        Integer id = getParaToInt("id");
        Db.update("update notice set status = 1 where id=? and status<>2", id);
        renderJson(Ret.ok("更新成功!"));
    }

    public void showNotice() {
        Integer id = getParaToInt("id");
        Record notice = Db.findFirst("select * from notice where id=?", id);
        renderJson(Ret.ok("获取提示成功!").set("notice", notice));
    }

    public void allNoticeSetRead() {
        Db.update("update notice set status = 1 where user_id=? and status<>2", getLoginAccountId());
        renderJson(Ret.ok("更新成功!"));
    }

    public void getEmployeeList() {
        renderJson(Ret.ok().set("data", Db.find("select * from account where ifnull(bq,'澳林') in ('澳林', 'WDS', '工厂') and ifnull(xm, '')<>'' and status=1 order by xm;")));
    }

    public void getWDSAndOSEmployeeList() {
        renderJson(Ret.ok().set("data", Db.find("select * from account where ifnull(bq,'澳林') in ('澳林', 'WDS', '工厂') and ifnull(xm, '')<>'' and status=1 order by xm;")));
    }

    public void sendNotice() {
        String content = getPara("content");
        String userIds = getPara("userIds");

        if (StrKit.isBlank(content)) {
            renderJson(Ret.fail("msg", "通知内容不能为空"));
            return;
        }

        if (StrKit.isBlank(userIds)) {
            renderJson(Ret.fail("msg", "请选择要通知的用户"));
            return;
        }

        try {
            // 解析用户ID列表
            String[] userIdArray = userIds.split(",");


            // 构建SQL语句
            StringBuilder sql = new StringBuilder();
            sql.append("INSERT INTO notice (user_id, content, status, create_at, showed, type) ");
            sql.append("SELECT id, ?, 0, NOW(), 0, '通知' FROM account WHERE id IN (");
            ;

            // 添加用户ID占位符
            for (int i = 0; i < userIdArray.length; i++) {
                if (i > 0) sql.append(",");
                sql.append("?");
            }
            sql.append(") AND status = 1");

            // 准备参数
            Object[] params = new Object[userIdArray.length + 1];
            params[0] = content;
            for (int i = 0; i < userIdArray.length; i++) {
                params[i + 1] = Integer.parseInt(userIdArray[i].trim());
            }

            // 执行插入
            int count = Db.update(sql.toString(), params);

            if (count > 0) {
                renderJson(Ret.ok("msg", "通知发送成功，共发送给 " + count + " 个用户"));
            } else {
                renderJson(Ret.fail("msg", "没有找到有效的用户，发送失败"));
            }

        } catch (Exception e) {
            LogKit.error("发送通知失败", e);
            renderJson(Ret.fail("msg", "发送通知失败：" + e.getMessage()));
        }
    }

    public void dk() {
        String today = get("today", yyyy_MM_dd());
        List<Record> records = Db.find("select cjr,sj,dd,ip from kq_dk where sj>=? and sj<=? order by cjr;", today, today + " 23:59:59");
        StringBuilder content = new StringBuilder("<table><thead><tr><td>打卡人</td><td>打卡时间</td><td>打卡地点</td><td>打卡IP</td></tr></thead><tbody>");
        for (Record record : records) {
            content.append("<tr><td>").append(record.getStr("cjr")).append("</td><td>").append(record.getStr("sj")).append("</td><td>").append(record.getStr("dd")).append("</td><td>").append(record.getStr("ip")).append("</td></tr>");
        }
        content.append("</tbody></table>");

        List<Record> qjRecord = Db.find("select cjr,kssj,jssj from kq_qj where kssj<=? and jssj>=? order by cjr;", today + " 18:00:00", today + " 00:00:00");
        content.append("<hr><table><thead><tr><td>请假人</td><td>请假开始</td><td>请假结束</td></tr></thead><tbody>");
        for (Record record : qjRecord) {
            content.append("<tr><td>").append(record.getStr("cjr")).append("</td><td>").append(record.getStr("kssj")).append("</td><td>").append(record.getStr("jssj")).append("</td></tr>");
        }
        content.append("</tbody></table>");
        renderHtml(content.toString());
    }

    public void phone() {
        render("phone.html");
    }

    public String makeCall() throws Exception {
        Call call = Call.creator(
                        new PhoneNumber(get("toNumber")),
                        new PhoneNumber(get("TWILIO_NUMBER")),
                        new URI("http://360.theolympiastone.com/download/voice.xml")).setRecord(true) // 启用录音
                .create();

        return call.getSid();
    }

    public String getRecording() {
        Recording recording = Recording.reader()
                .setCallSid(get("callSid"))
                .firstPage()
                .getRecords().
                get(0);

        return recording.getUri();
    }
}
