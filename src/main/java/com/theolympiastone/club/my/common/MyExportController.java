package com.theolympiastone.club.my.common;

import com.google.common.collect.Maps;
import com.jfinal.aop.Before;
import com.jfinal.kit.PathKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import com.theolympiastone.club.common.controller.BaseController;
import com.theolympiastone.club.common.interceptor.FrontAuthInterceptor;

import java.util.Map;

import static com.theolympiastone.club.common.kit.ControllerKit.exportCsmx;
import static com.theolympiastone.club.common.kit.ControllerKit.genFile;

@Before(FrontAuthInterceptor.class)
public class MyExportController extends BaseController {
    public void index() {
        keepPara();
        render("index.html");
    }

    @Before(RoleValidator.class)
    public void csmx() {
        String ksyf = getPara("ksyf");
        String jsyf = getPara("jsyf");
        String wjmc = exportCsmx(ksyf, jsyf);
        redirect("/upload/" + wjmc + "?rand=" + System.currentTimeMillis());
    }


    public void zh() {
        Integer id = getParaToInt("id");
        Record zh = Db.findFirst("select * from zh where id=?", id);
        Map<String, String> map = Maps.newHashMap();
        for (String columnName : zh.getColumnNames()) {
            map.put(columnName, zh.getStr(columnName));
        }
        redirect("/upload/" + genFile("export/数据/账号.xls", PathKit.getWebRootPath() + "/upload/" + zh.getStr("mc") + ".xls?rand=" + System.currentTimeMillis(), map));
    }
}
