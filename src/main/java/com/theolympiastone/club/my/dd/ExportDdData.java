package com.theolympiastone.club.my.dd;

import com.alibaba.excel.annotation.ExcelProperty;
import com.theolympiastone.club.common.kit.DoubleStringConverter;

public class ExportDdData {
    private Integer id;
    private Integer xh;
    @ExcelProperty(value = "订单编号")
    private String ddbh;
    @ExcelProperty(value = "货柜编号")
    private String hgbh;
    @ExcelProperty(value = "工程名称")
    private String gcdd;
    @ExcelProperty(value = "客户订单号")
    private String khddh;
    @ExcelProperty(value = "中文石种")
    private String zwszm;
    @ExcelProperty(value = "英文石种")
    private String ywszm;
    @ExcelProperty(value = "中文品名")
    private String zwpm;
    @ExcelProperty(value = "英文品名")
    private String ywpm;
    @ExcelProperty(value = "单位")
    private String dw;
    @ExcelProperty(value = "Size A", converter = DoubleStringConverter.class)
    private Double sizea;
    @ExcelProperty(value = "Size B", converter = DoubleStringConverter.class)
    private Double sizeb;
    @ExcelProperty(value = "Size C", converter = DoubleStringConverter.class)
    private Double sizec;
    private Double sizeacm;
    private Double sizebcm;
    private Double sizeccm;
    @ExcelProperty(value = "数量", converter = DoubleStringConverter.class)
    private Double sl;
    @ExcelProperty(value = "数量单位")
    private String sldw;
    @ExcelProperty(value = "中文加工方式")
    private String zwjgff;
    @ExcelProperty(value = "英文加工方式")
    private String ywjgff;
    @ExcelProperty(value = "中文加工方式1")
    private String zwjgff1;
    @ExcelProperty(value = "英文加工方式1")
    private String ywjgff1;
    @ExcelProperty(value = "中文加工方式2")
    private String zwjgff2;
    @ExcelProperty(value = "英文加工方式2")
    private String ywjgff2;
    @ExcelProperty(value = "中文加工方式3")
    private String zwjgff3;
    @ExcelProperty(value = "英文加工方式3")
    private String ywjgff3;
    @ExcelProperty(value = "加工备注")
    private String jgbz;
    @ExcelProperty(value = "中文加工备注")
    private String zwjgbz;
    @ExcelProperty(value = "单价单位")
    private String djdw;
    @ExcelProperty(value = "单价", converter = DoubleStringConverter.class)
    private Double dj;
    @ExcelProperty(value = "附加费", converter = DoubleStringConverter.class)
    private Double dpfjf;
    @ExcelProperty(value = "材料附加%", converter = DoubleStringConverter.class)
    private Double clfj;
    @ExcelProperty(value = "每行总计", converter = DoubleStringConverter.class)
    private Double mhzj;
    @ExcelProperty(value = "才数", converter = DoubleStringConverter.class)
    private Double cs;
    @ExcelProperty(value = "重量KGS", converter = DoubleStringConverter.class)
    private Double zl;
    private String zlT;
    @ExcelProperty(value = "重量LBS", converter = DoubleStringConverter.class)
    private Double zllbs;
    @ExcelProperty(value = "体积", converter = DoubleStringConverter.class)
    private Double lfs;
    @ExcelProperty(value = "包装箱号")
    private String bzxh;
    @ExcelProperty(value = "包装备注")
    private String bzbz;
    @ExcelProperty(value = "中文包装备注")
    private String zwbzbz;
    @ExcelProperty(value = "是否索赔")
    private String sfsp;
    @ExcelProperty(value = "备注")
    private String bz;
    @ExcelProperty(value = "备注1")
    private String bz1;
    @ExcelProperty(value = "备注2")
    private String bz2;
    @ExcelProperty(value = "备注3")
    private String bz3;
    @ExcelProperty(value = "备注4")
    private String bz4;
    @ExcelProperty(value = "备注5")
    private String bz5;
    @ExcelProperty(value = "备注6")
    private String bz6;
    @ExcelProperty(value = "备注7")
    private String bz7;
    @ExcelProperty(value = "备注8")
    private String bz8;
    @ExcelProperty(value = "总数量")
    private String zsl;
    @ExcelProperty(value = "总金额", converter = DoubleStringConverter.class)
    private Double zje;
    @ExcelProperty(value = "总才数")
    private String zcs;
    @ExcelProperty(value = "总重量KGS")
    private String zzl;
    @ExcelProperty(value = "总重量LBS")
    private String zzllbs;
    @ExcelProperty(value = "总体积")
    private String zlfs;
    @ExcelProperty(value = "导入石种")
    private String khsz;
    @ExcelProperty(value = "导入品名")
    private String khpm;
    @ExcelProperty(value = "导入加工")
    private String khjg;
    @ExcelProperty(value = "唛头")
    private String mt;
    @ExcelProperty(value = "货币")
    private String hb;
    @ExcelProperty(value = "工厂")
    private String gc;
    private String sz;
    private String pm;
    private String hs;
    private String sbys;
    private String kzms;
    private String kzfjf;
    private String jg;
    private String wcje;

    public String getHs() {
        return hs;
    }

    public void setHs(String hs) {
        this.hs = hs;
    }

    public String getSbys() {
        return sbys;
    }

    public void setSbys(String sbys) {
        this.sbys = sbys;
    }

    public String getSz() {
        return sz;
    }

    public void setSz(String sz) {
        this.sz = sz;
    }

    public String getPm() {
        return pm;
    }

    public String getWcje() {
        return wcje;
    }

    public void setWcje(String wcje) {
        this.wcje = wcje;
    }

    public void setPm(String pm) {
        this.pm = pm;
    }

    public String getJg() {
        return jg;
    }

    public void setJg(String jg) {
        this.jg = jg;
    }

    public String getKzms() {
        return kzms;
    }

    public void setKzms(String kzms) {
        this.kzms = kzms;
    }

    public String getKzfjf() {
        return kzfjf;
    }

    public void setKzfjf(String kzfjf) {
        this.kzfjf = kzfjf;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getXh() {
        return xh;
    }

    public void setXh(Integer xh) {
        this.xh = xh;
    }

    public String getDdbh() {
        return ddbh;
    }

    public void setDdbh(String ddbh) {
        this.ddbh = ddbh;
    }

    public String getHgbh() {
        return hgbh;
    }

    public void setHgbh(String hgbh) {
        this.hgbh = hgbh;
    }

    public String getGcdd() {
        return gcdd;
    }

    public void setGcdd(String gcdd) {
        this.gcdd = gcdd;
    }

    public String getKhddh() {
        return khddh;
    }

    public void setKhddh(String khddh) {
        this.khddh = khddh;
    }

    public String getZwszm() {
        return zwszm;
    }

    public void setZwszm(String zwszm) {
        this.zwszm = zwszm;
    }

    public String getYwszm() {
        return ywszm;
    }

    public void setYwszm(String ywszm) {
        this.ywszm = ywszm;
    }

    public String getZwpm() {
        return zwpm;
    }

    public void setZwpm(String zwpm) {
        this.zwpm = zwpm;
    }

    public String getYwpm() {
        return ywpm;
    }

    public void setYwpm(String ywpm) {
        this.ywpm = ywpm;
    }

    public String getDw() {
        return dw;
    }

    public void setDw(String dw) {
        this.dw = dw;
    }

    public Double getSizea() {
        return sizea;
    }

    public void setSizea(Double sizea) {
        this.sizea = sizea;
    }

    public Double getSizeb() {
        return sizeb;
    }

    public void setSizeb(Double sizeb) {
        this.sizeb = sizeb;
    }

    public Double getSizec() {
        return sizec;
    }

    public void setSizec(Double sizec) {
        this.sizec = sizec;
    }

    public Double getSizeacm() {
        return sizeacm;
    }

    public void setSizeacm(Double sizeacm) {
        this.sizeacm = sizeacm;
    }

    public Double getSizebcm() {
        return sizebcm;
    }

    public void setSizebcm(Double sizebcm) {
        this.sizebcm = sizebcm;
    }

    public Double getSizeccm() {
        return sizeccm;
    }

    public void setSizeccm(Double sizeccm) {
        this.sizeccm = sizeccm;
    }

    public Double getSl() {
        return sl;
    }

    public void setSl(Double sl) {
        this.sl = sl;
    }

    public String getSldw() {
        return sldw;
    }

    public void setSldw(String sldw) {
        this.sldw = sldw;
    }

    public String getZwjgff() {
        return zwjgff;
    }

    public void setZwjgff(String zwjgff) {
        this.zwjgff = zwjgff;
    }

    public String getYwjgff() {
        return ywjgff;
    }

    public void setYwjgff(String ywjgff) {
        this.ywjgff = ywjgff;
    }

    public String getZwjgff1() {
        return zwjgff1;
    }

    public void setZwjgff1(String zwjgff1) {
        this.zwjgff1 = zwjgff1;
    }

    public String getYwjgff1() {
        return ywjgff1;
    }

    public void setYwjgff1(String ywjgff1) {
        this.ywjgff1 = ywjgff1;
    }

    public String getZwjgff2() {
        return zwjgff2;
    }

    public void setZwjgff2(String zwjgff2) {
        this.zwjgff2 = zwjgff2;
    }

    public String getYwjgff2() {
        return ywjgff2;
    }

    public void setYwjgff2(String ywjgff2) {
        this.ywjgff2 = ywjgff2;
    }

    public String getZwjgff3() {
        return zwjgff3;
    }

    public void setZwjgff3(String zwjgff3) {
        this.zwjgff3 = zwjgff3;
    }

    public String getYwjgff3() {
        return ywjgff3;
    }

    public void setYwjgff3(String ywjgff3) {
        this.ywjgff3 = ywjgff3;
    }

    public String getJgbz() {
        return jgbz;
    }

    public void setJgbz(String jgbz) {
        this.jgbz = jgbz;
    }

    public String getZwjgbz() {
        return zwjgbz;
    }

    public void setZwjgbz(String zwjgbz) {
        this.zwjgbz = zwjgbz;
    }

    public String getDjdw() {
        return djdw;
    }

    public void setDjdw(String djdw) {
        this.djdw = djdw;
    }

    public Double getDj() {
        return dj;
    }

    public void setDj(Double dj) {
        this.dj = dj;
    }

    public Double getDpfjf() {
        return dpfjf;
    }

    public void setDpfjf(Double dpfjf) {
        this.dpfjf = dpfjf;
    }

    public Double getClfj() {
        return clfj;
    }

    public void setClfj(Double clfj) {
        this.clfj = clfj;
    }

    public Double getMhzj() {
        return mhzj;
    }

    public void setMhzj(Double mhzj) {
        this.mhzj = mhzj;
    }

    public Double getCs() {
        return cs;
    }

    public void setCs(Double cs) {
        this.cs = cs;
    }

    public Double getZl() {
        return zl;
    }

    public void setZl(Double zl) {
        this.zl = zl;
    }

    public Double getZllbs() {
        return zllbs;
    }

    public void setZllbs(Double zllbs) {
        this.zllbs = zllbs;
    }

    public Double getLfs() {
        return lfs;
    }

    public void setLfs(Double lfs) {
        this.lfs = lfs;
    }

    public String getBzxh() {
        return bzxh;
    }

    public void setBzxh(String bzxh) {
        this.bzxh = bzxh;
    }

    public String getBzbz() {
        return bzbz;
    }

    public void setBzbz(String bzbz) {
        this.bzbz = bzbz;
    }

    public String getZwbzbz() {
        return zwbzbz;
    }

    public void setZwbzbz(String zwbzbz) {
        this.zwbzbz = zwbzbz;
    }

    public String getSfsp() {
        return sfsp;
    }

    public void setSfsp(String sfsp) {
        this.sfsp = sfsp;
    }

    public String getBz() {
        return bz;
    }

    public void setBz(String bz) {
        this.bz = bz;
    }

    public String getBz1() {
        return bz1;
    }

    public void setBz1(String bz1) {
        this.bz1 = bz1;
    }

    public String getBz2() {
        return bz2;
    }

    public void setBz2(String bz2) {
        this.bz2 = bz2;
    }

    public String getBz3() {
        return bz3;
    }

    public void setBz3(String bz3) {
        this.bz3 = bz3;
    }

    public String getBz4() {
        return bz4;
    }

    public void setBz4(String bz4) {
        this.bz4 = bz4;
    }

    public String getBz5() {
        return bz5;
    }

    public void setBz5(String bz5) {
        this.bz5 = bz5;
    }

    public String getBz6() {
        return bz6;
    }

    public void setBz6(String bz6) {
        this.bz6 = bz6;
    }

    public String getBz7() {
        return bz7;
    }

    public void setBz7(String bz7) {
        this.bz7 = bz7;
    }

    public String getBz8() {
        return bz8;
    }

    public void setBz8(String bz8) {
        this.bz8 = bz8;
    }

    public String getZsl() {
        return zsl;
    }

    public void setZsl(String zsl) {
        this.zsl = zsl;
    }

    public Double getZje() {
        return zje;
    }

    public void setZje(Double zje) {
        this.zje = zje;
    }

    public String getZcs() {
        return zcs;
    }

    public void setZcs(String zcs) {
        this.zcs = zcs;
    }

    public String getZzl() {
        return zzl;
    }

    public void setZzl(String zzl) {
        this.zzl = zzl;
    }

    public String getZzllbs() {
        return zzllbs;
    }

    public void setZzllbs(String zzllbs) {
        this.zzllbs = zzllbs;
    }

    public String getZlfs() {
        return zlfs;
    }

    public void setZlfs(String zlfs) {
        this.zlfs = zlfs;
    }

    public String getKhsz() {
        return khsz;
    }

    public void setKhsz(String khsz) {
        this.khsz = khsz;
    }

    public String getKhpm() {
        return khpm;
    }

    public void setKhpm(String khpm) {
        this.khpm = khpm;
    }

    public String getKhjg() {
        return khjg;
    }

    public void setKhjg(String khjg) {
        this.khjg = khjg;
    }

    public String getMt() {
        return mt;
    }

    public void setMt(String mt) {
        this.mt = mt;
    }

    public String getHb() {
        return hb;
    }

    public void setHb(String hb) {
        this.hb = hb;
    }

    public String getGc() {
        return gc;
    }

    public void setGc(String gc) {
        this.gc = gc;
    }

    public String getZlT() {
        return zlT;
    }

    public void setZlT(String zlT) {
        this.zlT = zlT;
    }

}
