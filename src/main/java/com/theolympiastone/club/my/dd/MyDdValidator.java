package com.theolympiastone.club.my.dd;

import com.jfinal.core.Controller;
import com.jfinal.validate.Validator;

public class MyDdValidator extends Validator {


    @Override
    protected void validate(Controller c) {
        setShortCircuit(true);
//        validateRequiredString("dd.", "msg", "不能为空");
        //$[isnotnull]
    }

    @Override
    protected void handleError(Controller c) {
        c.setAttr("state", "fail");
        c.render<PERSON>son();
    }
}
