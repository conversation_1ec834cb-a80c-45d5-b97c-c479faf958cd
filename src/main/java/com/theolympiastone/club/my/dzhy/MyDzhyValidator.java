package com.theolympiastone.club.my.dzhy;

import com.jfinal.core.Controller;
import com.jfinal.validate.Validator;

public class MyDzhyValidator extends Validator {


    @Override
    protected void validate(Controller c) {
        setShortCircuit(true);
//        validateRequiredString("dzhy.id", "msg", "id不能为空");
        //$[isnotnull]
    }

    @Override
    protected void handleError(Controller c) {
        c.setAttr("state", "fail");
        c.render<PERSON>son();
    }
}
