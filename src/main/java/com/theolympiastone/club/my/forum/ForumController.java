package com.theolympiastone.club.my.forum;

import com.jfinal.aop.Before;
import com.jfinal.kit.PathKit;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import com.jfinal.upload.UploadFile;
import com.theolympiastone.club.common.controller.BaseController;
import com.theolympiastone.club.common.interceptor.FrontAuthInterceptor;
import com.theolympiastone.club.common.model.*;
import net.m3u8.utils.StringUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.io.FileUtils;

import java.io.File;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.UUID;

import static com.theolympiastone.club.common.interceptor.AuthCacheClearInterceptor.isAdmin;
import static com.theolympiastone.club.common.kit.StringKit.yyyy_MM_dd;

@Before({FrontAuthInterceptor.class})
public class ForumController extends BaseController {

    // 论坛首页
    public void index() {
        int pageNumber = getParaToInt("page", 1);
        Long categoryId = getParaToLong("category", null);

        // 获取分类列表
        List<ForumCategory> categories = ForumCategory.dao.getAllEnabled();
        setAttr("categories", categories);
        setAttr("currentCategory", categoryId);

        // 构建查询条件
        String select = "select p.*, c.name as category_name";
        StringBuilder where = new StringBuilder("from forum_post p left join forum_category c on p.category_id = c.id where p.status = 1");
        if (categoryId != null) {
            where.append(" and p.category_id = ").append(categoryId);
        }
        where.append(" order by p.is_essence desc, p.created_at desc");

        Page<ForumPost> page = ForumPost.dao.paginate(pageNumber, 10, select, where.toString());
        setAttr("page", page);
        render("index.html");
    }

    // 发帖页面
    public void add() {
        setAttr("categories", ForumCategory.dao.getAllEnabled());
        render("add.html");
    }

    // 保存帖子
    public void save() {
        ForumPost post = getModel(ForumPost.class, "forumPost", true);
        String addFiles = get("addFiles", "");
        String employees = get("employees", "");

        // 修改: 限制标题长度为20个字符
        String title = post.getStr("title");
        if (title != null && title.length() > 20) {
            title = title.substring(0, 20);
        }
        post.set("user_id", getLoginAccountId())
                .set("anonymous", getParaToBoolean("anonymous", false))
                .set("created_at", new Date())
                .save();
        if (!StringUtils.isEmpty(addFiles)) {
            String[] split = addFiles.split(",");
            for (String forumAttachmentId : split) {
                ForumAttachment.dao.findById(Integer.parseInt(forumAttachmentId)).set("post_id", post.getId()).update();
            }
        }
        if (!StringUtils.isEmpty(employees)) {
            List<String> insertSqlList = Lists.newArrayList();
            String[] split = employees.split("[,;]");
            for (String id : split) {
                insertSqlList.add("insert into notice (user_id, content, status, create_at, showed, type) values ( " + id + ", " +
                        "       '" + yyyy_MM_dd() + " 发帖, " + title + "。<a class=\"layui-btn layui-btn-sm\" href=\"http://360.theolympiastone.com/my/forum/d/" + post.getId() + "\" target=\"_blank\">帖子详情</a>', " +
                        "       0, " +
                        "       now(), " +
                        "       0, '通知');");
            }
            Db.batch(insertSqlList, insertSqlList.size());
        }

        redirect("/my/forum");
    }

    // 帖子详情页
    public void d() {
        Long id = getParaToLong();
        ForumPost post = ForumPost.dao.findById(id);
        if (post == null || post.getInt("status") != 1) {
            renderError(404);
            return;
        }

        // 增加浏览次数
        post.set("view_count", post.getInt("view_count") + 1).update();

        // 获取回复列表（只取顶层回复）
        Page<ForumReply> replyPage = ForumReply.dao.paginate(getParaToInt("page", 1), 10,
                "select *",
                "from forum_reply where post_id = ? and parent_id is null and status = 1 order by is_best desc, created_at desc",
                id);

        // 获取附件列表
        setAttr("attachments", ForumAttachment.dao.find("select * from forum_attachment where post_id = ? and status = 1", id));
        setAttr("post", post);
        setAttr("replyPage", replyPage);
        render("detail.html");
    }

    // 保存回复
    public void saveReply() {
        // 处理上传的附件
        List<UploadFile> files = getFiles();
        ForumReply reply = getModel(ForumReply.class, "forumReply", true);
        reply.set("user_id", getLoginAccountId())
                .set("anonymous", getParaToBoolean("anonymous", false))
                .set("created_at", new Date())
                .save();

        // 更新帖子回复数
        ForumPost post = ForumPost.dao.findById(reply.getLong("post_id"));
        post.set("reply_count", post.getInt("reply_count") + 1).update();
        if (files != null) {
            for (UploadFile file : files) {
                String originalFileName = file.getOriginalFileName();
                String ext = originalFileName.substring(originalFileName.lastIndexOf("."));
                String newFileName = originalFileName + "-" + UUID.randomUUID() + ext;

                File dest = new File(getUploadPath() + "/forum/" + newFileName);
                try {
                    FileUtils.moveFile(file.getFile(), dest);
                } catch (IOException e) {
                    e.printStackTrace();
                }
                new ForumAttachment()
                        .set("reply_id", reply.getLong("id"))
                        .set("file_type", getFileType(ext))
                        .set("file_path", "/upload/forum/" + newFileName)
                        .set("file_name", originalFileName)
                        .set("file_size", file.getFile().length())
                        .save();
            }
        }

        redirect("/my/forum/d/" + reply.getLong("post_id"));
    }

    // 设置最佳答案
    public void setBestReply() {
        Long postId = getParaToLong("postId");
        Long replyId = getParaToLong("replyId");

        ForumPost post = ForumPost.dao.findById(postId);
        if (post == null || !post.canSetBestReply(getLoginAccountId())) {
            renderJson(Ret.fail());
            return;
        }

        // 更新帖子的最佳答案
        post.set("best_reply_id", replyId).update();

        // 更新回复的最佳答案标记
        ForumReply reply = ForumReply.dao.findById(replyId);
        reply.set("is_best", true).update();

        // 给回复者加积分
        ForumScoreLog.addScore(
                reply.getLong("user_id"),
                ForumScoreRule.TYPE_REPLY_BEST,
                postId,
                replyId,
                "回复被设为最佳答案"
        );

        renderJson(Ret.ok());
    }

    // 设置/取消精华帖子
    public void setEssence() {
        Long postId = getParaToLong("postId");

        if (!ForumPermission.canSetEssence(getLoginAccountId(), isAdmin(getLoginAccount()))) {
            renderJson(Ret.fail("msg", "没有权限"));
            return;
        }

        ForumPost post = ForumPost.dao.findById(postId);
        if (post == null) {
            renderJson(Ret.fail("msg", "帖子不存在"));
            return;
        }

        boolean currentStatus = post.getBoolean("is_essence");
        post.set("is_essence", !currentStatus).update();

        // 如果是设为精华，给发帖者加积分
        if (!currentStatus) {
            ForumScoreLog.addScore(
                    post.getLong("user_id"),
                    ForumScoreRule.TYPE_POST_ESSENCE,
                    postId,
                    null,
                    "帖子被设为精华"
            );
        }

        renderJson(Ret.ok("msg", !currentStatus ? "设置精华成功" : "取消精华成功"));
    }

    // 点赞/踩
    public void vote() {
        Long postId = getParaToLong("postId");
        Long replyId = getParaToLong("replyId");
        int voteType = getParaToInt("type"); // 1:赞, 2:踩
        Integer userId = getLoginAccountId();

        // 检查是否已经投过票
        ForumVote existingVote = ForumVote.dao.findFirst(
                "select * from forum_vote where post_id = ? and reply_id = ? and user_id = ?",
                postId, replyId, userId
        );

        // 准备返回结果
        Ret ret = Ret.create();

        if (existingVote != null) {
            // 如果是同一类型的投票，则取消投票
            if (existingVote.getInt("vote_type") == voteType) {
                // 删除投票记录
                existingVote.delete();

                // 更新计数
                if (replyId != null) {
                    ForumReply reply = ForumReply.dao.findById(replyId);
                    if (voteType == 1) {
                        reply.set("like_count", reply.getInt("like_count") - 1);
                    } else {
                        reply.set("dislike_count", reply.getInt("dislike_count") - 1);
                    }
                    reply.update();
                } else {
                    ForumPost post = ForumPost.dao.findById(postId);
                    if (voteType == 1) {
                        post.set("like_count", post.getInt("like_count") - 1);
                    } else {
                        post.set("dislike_count", post.getInt("dislike_count") - 1);
                    }
                    post.update();
                }

                ret.set("state", "canceled")
                        .set("msg", voteType == 1 ? "已取消点赞" : "已取消点踩");
            } else {
                // 如果是不同类型的投票，返回错误
                ret.set("state", "error")
                        .set("msg", "您已经投过票了");
            }
        } else {
            // 新投票
            new ForumVote()
                    .set("post_id", postId)
                    .set("reply_id", replyId)
                    .set("user_id", userId)
                    .set("vote_type", voteType)
                    .save();

            // 更新点赞/踩数并记录积分
            if (replyId != null) {
                ForumReply reply = ForumReply.dao.findById(replyId);
                if (voteType == 1) {
                    reply.set("like_count", reply.getInt("like_count") + 1);
                    // 给回复者加积分
                    ForumScoreLog.addScore(
                            reply.getLong("user_id"),
                            ForumScoreRule.TYPE_REPLY_LIKE,
                            postId,
                            replyId,
                            "回复被点赞"
                    );
                } else {
                    reply.set("dislike_count", reply.getInt("dislike_count") + 1);
                }
                reply.update();
            } else {
                ForumPost post = ForumPost.dao.findById(postId);
                if (voteType == 1) {
                    post.set("like_count", post.getInt("like_count") + 1);
                    // 给发帖者加积分
                    ForumScoreLog.addScore(
                            post.getLong("user_id"),
                            ForumScoreRule.TYPE_POST_LIKE,
                            postId,
                            null,
                            "帖子被点赞"
                    );
                } else {
                    post.set("dislike_count", post.getInt("dislike_count") + 1);
                }
                post.update();
            }

            ret.set("state", "success")
                    .set("msg", voteType == 1 ? "点赞成功" : "踩一下");
        }

        renderJson(ret);
    }

    // 分享
    public void share() {
        Long postId = getParaToLong("postId");
        ForumPost post = ForumPost.dao.findById(postId);
        post.set("share_count", post.getInt("share_count") + 1).update();
        renderJson(Ret.ok());
    }

    // 积分页面
    public void score() {
        int pageNumber = getParaToInt("page", 1);

        // 获取用户总积分
        int totalScore = ForumScoreLog.getTotalScore(getLoginAccountId());
        setAttr("totalScore", totalScore);

        // 获取积分记录
        Page<ForumScoreLog> scoreLogs = ForumScoreLog.getUserScoreLogs(getLoginAccountId(), pageNumber, 10);
        setAttr("scoreLogs", scoreLogs);

        // 获取积分排行榜
        String sql = "select u.name, sum(l.score) as total_score " +
                "from forum_score_log l " +
                "left join user u on l.user_id = u.id " +
                "group by l.user_id " +
                "order by total_score desc " +
                "limit 10";
        List<Record> scoreRanks = Db.find(sql);
        setAttr("scoreRanks", scoreRanks);

        render("score.html");
    }

    // 删除帖子
    public void deletePost() {
        Long postId = getParaToLong("id");
        if (postId == null) {
            postId = getLong(0);
        }
        if (postId == null) {
            renderJson(Ret.fail());
            return;
        }

        ForumPost post = ForumPost.dao.findById(postId);
        if (!ForumPermission.canDeletePost(getLoginAccountId(), post, isAdmin(getLoginAccount()))) {
            renderJson(Ret.fail());
            return;
        }

        // 软删除帖子
        post.set("status", 0).update();

        // 软删除相关回复
        Db.update("update forum_reply set status = 0 where post_id = ?", postId);

        renderJson(Ret.ok());
    }

    // 删除回复
    public void deleteReply() {
        Long replyId = getParaToLong("id");

        ForumReply reply = ForumReply.dao.findById(replyId);
        if (!ForumPermission.canDeleteReply(getLoginAccountId(), reply, isAdmin(getLoginAccount()))) {
            renderJson(Ret.fail());
            return;
        }

        // 软删除回复
        reply.set("status", 0).update();

        // 更新帖子回复数
        ForumPost post = ForumPost.dao.findById(reply.getLong("post_id"));
        post.set("reply_count", post.getInt("reply_count") - 1).update();

        renderJson(Ret.ok());
    }

    // 编辑帖子页面
    public void edit() {
        Long id = getParaToLong();

        ForumPost post = ForumPost.dao.findById(id);
        if (!ForumPermission.canEditPost(getLoginAccountId(), post, isAdmin(getLoginAccount()))) {
            redirect("/my/forum");
            return;
        }

        setAttr("post", post);
        setAttr("categories", ForumCategory.dao.getAllEnabled());
        setAttr("attachments", ForumAttachment.dao.find("select * from forum_attachment where post_id = ? and status = 1", id));
        render("edit.html");
    }

    // 更新帖子
    public void update() {
        // 处理新上传的附件
        ForumPost post = getModel(ForumPost.class, "forumPost", true);
        String addFiles = get("addFiles", "");
        String employees = get("employees", "");
        Long id = post.getLong("id");
        ForumPost oldPost = ForumPost.dao.findById(id);
        if (!ForumPermission.canEditPost(getLoginAccountId(), oldPost, isAdmin(getLoginAccount()))) {
            renderJson(Ret.fail());
            return;
        }

        // 修改: 限制标题长度为20个字符
        String title = post.getStr("title");
        if (title != null && title.length() > 20) {
            title = title.substring(0, 20);
        }

        // 更新帖子基本信息
        oldPost.set("title", title)
                .set("content", post.getStr("content"))
                .set("category_id", post.getLong("category_id"))
                .set("anonymous", getParaToBoolean("anonymous", false))
                .set("updated_at", new Date())
                .update();
        if (!StringUtils.isEmpty(addFiles)) {
            String[] split = addFiles.split(",");
            for (String forumAttachmentId : split) {
                ForumAttachment.dao.findById(Integer.parseInt(forumAttachmentId)).set("post_id", id).update();
            }
        }

        if (!StringUtils.isEmpty(employees)) {
            List<String> insertSqlList = Lists.newArrayList();
            String[] split = employees.split(",");
            for (String userId : split) {
                insertSqlList.add("insert into notice (user_id, content, status, create_at, showed, type) values ( " + userId + ", " +
                        "       '" + yyyy_MM_dd() + " 发帖。<a class=\"layui-btn layui-btn-sm\" href=\"http://360.theolympiastone.com/my/forum/d/" + post.getId() + "\" target=\"_blank\">帖子详情</a>', " +
                        "       0, " +
                        "       now(), " +
                        "       0, '通知');");
            }
            Db.batch(insertSqlList, insertSqlList.size());
        }
        redirect("/my/forum/d/" + id);
    }

    // 删除附件
    public void deleteAttachment() {
        Long id = getParaToLong("id");

        ForumAttachment attachment = ForumAttachment.dao.findById(id);
        if (attachment == null) {
            renderJson(Ret.fail());
            return;
        }

        ForumPost post = null;
        ForumReply reply = null;

        if (attachment.getLong("post_id") != null) {
            post = ForumPost.dao.findById(attachment.getLong("post_id"));
        } else if (attachment.getLong("reply_id") != null) {
            reply = ForumReply.dao.findById(attachment.getLong("reply_id"));
        }

        if (!ForumPermission.canDeleteAttachment(getLoginAccountId(), post, reply, isAdmin(getLoginAccount()))) {
            renderJson(Ret.fail());
            return;
        }

        // 软删除附件
        attachment.set("status", 0).update();
        renderJson(Ret.ok());
    }

    // 编辑回复
    public void editReply() {
        Long id = getParaToLong();

        ForumReply reply = ForumReply.dao.findById(id);
        if (reply == null || (!isAdmin(getLoginAccount()) && !reply.canEdit(getLoginAccountId()))) {
            renderJson(Ret.fail());
            return;
        }

        setAttr("reply", reply);
        setAttr("attachments", ForumAttachment.dao.find("select * from forum_attachment where reply_id = ? and status = 1", id));
        renderJson(reply);
    }

    // 更新回复
    public void updateReply() {
        // 处理新上传的附件
        List<UploadFile> files = getFiles();
        ForumReply reply = getModel(ForumReply.class);

        ForumReply oldReply = ForumReply.dao.findById(reply.getLong("id"));
        if (oldReply == null || (!isAdmin(getLoginAccount()) && !oldReply.canEdit(getLoginAccountId()))) {
            renderJson(Ret.fail());
            return;
        }

        // 更新回复基本信息
        oldReply.set("content", reply.getStr("content"))
                .set("anonymous", getParaToBoolean("anonymous", false))
                .set("updated_at", new Date())
                .update();

        if (files != null) {
            for (UploadFile file : files) {
                String originalFileName = file.getOriginalFileName();
                String ext = originalFileName.substring(originalFileName.lastIndexOf("."));
                String newFileName = originalFileName + "-" + UUID.randomUUID() + ext;

                File dest = new File(getUploadPath() + "/forum/" + newFileName);
                try {
                    FileUtils.moveFile(file.getFile(), dest);
                } catch (IOException e) {
                    e.printStackTrace();
                }

                new ForumAttachment()
                        .set("reply_id", reply.getLong("id"))
                        .set("file_type", getFileType(ext))
                        .set("file_path", "/upload/forum/" + newFileName)
                        .set("file_name", originalFileName)
                        .set("file_size", file.getFile().length())
                        .save();
            }
        }

        renderJson(Ret.ok());
    }

    private String getUploadPath() {
        return PathKit.getWebRootPath() + "/upload";
    }

    private int getFileType(String ext) {
        ext = ext.toLowerCase();
        if (ext.matches("\\.(jpg|jpeg|png|gif|bmp)$")) {
            return ForumAttachment.TYPE_IMAGE;
        } else if (ext.matches("\\.(mp4|avi|mov|wmv|flv)$")) {
            return ForumAttachment.TYPE_VIDEO;
        } else if (ext.matches("\\.(mp3|wav|wma|ogg|aac)$")) {
            return ForumAttachment.TYPE_AUDIO;
        }
        return 0;
    }

    public void uploadFile() {
        UploadFile file = getFile();
        if (file != null) {
            String originalFileName = file.getOriginalFileName();
            String ext = originalFileName.substring(originalFileName.lastIndexOf("."));
            String newFileName = originalFileName + "-" + UUID.randomUUID() + ext;

            // 移动文件到目标目录
            File dest = new File(getUploadPath() + "/forum/" + newFileName);
            try {
                FileUtils.moveFile(file.getFile(), dest);
            } catch (IOException e) {
                e.printStackTrace();
            }
            // 保存附件记录
            ForumAttachment forumAttachment = new ForumAttachment();
            forumAttachment
                    .set("file_type", getFileType(ext))
                    .set("file_path", "/upload/forum/" + newFileName)
                    .set("file_name", originalFileName)
                    .set("file_size", dest.length())
                    .save();
            renderJson(Ret.ok("上传成功!").set("id", forumAttachment.getId()).set("url", forumAttachment.getFilePath()).set("name", forumAttachment.getFileName()).set("type", forumAttachment.getFileType()).set("size", forumAttachment.getFileSize()));
            return;
        }
        renderJson(Ret.fail("没有上传的文件"));
    }
} 