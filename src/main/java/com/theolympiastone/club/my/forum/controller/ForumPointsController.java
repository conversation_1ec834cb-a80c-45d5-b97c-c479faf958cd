package com.theolympiastone.club.my.forum.controller;

import com.jfinal.aop.Before;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Page;
import com.theolympiastone.club.common.controller.BaseController;
import com.theolympiastone.club.common.interceptor.FrontAuthInterceptor;
import com.theolympiastone.club.common.model.Account;
import com.theolympiastone.club.common.model.ForumPointsOrder;
import com.theolympiastone.club.common.model.ForumPointsProduct;

import java.util.Date;
import java.util.UUID;

import static com.theolympiastone.club.common.interceptor.AuthCacheClearInterceptor.isAdmin;

/**
 * 积分商城控制器
 */
@Before({FrontAuthInterceptor.class})
public class ForumPointsController extends BaseController {

    /**
     * 积分商城首页
     */
    public void index() {
        int pageNumber = getParaToInt("page", 1);
        Page<ForumPointsProduct> products = ForumPointsProduct.dao.getProducts(pageNumber, 12);
        setAttr("products", products);
        render("index.html");
    }

    /**
     * 我的订单列表
     */
    public void orders() {
        int pageNumber = getParaToInt("page", 1);
        Page<ForumPointsOrder> orders = ForumPointsOrder.dao.getUserOrders((long) getLoginAccountId(), pageNumber, 10);
        setAttr("orders", orders);
        render("orders.html");
    }

    /**
     * 订单详情
     */
    public void orderDetail() {
        String orderNo = getPara("orderNo");
        ForumPointsOrder order = ForumPointsOrder.dao.findFirst(
                "select * from forum_points_order where order_no = ?", orderNo);

        if (order == null || !order.getUserId().equals((long) getLoginAccountId())) {
            renderError(404);
            return;
        }

        setAttr("order", order);
        render("order_detail.html");
    }

    /**
     * 创建订单
     */
    public void createOrder() {
        Long productId = getParaToLong("productId");
        ForumPointsProduct product = ForumPointsProduct.dao.findById(productId);
        if (product == null) {
            renderJson(Ret.fail("msg", "商品不存在"));
            return;
        }

        // 检查商品状态
        if (product.getStatus() != 1) {
            renderJson(Ret.fail("msg", "商品已下架"));
            return;
        }

        // 检查库存
        if (!product.hasEnoughStock()) {
            renderJson(Ret.fail("msg", "商品库存不足"));
            return;
        }

        // 检查兑换限制
        if (!product.checkExchangeLimit((long) getLoginAccountId())) {
            renderJson(Ret.fail("msg", "已达到该商品的兑换限制"));
            return;
        }

        Account loginAccount = getLoginAccount();
        if (loginAccount.getInt("points") < product.getPoints()) {
            renderJson(Ret.fail("msg", "积分不足"));
            return;
        }

        // 扣除积分
        loginAccount.set("points", loginAccount.getInt("points") - product.getPoints()).update();

        // 扣减库存
        if (!product.reduceStock()) {
            // 库存扣减失败，可能是并发导致的库存不足
            loginAccount.set("points", loginAccount.getInt("points") + product.getPoints()).update();
            renderJson(Ret.fail("msg", "商品库存不足"));
            return;
        }

        // 创建订单
        ForumPointsOrder order = new ForumPointsOrder()
                .set("order_no", "PO" + UUID.randomUUID().toString().replace("-", "").substring(0, 28))
                .set("user_id", getLoginAccountId())
                .set("product_id", product.getId())
                .set("product_name", product.getName())
                .set("points", product.getPoints())
                .set("status", product.isVirtualProduct() ? ForumPointsOrder.STATUS_SHIPPED : ForumPointsOrder.STATUS_PENDING)
                .set("created_time", new Date())
                .set("update_time", new Date());

        if (product.isVirtualProduct()) {
            // 虚拟商品自动发货
            order.set("shipping_time", new Date())
                    .set("virtual_code", generateVirtualCode()); // 生成虚拟商品兑换码
        }

        if (order.save()) {
            renderJson(Ret.ok("msg", "下单成功").set("orderNo", order.getOrderNo()));
        } else {
            // 下单失败，恢复积分和库存
            loginAccount.set("points", loginAccount.getInt("points") + product.getPoints()).update();
            product.restoreStock();
            renderJson(Ret.fail("msg", "下单失败"));
        }
    }

    /**
     * 生成虚拟商品兑换码
     */
    private String generateVirtualCode() {
        return "VC" + UUID.randomUUID().toString().replace("-", "").substring(0, 16).toUpperCase();
    }

    /**
     * 取消订单
     */
    public void cancelOrder() {
        String orderNo = getPara("orderNo");
        ForumPointsOrder order = ForumPointsOrder.dao.findFirst(
                "select * from forum_points_order where order_no = ?", orderNo);

        if (order == null || !order.getUserId().equals((long) getLoginAccountId())) {
            renderJson(Ret.fail("msg", "订单不存在"));
            return;
        }

        if (order.cancel(getLoginAccount())) {
            // 恢复商品库存
            ForumPointsProduct product = ForumPointsProduct.dao.findById(order.getProductId());
            if (product != null) {
                product.restoreStock();
            }
            renderJson(Ret.ok("msg", "订单已取消"));
        } else {
            renderJson(Ret.fail("msg", "取消失败"));
        }
    }

    /**
     * 确认收货
     */
    public void confirmReceive() {
        String orderNo = getPara("orderNo");
        ForumPointsOrder order = ForumPointsOrder.dao.findFirst(
                "select * from forum_points_order where order_no = ?", orderNo);

        if (order == null || !order.getUserId().equals((long) getLoginAccountId())) {
            renderJson(Ret.fail("msg", "订单不存在"));
            return;
        }

        if (order.complete()) {
            renderJson(Ret.ok("msg", "确认收货成功"));
        } else {
            renderJson(Ret.fail("msg", "确认收货失败"));
        }
    }

    /**
     * 管理员订单列表
     */
    public void adminOrders() {
        if (!isAdmin(getLoginAccount())) {
            renderError(403);
            return;
        }

        int pageNumber = getParaToInt("page", 1);
        Page<ForumPointsOrder> orders = ForumPointsOrder.dao.getAllOrders(pageNumber, 10);
        setAttr("orders", orders);
        render("admin_orders.html");
    }

    /**
     * 发货
     */
    public void ship() {
        if (!isAdmin(getLoginAccount())) {
            renderJson(Ret.fail("msg", "无权限"));
            return;
        }

        String orderNo = getPara("orderNo");
        String shippingCompany = getPara("shippingCompany");
        String shippingNo = getPara("shippingNo");

        ForumPointsOrder order = ForumPointsOrder.dao.findFirst(
                "select * from forum_points_order where order_no = ?", orderNo);

        if (order == null) {
            renderJson(Ret.fail("msg", "订单不存在"));
            return;
        }

        if (order.ship(shippingCompany, shippingNo)) {
            renderJson(Ret.ok("msg", "发货成功"));
        } else {
            renderJson(Ret.fail("msg", "发货失败"));
        }
    }
} 