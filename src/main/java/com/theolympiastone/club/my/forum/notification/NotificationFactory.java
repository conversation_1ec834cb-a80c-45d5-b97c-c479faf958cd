package com.theolympiastone.club.my.forum.notification;

import com.theolympiastone.club.common.model.ForumPointsOrder;
import com.theolympiastone.club.common.model.ForumPointsOrderNotification;

import java.util.HashMap;
import java.util.Map;

/**
 * 通知服务工厂
 */
public class NotificationFactory {
    private static final Map<String, NotificationService> services = new HashMap<>();

    static {
        services.put(ForumPointsOrderNotification.TYPE_EMAIL, new EmailNotificationService());
        services.put(ForumPointsOrderNotification.TYPE_SYSTEM, new SystemNotificationService());
    }

    /**
     * 获取通知服务
     */
    public static NotificationService getService(String type) {
        return services.get(type);
    }

    /**
     * 发送所有类型的通知
     */
    public static void sendAllNotifications(String method, Object... args) {
        for (NotificationService service : services.values()) {
            try {
                switch (method) {
                    case "orderStatus":
                        service.sendOrderStatusNotification((ForumPointsOrder) args[0], (String) args[1], (String) args[2]);
                        break;
                    case "shipping":
                        service.sendShippingNotification((ForumPointsOrder) args[0]);
                        break;
                    case "refund":
                        service.sendRefundNotification((ForumPointsOrder) args[0]);
                        break;
                    case "complete":
                        service.sendOrderCompleteNotification((ForumPointsOrder) args[0]);
                        break;
                    case "cancel":
                        service.sendOrderCancelNotification((ForumPointsOrder) args[0]);
                        break;
                }
            } catch (Exception e) {
                // 一个通知服务失败不影响其他服务
                continue;
            }
        }
    }
} 