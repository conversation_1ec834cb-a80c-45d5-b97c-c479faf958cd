package com.theolympiastone.club.my.forum.notification;

import com.theolympiastone.club.common.model.ForumPointsOrder;
import com.theolympiastone.club.common.model.ForumPointsOrderNotification;

/**
 * 通知服务接口
 */
public interface NotificationService {

    /**
     * 发送订单状态变更通知
     */
    ForumPointsOrderNotification sendOrderStatusNotification(ForumPointsOrder order, String oldStatus, String newStatus);

    /**
     * 发送发货通知
     */
    ForumPointsOrderNotification sendShippingNotification(ForumPointsOrder order);

    /**
     * 发送退款通知
     */
    ForumPointsOrderNotification sendRefundNotification(ForumPointsOrder order);

    /**
     * 发送订单完成通知
     */
    ForumPointsOrderNotification sendOrderCompleteNotification(ForumPointsOrder order);

    /**
     * 发送订单取消通知
     */
    ForumPointsOrderNotification sendOrderCancelNotification(ForumPointsOrder order);
} 