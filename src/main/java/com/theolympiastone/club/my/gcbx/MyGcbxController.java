package com.theolympiastone.club.my.gcbx;

import com.jfinal.aop.Before;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.upload.UploadFile;
import com.theolympiastone.club._admin.auth.AdminAuthService;
import com.theolympiastone.club.common.controller.BaseController;
import com.theolympiastone.club.common.interceptor.FrontAuthInterceptor;
import com.theolympiastone.club.common.kit.ControllerKit;
import com.theolympiastone.club.common.kit.StringKit;
import com.theolympiastone.club.common.model.Gcbx;
import com.theolympiastone.club.my.sp.MySpService;
import com.theolympiastone.club.project.ProjectService;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.util.Date;
import java.util.List;

import static com.theolympiastone.club.common.kit.DdgkKit.updateDdgk;
import static com.theolympiastone.club.common.kit.StringKit.formatPureDate;
import static com.theolympiastone.club.common.kit.StringKit.trueString;

@Before({FrontAuthInterceptor.class})
public class MyGcbxController extends BaseController {
    @Inject
    MyGcbxService srv;
    @Inject
    MySpService spSrv;
    @Inject
    ProjectService projectService;

    public void index() {
        keepPara();
        String query = getPara("query", "");
        String queryKsrq = getPara("queryKsrq", "");
        String queryJsrq = getPara("queryJsrq", "");
        String queryPx = getPara("queryPx", " order by bxsj desc");
        setAttr("query", query);
        setAttr("queryKsrq", queryKsrq);
        setAttr("queryJsrq", queryJsrq);
        setAttr("queryPx", queryPx);
        Page<Gcbx> wcbxPage = srv.paginate(getParaToInt("p", 1), getLoginAccount(), query, queryKsrq, queryJsrq, queryPx);
        setAttr("page", wcbxPage);
        render("index.html");
    }

    public void add() {
        setAttr("isAdd", true);
        setAttr("ddList", spSrv.getDdList());
        setAttr("skrList", Db.find("select distinct skr,khh,khzh from gcbx order by skr"));
        boolean hasRole = AdminAuthService.me.hasRole(getLoginAccountId(), new String[]{"权限管理员", "超级管理员", "总经理"});
        if (hasRole) {
            setAttr("khList", Db.find("select id,jc,hb from kh order by jc"));
        } else {
            setAttr("khList", Db.find("select id,jc,hb from kh where ywy like '%" + getLoginAccount().getUserName() + "%' order by jc"));
        }
        setAttr("accountList", projectService.getAccountList());
        render("add_edit.html");
    }

    @Before(MyGcbxValidator.class)
    public void save() {
        List<UploadFile> files = getFiles();
        Gcbx gcbx = getBean(Gcbx.class, true);
        gcbx.setUsername(getLoginAccount().getUserName());
        gcbx.setTxsj(formatPureDate(new Date()));
        Ret ret = srv.save(gcbx);
        String wj = ControllerKit.upLoadFiles(files, "gcbx", trueString(gcbx.getWj()));
        gcbx.setWj(wj);
        gcbx.update();
        updateDdgk();
        renderJson(ret.set("id", gcbx.getId()));
    }

    public void edit() {
        keepPara();
        Gcbx gcbx = srv.findById(getParaToInt("id"));
        setAttr("gcbx", gcbx);
        setAttr("isAdd", false);
        setAttr("ddList", spSrv.getDdList());
        setAttr("skrList", Db.find("select distinct skr,khh,khzh from gcbx order by skr"));
        boolean hasRole = AdminAuthService.me.hasRole(getLoginAccountId(), new String[]{"权限管理员", "超级管理员", "总经理"});
        if (hasRole) {
            setAttr("khList", Db.find("select id,jc,hb from kh order by jc"));
        } else {
            setAttr("khList", Db.find("select id,jc,hb from kh where ywy like '%" + getLoginAccount().getUserName() + "%' order by jc"));
        }
        setAttr("accountList", projectService.getAccountList());
        String wj = StringKit.trueString(gcbx.getWj());
        if (!StringUtils.isEmpty(wj)) {
            String[] wjList = StringKit.trueString(wj).split(";");
            setAttr("wjList", wjList);
        }
        render("add_edit.html");
    }

    @Before(MyGcbxValidator.class)
    public void update() {
        List<UploadFile> files = getFiles();
        Gcbx gcbx = getBean(Gcbx.class, true);
        gcbx.setUsername(getLoginAccount().getUserName());
        String wj = ControllerKit.upLoadFiles(files, "gcbx", trueString(gcbx.getWj()));
        gcbx.setWj(wj);
        Ret ret = srv.update(gcbx);
        updateDdgk();
        renderJson(ret.set("id", gcbx.getId()));
    }

    public void delete() {
        srv.delete(getParaToInt("id"));
        updateDdgk();
        renderJson(Ret.ok().set("msg", "删除成功!"));
    }

    public void deleteFile() {
        try {
            Integer id = getParaToInt("id");
            String wj = getPara("wj");
            Gcbx bx = srv.findById(id);
            String clWj = bx.getWj();
            bx.setWj(StringUtils.replace(clWj, wj + ";", ""));
            bx.update();
            FileUtils.deleteQuietly(new File(ControllerKit.UPLOAD + "/gcbx/" + wj));
            redirect("/my/gcbx/edit?id=" + id);
        } catch (Exception e) {
            renderJson(Ret.fail().set("msg", e.getMessage()));
            e.printStackTrace();
        }
    }
}
