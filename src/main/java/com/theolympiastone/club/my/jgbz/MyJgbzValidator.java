package com.theolympiastone.club.my.jgbz;

import com.jfinal.core.Controller;
import com.jfinal.validate.Validator;

public class MyJgbzValidator extends Validator {


    @Override
    protected void validate(Controller c) {
        setShortCircuit(true);
//        validateRequiredString("jgbz.id", "msg", "id不能为空");
        //$[isnotnull]
    }

    @Override
    protected void handleError(Controller c) {
        c.setAttr("state", "fail");
        c.renderJson();
    }
}
