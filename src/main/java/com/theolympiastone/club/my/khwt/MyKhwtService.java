package com.theolympiastone.club.my.khwt;

import com.alibaba.druid.util.StringUtils;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Page;
import com.theolympiastone.club._admin.auth.AdminAuthService;
import com.theolympiastone.club.common.model.Account;
import com.theolympiastone.club.common.model.Khwt;

public class MyKhwtService {
    public static final MyKhwtService me = new MyKhwtService();
    private final Khwt dao = new Khwt().dao();


    private String getSuffixString(Account account, String query, String queryKsrq, String queryJsrq, String queryPx) {
        boolean hasRole = AdminAuthService.me.hasRole(account.getId(), new String[]{"权限管理员", "超级管理员", "总经理"});
        String suffix = "";
        if (!StringUtils.isEmpty(query)) {
            suffix += " and jyms like '%" + query + "%' ";
        }
        if (!StringUtils.isEmpty(queryKsrq)) {
            suffix += " and rq >= '" + queryKsrq + "' ";
        }
        if (!StringUtils.isEmpty(queryJsrq)) {
            suffix += " and rq <= '" + queryJsrq + "' ";
        }
        suffix += queryPx;
        return suffix;
    }

    public Page<Khwt> paginate(int pageNum, Account account, String query, String queryKsrq, String queryJsrq, String queryPx) {
        String suffix = getSuffixString(account, query, queryKsrq, queryJsrq, queryPx);
        return dao.paginate(pageNum, 100, "select * ", "from khwt where 1=1 " + suffix + " ");
    }

    public Ret save(Khwt khwt) {
        khwt.save();
        return Ret.ok("msg", "创建成功");
    }

    public Khwt findById(Integer id) {
        return dao.findById(id);
    }

    public Ret update(Khwt khwt) {
        khwt.update();
        return Ret.ok("msg", "修改成功");
    }

    public Ret delete(Integer id) {
        dao.deleteById(id);
        return Ret.ok("msg", "删除成功");
    }
}
