package com.theolympiastone.club.my.kq_dk;

import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Page;
import com.theolympiastone.club._admin.auth.AdminAuthService;
import com.theolympiastone.club.common.model.Account;
import com.theolympiastone.club.common.model.KqDk;
import org.apache.commons.lang3.StringUtils;

public class MyKq_dkService {
    public static final MyKq_dkService me = new MyKq_dkService();
    private final KqDk dao = new KqDk().dao();

    private String getSuffixString(Account account, String query, String queryKsrq, String queryJsrq, String queryPx) {
        boolean hasRole = AdminAuthService.me.hasRole(account.getId(), new String[]{"权限管理员", "超级管理员", "总经理"});
        String suffix = "";
        if (!hasRole) {
            suffix += " and cjr='" + account.getUserName() + "' ";
        }
        if (!StringUtils.isEmpty(query)) {
            suffix += " and cjr like '%" + query + "%' ";
        }
        if (!StringUtils.isEmpty(queryKsrq)) {
            suffix += " and rq >= '" + queryKsrq + "' ";
        }
        if (!StringUtils.isEmpty(queryJsrq)) {
            suffix += " and rq <= '" + queryJsrq + "' ";
        }
        suffix += queryPx;
        return suffix;
    }

    public Page<KqDk> paginate(int pageNum, int pageSize, Account account, String query, String queryKsrq, String queryJsrq, String queryPx) {
        String suffix = getSuffixString(account, query, queryKsrq, queryJsrq, queryPx);
        return dao.paginate(pageNum, pageSize, "select * ", " from kq_dk where 1=1 " + suffix + " ");
    }

    public Ret save(KqDk kq_dk) {
        kq_dk.save();
        return Ret.ok("msg", "创建成功");
    }

    public KqDk findById(Integer id) {
        return dao.findById(id);
    }

    public Ret update(KqDk kq_dk) {
        kq_dk.update();
        return Ret.ok("msg", "修改成功");
    }

    public Ret delete(Integer id) {
        dao.deleteById(id);
        return Ret.ok("msg", "删除成功");
    }

    public KqDk getLastKqDk(String cjr, String sj) {
        System.out.println(sj);
        return dao.findFirst("select * from kq_dk where sj=(select max(sj) from kq_dk where cjr=? and sj>=?) and cjr=?", cjr, sj, cjr);
    }
}
