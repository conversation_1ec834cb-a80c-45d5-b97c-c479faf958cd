package com.theolympiastone.club.my.project;

import com.jfinal.aop.Before;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.theolympiastone.club.common.controller.BaseController;
import com.theolympiastone.club.common.interceptor.FrontAuthInterceptor;
import com.theolympiastone.club.common.model.Project;
import com.theolympiastone.club.common.safe.RestTime;

import java.util.List;

/**
 * 我的项目
 */
@Before({FrontAuthInterceptor.class})
public class MyProjectController extends BaseController {
    @Inject
    MyProjectService srv;

    /**
     * 暂时全部显示，不分页
     */
    public void index() {
        keepPara();
        String px = getPara("px");
        List<Project> projectList = srv.findAll(px);
        setAttr("projectList", projectList);
        render("index.html");
    }

    public void add() {
        render("add.html");
    }

    @Before({MyProjectValidator.class, RestTime.class})
    public void save() {
        Project project = getModel(Project.class);
        srv.save(getLoginAccountId(), project);
        renderJson(Ret.ok("保存成功!").set("id", project.getId()));
    }

    public void edit() {
        Project project = srv.findById(getLoginAccountId(), getParaToInt("id"));
        setAttr("project", project);
        render("edit.html");
    }

    @Before(MyProjectValidator.class)
    public void update() {
        Project project = getModel(Project.class);
        srv.update(getLoginAccountId(), project);
        renderJson(Ret.ok("更新成功!").set("id", project.getId()));
    }

    public void delete() {
        srv.delete(getLoginAccountId(), getParaToInt("id"));
        renderJson(Ret.ok().set("msg", "删除成功!"));
    }
}