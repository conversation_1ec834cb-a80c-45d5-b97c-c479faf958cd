package com.theolympiastone.club.my.project;

import com.jfinal.core.Controller;
import com.jfinal.validate.Validator;

/**
 * 我的项目表单提交校验
 */
public class MyProjectValidator extends Validator {

    protected void validate(Controller c) {
        setShortCircuit(true);

        validateString("project.name", 3, 20, "msg", "项目名称长度要求在3到20个字符");

        String projectName = c.getPara("project.name");
        // 创建项目
        if ("save".equals(getActionMethod().getName())) {
            if (MyProjectService.me.isProjectNameExists(projectName)) {
                addError("msg", "项目名称已经存在，请使用其她名称");
            }
        }
        // 修改项目
        else if ("update".equals(getActionMethod().getName())) {
            int projectId = c.getParaToInt("project.id");
            if (MyProjectService.me.isProjectNameExists(projectId, projectName)) {
                addError("msg", "项目名称已经存在，请使用其她名称");
            }
        } else {
            addError("msg", "MyProjectValidator 只能用于 save、update 方法");
        }

        validateString("project.title", 3, 100, "msg", "标题长度要求在3到100个字符");
        validateString("project.content", 19, 16777215, "msg", "正文内容太少或者太多啦，多写点哈");
    }

    protected void handleError(Controller c) {
        c.renderJson();
    }
}
