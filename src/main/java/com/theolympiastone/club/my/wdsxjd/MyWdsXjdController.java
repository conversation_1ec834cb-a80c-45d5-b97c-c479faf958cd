package com.theolympiastone.club.my.wdsxjd;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.jfinal.aop.Before;
import com.jfinal.aop.Inject;
import com.jfinal.kit.JsonKit;
import com.jfinal.kit.LogKit;
import com.jfinal.kit.PathKit;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import com.jfinal.upload.UploadFile;
import com.theolympiastone.club._admin.auth.AdminAuthService;
import com.theolympiastone.club.common.controller.BaseController;
import com.theolympiastone.club.common.interceptor.FrontAuthInterceptor;
import com.theolympiastone.club.common.kit.*;
import com.theolympiastone.club.common.model.Account;
import com.theolympiastone.club.common.model.WdsXjd;
import com.theolympiastone.club.common.model.WdsXjdmx;
import com.theolympiastone.club.common.model.WdsYxkh;
import com.theolympiastone.club.my.common.RzKit;
import com.theolympiastone.club.my.dd.ExportDdData;
import com.theolympiastone.club.my.wdsyxkh.MyWdsYxkhService;
import net.coobird.thumbnailator.Thumbnails;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.attribute.BasicFileAttributes;
import java.nio.file.attribute.FileTime;
import java.util.*;

import static com.theolympiastone.club.common.kit.CollectionKit.listToJson;
import static com.theolympiastone.club.common.kit.RequestKit.getIpAddr;
import static com.theolympiastone.club.common.kit.StringKit.*;
import static com.theolympiastone.club.my.dd.DdmxLikeExportData.toMx;

@Before({FrontAuthInterceptor.class})
public class MyWdsXjdController extends BaseController {
    @Inject
    MyXjdService srv;
    @Inject
    MyWdsYxkhService yxkhSrv;

    public static String getPath() {
        return Objects.requireNonNull(MyWdsXjdController.class.getResource("/")).getPath();
    }

    private static List<File> getFiles(File[] files) {
        List<File> fList = Arrays.asList(files);
        fList.sort((o1, o2) -> {
            try {
                FileTime fileTime1 = Files.readAttributes(Paths.get(o1.getPath()), BasicFileAttributes.class).creationTime();
                FileTime fileTime2 = Files.readAttributes(Paths.get(o2.getPath()), BasicFileAttributes.class).creationTime();
                return fileTime2.compareTo(fileTime1);
            } catch (IOException e) {
                e.printStackTrace();
            }
            return 0;
        });
        return fList;
    }

    public void searchKh() {
        String keyword = getPara("keyword");
        Account loginAccount = getLoginAccount();
        List<Record> records;
        List<Map<String, String>> resultMapList = Lists.newArrayList();
        boolean hasRole = AdminAuthService.me.hasRole(loginAccount.getId(), new String[]{"权限管理员", "超级管理员", "总经理", "阿旭"});
        if (hasRole) {
            records = Db.find("select id value, gsmc name from wds_yxkh where gsmc like '%" + keyword + "%' order by gsmc");
        } else {
            records = Db.find("select id value, gsmc name from wds_yxkh where fg like '%" + loginAccount.getUserName() + "%' and gsmc like '%" + keyword + "%' order by gsmc");
        }
        for (Record record : records) {
            resultMapList.add(ImmutableMap.of("name", record.getStr("value") + ": " + record.getStr("name"), "value", record.getStr("value")));
        }
        String data = new Gson().toJson(resultMapList);
        renderJson(Ret.ok().set("data", data));
    }

    public void index() {
        keepPara();
        String query = getPara("query", "");
        String queryKh = getPara("queryKh", "");
        String queryGcdd = getPara("queryGcdd", "");
        String queryKhdd = getPara("queryKhdd", "");
        String querySz = getPara("querySz", "");
        String queryPm = getPara("queryPm", "");
        String khid = getPara("khid", "");
        setAttr("query", query);
        setAttr("queryGcdd", queryGcdd);
        setAttr("queryKhdd", queryKhdd);
        setAttr("querySz", querySz);
        setAttr("queryPm", queryPm);
        setAttr("khid", khid);
        Page<Record> ddPage;
        boolean hasRole = AdminAuthService.me.hasRole(getLoginAccountId(), new String[]{"权限管理员", "超级管理员", "总经理", "阿旭"});
        if (hasRole) {
            ddPage = srv.paginate(getParaToInt("p", 1), query, khid, queryGcdd, queryKhdd, queryPm, querySz);
        } else {
            ddPage = srv.paginate(getParaToInt("p", 1), getLoginAccount().getUserName(), query, khid, queryGcdd, queryKhdd, queryPm, querySz);
        }
        setAttr("page", ddPage);
        render("index.html");
    }

    public void add() {
        keepPara();
        setAttr("dataObject", "[{}]");
        setAttr("isAdd", true);
        Integer khid = getParaToInt("khid", 0);
        if (khid != 0) {
            WdsYxkh yxkh = yxkhSrv.findById(khid);
            setAttr("xjd.khszg", trueString(yxkh.getGb()));
            setAttr("xjd.khszcs", trueString(yxkh.getShi()));
            setAttr("xjd.ddujsjl", trueString(yxkh.getDdujsjl()));
        }
        toAddEdit();
    }

    public void khXj() {
        setAttr("dataObject", "[{}]");
        setAttr("isAdd", true);
        toAddEdit();
    }

    private void toAddEdit() {
        List<String> szList = Db.query("select mc from cl order by mc");
        setAttr("szList", JsonKit.toJson(szList));
        List<String> pmList = Db.query("select mc from zl order by mc");
        setAttr("pmList", JsonKit.toJson(pmList));
        setAttr("jgfsList", listToJson(Db.find("select mc from jgfs order by mc"), "mc"));
        render("add_edit.html");
    }

    public void edit() {
        Integer id = getParaToInt("id");
        String ddbh = getPara("ddbh");
        String px = getPara("px", "按工程名称顺序");
        setAttr("px", px);
        WdsXjd dd;
        if (id != null) {
            dd = srv.findXjdById(id);
        } else if (!StringUtils.isEmpty(ddbh)) {
            dd = srv.findByXjdbh(ddbh);
        } else {
            renderJson("查不到该订单号。联系老柳。<EMAIL>");
            return;
        }
        ddbh = dd.getDdbh();
        String gsmc = Db.queryFirst("select gsmc from wds_yxkh where id=?", dd.getKhid());
        setAttr("gsmc", gsmc);
        String userName = getLoginAccount().getUserName();
        boolean hasRole = AdminAuthService.me.hasRole(getLoginAccountId(), new String[]{"权限管理员", "超级管理员", "总经理", "阿旭", "WDS管理员"});
        if (!hasRole) {
            String s = Db.queryStr("select 1 from wds_yxkh where id=? and fg is null or fg like '%" + userName + "%'", dd.getKhid());
            if (StringUtils.isEmpty(s) || "null".equalsIgnoreCase(s)) {
                renderJson("没有权限查看该订单。联系老柳。<EMAIL>");
                return;
            }
        }
        String uploadPath = PathKit.getWebRootPath() + "/upload/wds_xjd_draw/" + id;
        File file = new File(uploadPath);
        File[] files = file.listFiles();
        if (files != null) {
            setAttr("tps", files.length);
        }
        String importError = getPara("importError", "");

        List<WdsXjdmx> ddmxs = srv.findXjdmxList(ddbh, px);

        setAttr("dataObject", new Gson().toJson(ddmxs).replace("{\"columns\":", "").replace("}}]", "}]").replace("}},{", "},{").replace("data: ", "").replace("{\"attrs\":", ""));
        setAttr("isAdd", false);
        List<String> errorList = Db.query("select distinct zwszm from wds_xjdmx where ddbh=? and zwszm not in (select mc from cl)", ddbh);
        if (errorList != null && !errorList.isEmpty()) {
            importError += StringUtils.join(errorList, ",") + ": 不是系统里面设置的中文石种名字.";
        }
        if (!StringUtils.isEmpty(importError)) {
            setAttr("importError", importError);
        }
        setAttr("xjd", dd);
        toAddEdit();
    }

    public void xgddh() {
        String ddbh = getPara("ddbh");
        String xddbh = getPara("xddbh");
        if (StringUtils.isEmpty(xddbh) || StringUtils.isEmpty(ddbh)) {
            renderText("修改失败，新旧订单号都不能放空。");
            return;
        }
        xddbh = xddbh.replace("'", " ");
        List<String> sqlList = Lists.newArrayList();
        sqlList.add("update wds_xjd set ddbh='" + xddbh + "' where ddbh='" + ddbh + "';");
        sqlList.add("update wds_xjdmx set ddbh='" + xddbh + "' where ddbh='" + ddbh + "';");
        Db.batch(sqlList, sqlList.size());
        renderText("订单编号: " + ddbh + ", 更新为: " + xddbh + " 成功。");
    }

    public void savemx() {
        try {
            String ddbh = getPara("ddbh");
            String data = getPara("data");
            Boolean mxmodify = getParaToBoolean("mxmodify", true);
            WdsXjd dd = getModel(WdsXjd.class, "xjd");
            if (dd.getId() != null) {
                dd.update();
            } else {
                dd.setLrr(getLoginAccount().getUserName());
                dd.setLrsj(formatSmipleDate(new Date()));
                dd.setLrfs(formatSmipleDate(new Date()));
                dd.save();
            }
            if (!mxmodify) {
                renderJson(Ret.ok().set("ddbh", ddbh).set("msg", "保存成功!").set("id", dd.getId()));
                return;
            }
            List<ExportDdData> dataList = JSON.parseArray(data, ExportDdData.class);

            List<Record> szList = Db.find("select ywmc, mc from cl order by mc");
            List<Record> pmList = Db.find("select ywmc, mc from zl order by mc");
            Map<String, String> szMap = Maps.newHashMap();
            Map<String, String> pmMap = Maps.newHashMap();
            Map<String, String> jgfsMap = Maps.newHashMap();

            for (Record record : szList) {
                szMap.put(record.get("mc"), record.get("ywmc"));
            }

            for (Record record : pmList) {
                pmMap.put(record.get("mc"), record.get("ywmc"));
            }
            for (ExportDdData exportDdData : dataList) {
                String gcdd = exportDdData.getGcdd();
                if (StringUtils.isEmpty(gcdd) || (!StringUtils.isEmpty(gcdd) && "订单总计".equalsIgnoreCase(gcdd))) {
                    continue;
                }
                WdsXjdmx ddmx = new WdsXjdmx();
                ddmx = (WdsXjdmx) toMx(exportDdData, ddmx);
                ddmx.setDdbh(ddbh);
                ddmx.setYwszm(szMap.get(exportDdData.getZwszm()));
                ddmx.setYwpm(pmMap.get(exportDdData.getZwpm()));
                if (ddmx.getId() != null) {
                    ddmx.update();
                } else {
                    ddmx.save();
                }
            }
            renderJson(Ret.ok().set("ddbh", ddbh).set("msg", "保存成功!").set("id", dd.getId()));
        } catch (Exception e) {
            renderJson(Ret.fail().set("msg", e.getMessage()));
            e.printStackTrace();
        }
    }

    public void delete() {
        String ddbh = getPara("ddbh");
        srv.delete(ddbh);
        redirect("/my/wds_xjd");
    }

    public void delmx() {
        String ddbh = getPara("ddbh");
        Ret ret = srv.deleteMx(ddbh);
        renderJson(ret.set("ddbh", ddbh));
    }

    public void removeRow() {
        String id = getPara("id");
        Db.update("delete from wds_xjdmx where id in " + id);
        renderJson(Ret.ok().set("msg", "删除成功！"));
    }

    public String getMc(Map<String, String> map, String key) {
        key = mvBlank(key).toUpperCase();
        if (StringUtils.isEmpty(key)) {
            return null;
        }
        for (String s : map.keySet()) {
            if (s.contains("##")) {
                List<String> keyList = arrToList(s, "##");
                if (keyList.contains(key)) {
                    return map.get(s);
                }
            } else {
                if (StringUtils.equals(s.trim().toUpperCase(), key)) {
                    return map.get(s);
                }
            }
        }
        return null;
    }

    private String getZwjgbz(Map<String, String> map, String ywjg) {
        if (StringUtils.isEmpty(ywjg)) {
            return null;
        }
        String line = StringKit.mvMoreBlank(ywjg);

        if (map.containsKey(line)) {
            return map.get(line);
        } else {
            return getZwjgbz(map, line.substring(0, line.lastIndexOf(" ") - 1));
        }
    }

    public void importFile() throws UnsupportedEncodingException {
        UploadFile file = getFile();
        WdsXjd dd = getModel(WdsXjd.class, "xjd");
        if (dd.getId() != null) {
            dd.update();
        }
        List<Record> jgbzList = Db.find("select ywmc,zwmc from jgbz");
        Map<String, String> jgbzMap = Maps.newHashMap();
        for (Record record : jgbzList) {
            jgbzMap.put(record.getStr("ywmc"), record.getStr("zwmc"));
        }

        List<Record> zlRecordList = Db.find("select distinct a.mc, a.ywmc, a.xh from (select mc mc, mc ywmc, 1 xh from zl union all select mc, ywmc ywmc, 2 xh from zl union all select mc, bz ywmc, 3 xh from zl) a where a.ywmc is not null order by a.xh asc");
        Map<String, String> zlMap = genDataMap(zlRecordList);

        List<Record> clRecordList = Db.find("select distinct a.mc, a.ywmc, a.xh from (select mc mc, mc ywmc, 1 xh from cl union all select mc, ywmc ywmc, 2 xh from cl union all select mc, bz ywmc, 3 xh from cl) a where a.ywmc is not null order by a.xh asc");
        Map<String, String> clMap = genDataMap(clRecordList);

        String import_file_id = dd.getDdbh();
        DataListener<ExportDdData> dataListener = new DataListener<>();
        try {
            EasyExcel.read(file.getFile(), ExportDdData.class, dataListener).headRowNumber(1).sheet(0).doRead();
        } catch (Exception e) {
            redirect("/my/wds_xjd/edit?ddbh=" + URLEncoder.encode(import_file_id, "UTF-8").replaceAll("\\+", "%20") + "&importError=" + e.getMessage());
            e.printStackTrace();
            return;
        }
        List<ExportDdData> list = dataListener.getList();

        int listSize = list.size();
        List<WdsXjdmx> ddmxes = Lists.newArrayList();
        for (ExportDdData exportDdData : list) {
            String gcdd = trueXlsString(exportDdData.getGcdd());
            if ("订单总计".equalsIgnoreCase(gcdd)) {
                continue;
            }
            WdsXjdmx ddmx = new WdsXjdmx();
            ddmx = (WdsXjdmx) toMx(exportDdData, ddmx);
            ddmx.setDdbh(import_file_id);
            ddmx.setKhsz(exportDdData.getYwszm());
            ddmx.setKhpm(exportDdData.getYwpm());
            ddmx.setKhjg(exportDdData.getYwjgff());
            String zwszm = trueString(exportDdData.getZwszm());
            if (StringUtils.isEmpty(zwszm)) {
                ddmx.setZwszm(getMc(clMap, exportDdData.getYwszm()));
            }
            String zwpm = trueString(exportDdData.getZwpm());
            if (StringUtils.isEmpty(zwpm)) {
                ddmx.setZwpm(getMc(zlMap, exportDdData.getYwpm()));
            }
            String zwjgff = trueString(exportDdData.getZwjgff());
            if (!StringUtils.isEmpty(ddmx.getGcdd())) {
                ddmxes.add(ddmx);
            }
        }
        try {
            for (WdsXjdmx ddmx : ddmxes) {
                ddmx.save();
            }
            redirect("/my/wds_xjd/edit?ddbh=" + URLEncoder.encode(import_file_id, "UTF-8").replaceAll("\\+", "%20"));
        } catch (Exception e) {
            redirect("/my/wds_xjd/edit?ddbh=" + URLEncoder.encode(import_file_id, "UTF-8").replaceAll("\\+", "%20") + "&importError=" + e.getMessage());
            e.printStackTrace();
        }
    }

    private Map<String, String> genDataMap(List<Record> jgfsRecordList) {
        Map<String, String> map = Maps.newHashMap();
        for (Record record : jgfsRecordList) {
            String key = record.getStr("ywmc").trim();
            if (!map.containsKey(key)) {
                map.put(key, record.getStr("mc"));
            }
        }
        return map;
    }

    public void exportFile() {
        String ddbh = getPara("ddbh");
        String px = getPara("px", "按工程名称顺序");
        setAttr("px", px);
        String hb = getPara("hb");
        String mb = getPara("mb", "Blank");

        String fileName = srv.exportFile(ddbh, px, hb, mb);
        if (StringUtils.containsIgnoreCase(mb, "PDF")) {
            PdfKit.exclToPdf(PathKit.getWebRootPath() + "/upload/" + fileName);
            fileName = fileName.replace(".xls", ".pdf");
        }
        renderFile(new File(PathKit.getWebRootPath() + "/upload/" + fileName));
    }

    public void uploadFile() {
        List<UploadFile> files = getFiles();
        String upload_id = getPara("upload_id");
        String uploadPath = PathKit.getWebRootPath() + "/upload/" + "dd/" + upload_id;

        if (files == null || files.isEmpty() || StringUtils.isEmpty(upload_id)) {
            redirect("/my/wds_xjd/edit?ddbh=" + upload_id);
            return;
        }
        try {
            FileUtils.forceMkdir(new File(uploadPath));
        } catch (IOException e) {
            e.printStackTrace();
        }
        WdsXjd dd = srv.findByXjdbh(upload_id);
        StringBuilder wj = new StringBuilder(trueString(dd.getWj()));
        for (UploadFile file : files) {
            try {
                String fileName = file.getFileName();
                File destFile = new File(uploadPath + "/" + fileName);
                FileUtils.deleteQuietly(destFile);
                FileUtils.moveFile(file.getFile(), destFile);
                wj.append(fileName).append(";");
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        dd.setWj(wj.toString());
        dd.update();
        redirect("/my/wds_xjd/edit?ddbh=" + upload_id);
    }

    public void deleteFile() {
        try {
            String ddbh = getPara("ddbh");
            String wj = getPara("wj");
            WdsXjd dd = srv.findByXjdbh(ddbh);
            String ddWj = dd.getWj();
            dd.setWj(StringUtils.replace(ddWj, wj + ";", ""));
            dd.update();
            renderJson(Ret.ok().set("msg", "删除成功！").set("id", dd.getId()));
        } catch (Exception e) {
            renderJson(Ret.fail().set("msg", e.getMessage()));
            e.printStackTrace();
        }
    }

    public void importDraws() {
        List<UploadFile> files = getFiles();
        Integer id = getInt("id");
        WdsXjd xjd = srv.findXjdById(id);
        String uploadPath = PathKit.getWebRootPath() + "/upload/wds_xjd_draw/" + id;

        if (files == null || files.isEmpty() || id == null) {
            renderJson(Ret.fail().set("msg", "上传失败!"));
            return;
        }
        try {
            FileUtils.forceMkdir(new File(uploadPath));
        } catch (IOException e) {
            e.printStackTrace();
        }
        List<String> successFiles = Lists.newArrayList();
        List<String> failFiles = Lists.newArrayList();
        for (UploadFile file : files) {
            String fileName = file.getOriginalFileName();
            File fileFile = file.getFile();
            long srcSize = FileUtils.sizeOf(fileFile);
            try {
                File destFile = new File(uploadPath + "/" + fileName);
                FileUtils.deleteQuietly(destFile);
                FileUtils.moveFile(fileFile, destFile);
                if (srcSize > 1024 * 1024 && srcSize <= 2 * 1024 * 1024) {
                    Thumbnails.of(destFile).scale(1F).outputQuality(0.9F).toFile(destFile);
                    long destFileSize = FileUtils.sizeOf(destFile);
                    LogKit.info("【图片压缩】fileName={} | 图片原大小={}kb | 压缩后大小={}kb",
                            fileName,
                            srcSize / 1024,
                            destFileSize / 1024);
                } else if (srcSize > 2 * 1024 * 1024 && srcSize <= 5 * 1024 * 1024) {
                    Thumbnails.of(destFile).scale(1F).outputQuality(0.6F).toFile(destFile);
                    long destFileSize = FileUtils.sizeOf(destFile);
                    LogKit.info("【图片压缩】fileName={} | 图片原大小={}kb | 压缩后大小={}kb",
                            fileName,
                            srcSize / 1024,
                            destFileSize / 1024);
                } else if (srcSize > 5 * 1024 * 1024) {
                    Thumbnails.of(destFile).scale(1F).outputQuality(0.4F).toFile(destFile);
                    long destFileSize = FileUtils.sizeOf(destFile);
                    LogKit.info("【图片压缩】fileName={} | 图片原大小={}kb | 压缩后大小={}kb",
                            fileName,
                            srcSize / 1024,
                            destFileSize / 1024);
                }
                successFiles.add(fileName);
            } catch (IOException e) {
                failFiles.add(fileName);
                e.printStackTrace();
            }
        }
        RzKit.ddrz(xjd.getDdbh(), "询价单编号: " + xjd.getDdbh() + ", 图纸:" + StringUtils.join(successFiles, ",") + " 上传成功.", getLoginAccountId(), getIpAddr(getRequest()));
        setAttr("importError", "上传成功: " + StringUtils.join(successFiles, ",") + ", 上传失败: " + StringUtils.join(failFiles));
        renderJson(Ret.ok());
    }

    public void editDraws() {
        Integer id = getInt("id");
        String ddbh = get("ddbh");
        String uploadPath = PathKit.getWebRootPath() + "/upload/wds_xjd_draw/" + id;
        File file = new File(uploadPath);
        File[] files = file.listFiles();
        List<String> fileList = Lists.newArrayList();
        if (files != null) {
            List<File> fList = getFiles(files);
            for (File f : fList) {
                fileList.add(f.getName());
            }
        }
        setAttr("id", id);
        setAttr("ddbh", ddbh);
        setAttr("files", fileList);
        render("editDraws.html");
    }

    public void uploadImage() {
        Integer id = getInt("id");
        String ddbh = get("ddbh");
        String preImage = get("preImage");
        String image = get("image");
        if (id == null) {
            renderJson(Ret.fail().set("msg", "找不到该订单!"));
            return;
        }
        if (StringUtils.isEmpty(preImage)) {
            renderJson(Ret.fail().set("msg", "原图像找不到!"));
            return;
        }
        if (StringUtils.isEmpty(image)) {
            renderJson(Ret.fail().set("msg", "涂鸦图片错误!"));
            return;
        }
        System.out.println(image.length());
        String[] split = image.split(",");
        String preImagePath = preImage.split("\\?")[0];
        if (split.length > 1) {
            Base64Utils.GenerateImage(split[1], PathKit.getWebRootPath() + preImagePath);
        } else {
            Base64Utils.GenerateImage(image, PathKit.getWebRootPath() + preImagePath);
        }
//        System.out.println(image);
        RzKit.ddrz(ddbh, "询价单编号: " + ddbh + ", 图纸:" + preImage + " 附加费填写成功.", getLoginAccountId(), getIpAddr(getRequest()));
        renderJson(Ret.ok());
//        redirect("/my/xjd/editDraws?id=" + id + "&ddbh=" + URLEncoder.encode(ddbh, "UTF-8"));
    }

    public void fjfwc() throws UnsupportedEncodingException {
        Integer id = getInt("id");
        String ddbh = get("ddbh");
        String ywyEmails = Db.queryStr("select fg from wds_yxkh where id=(select khid from xjd where ddbh=?)", ddbh);
        EmailKit.sendEmail(ywyEmails, "询价单附加费填写完成, " + ddbh, "<a href='http://360.theolympiastone.com/my/wds_xjd/editDraws?id=" + id + "&ddbh=" + URLEncoder.encode(ddbh, "UTF-8") + "' target='_blank')快速跳转到附加费页面</a>");
        setAttr("msg", "发送邮件成功");
        redirect("/my/wds_xjd/editDraws?id=" + id + "&ddbh=" + URLEncoder.encode(ddbh, "UTF-8"));
    }

    public void tpscwc() throws UnsupportedEncodingException {
        Integer id = getInt("id");
        String ddbh = get("ddbh");
        String uploadPath = PathKit.getWebRootPath() + "/upload/wds_xjd_draw/" + id;
        File file = new File(uploadPath);
        File[] files = file.listFiles();
        List<String> fileList = Lists.newArrayList();
        if (files != null) {
            for (File f : files) {
                fileList.add(f.getName());
            }
        }
        Collections.sort(fileList);

        EmailKit.sendEmail(Lists.newArrayList("<EMAIL>"), getLoginAccountUserName() + ", " + ddbh + " 图纸上传完成, ", "<a href='http://360.theolympiastone.com/my/wds_xjd/editDraws?id=" + id + "&ddbh=" + URLEncoder.encode(ddbh, "UTF-8") + "' target='_blank'>快速跳转到附加费填写页面</a>", "/xjd_draw/" + id, fileList);
        setAttr("msg", "发送邮件成功");
        redirect("/my/wds_xjd/editDraws?id=" + id + "&ddbh=" + URLEncoder.encode(ddbh, "UTF-8"));
    }

    public void saveAll() {
        Integer id = getInt("id");
        String preImage = get("preImage");
        String image = get("image");
        if (id == null) {
            renderJson(Ret.fail().set("msg", "找不到该订单!"));
            return;
        }
        if (StringUtils.isEmpty(preImage)) {
            renderJson(Ret.fail().set("msg", "原图像找不到!"));
            return;
        }
        if (StringUtils.isEmpty(image)) {
            renderJson(Ret.fail().set("msg", "涂鸦图片错误!"));
            return;
        }
        String[] split = image.split(",");
        if (split.length > 1) {
            Base64Utils.GenerateImage(split[1], PathKit.getWebRootPath() + preImage);
        } else {
            Base64Utils.GenerateImage(image, PathKit.getWebRootPath() + preImage);
        }
//        System.out.println(image);
        renderJson(Ret.ok());
//        redirect("/my/xjd/editDraws?id=" + id + "&ddbh=" + URLEncoder.encode(ddbh, "UTF-8"));
    }

    public void deleteDraw() throws UnsupportedEncodingException {
        Integer id = getInt("id");
        String ddbh = get("ddbh");
        String fileName = urlDecode(get("fileName", ""));
        String deleteFile = PathKit.getWebRootPath() + "/upload/wds_xjd_draw/" + id + "/" + fileName;
        FileUtils.deleteQuietly(new File(deleteFile));
        RzKit.ddrz(ddbh, "删除图纸: " + fileName, getLoginAccountId(), getIpAddr(getRequest()));
        redirect("/my/wds_xjd/editDraws?id=" + id + "&ddbh=" + URLEncoder.encode(ddbh, "UTF-8"));
    }
}
