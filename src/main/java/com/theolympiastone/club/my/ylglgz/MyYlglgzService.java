package com.theolympiastone.club.my.ylglgz;

import com.alibaba.druid.util.StringUtils;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Page;
import com.theolympiastone.club.common.model.Ylglgz;

public class MyYlglgzService {
    public static final MyYlglgzService me = new MyYlglgzService();
    private final Ylglgz dao = new Ylglgz().dao();


    private String getSuffixString(String query, String queryKsrq, String queryJsrq, String queryPx) {
        String suffix = "";
        if (!StringUtils.isEmpty(query)) {
            suffix += " and xm like '%" + query + "%' ";
        }
        if (!StringUtils.isEmpty(queryKsrq)) {
            suffix += " and yf >= '" + queryKsrq + "' ";
        }
        if (!StringUtils.isEmpty(queryJsrq)) {
            suffix += " and yf <= '" + queryJsrq + "' ";
        }
        suffix += queryPx;
        return suffix;
    }

    public Page<Ylglgz> paginate(int pageNum, String query, String queryKsrq, String queryJsrq, String queryPx) {
        String suffix = getSuffixString(query, queryKsrq, queryJsrq, queryPx);
        return dao.paginate(pageNum, 100, "select * ", "from ylglgz where 1=1 " + suffix + " ");
    }

    public Ret save(Ylglgz ylglgz) {
        ylglgz.save();
        return Ret.ok("msg", "创建成功");
    }

    public Ylglgz findById(Integer id) {
        return dao.findById(id);
    }

    public Ret update(Ylglgz ylglgz) {
        ylglgz.update();
        return Ret.ok("msg", "修改成功");
    }

    public Ret delete(Integer id) {
        dao.deleteById(id);
        return Ret.ok("msg", "删除成功");
    }
}
