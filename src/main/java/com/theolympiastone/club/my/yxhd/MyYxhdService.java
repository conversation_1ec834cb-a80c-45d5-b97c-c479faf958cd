package com.theolympiastone.club.my.yxhd;

import com.alibaba.druid.util.StringUtils;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Page;
import com.theolympiastone.club._admin.auth.AdminAuthService;
import com.theolympiastone.club.common.model.Account;
import com.theolympiastone.club.common.model.Yxhd;

public class MyYxhdService {
    public static final MyYxhdService me = new MyYxhdService();
    private final Yxhd dao = new Yxhd().dao();


    private String getSuffixString(Account account, String query, String queryKsrq, String queryJsrq, String queryPx) {
        boolean hasRole = AdminAuthService.me.hasRole(account.getId(), new String[]{"权限管理员", "超级管理员", "总经理"});
        String suffix = "";
        if (!hasRole) {
            suffix += " and l.cjr='" + account.getUserName() + "' ";
        }
        if (!StringUtils.isEmpty(query)) {
            suffix += " and l.mc like '%" + query + "%' ";
        }
        if (!StringUtils.isEmpty(queryKsrq)) {
            suffix += " and l.cjsj >= '" + queryKsrq + "' ";
        }
        if (!StringUtils.isEmpty(queryJsrq)) {
            suffix += " and l.cjsj <= '" + queryJsrq + "' ";
        }
        suffix += queryPx;
        return suffix;
    }

    public Page<Yxhd> paginate(int pageNum, Account account, String query, String queryKsrq, String queryJsrq, String queryPx) {
        String suffix = getSuffixString(account, query, queryKsrq, queryJsrq, queryPx);
        return dao.paginate(pageNum, 100, "select y.*, j.mc yj, l.mc lb ", "from yxhd y, yj j, lxrlb l where y.yjid=j.id and y.lbid=l.id " + suffix + " ");
    }

    public Ret save(Yxhd yxhd) {
        yxhd.save();
        return Ret.ok("msg", "创建成功");
    }

    public Yxhd findById(Integer id) {
        return dao.findById(id);
    }

    public Ret update(Yxhd yxhd) {
        yxhd.update();
        return Ret.ok("msg", "修改成功");
    }

    public Ret delete(Integer id) {
        dao.deleteById(id);
        return Ret.ok("msg", "删除成功");
    }
}
