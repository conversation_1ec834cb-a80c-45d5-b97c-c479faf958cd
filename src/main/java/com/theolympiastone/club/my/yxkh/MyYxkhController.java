package com.theolympiastone.club.my.yxkh;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.gson.Gson;
import com.jfinal.aop.Before;
import com.jfinal.aop.Inject;
import com.jfinal.kit.JsonKit;
import com.jfinal.kit.Kv;
import com.jfinal.kit.PathKit;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.ActiveRecordPlugin;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import com.jfinal.plugin.druid.DruidPlugin;
import com.jfinal.template.Engine;
import com.jfinal.template.Template;
import com.jfinal.upload.UploadFile;
import com.theolympiastone.club._admin.auth.AdminAuthService;
import com.theolympiastone.club.common.LayGsonBuilder;
import com.theolympiastone.club.common.LayRecordData;
import com.theolympiastone.club.common.OSClubConfig;
import com.theolympiastone.club.common.OSConstants;
import com.theolympiastone.club.common.account.AccountService;
import com.theolympiastone.club.common.controller.BaseController;
import com.theolympiastone.club.common.interceptor.FrontAuthInterceptor;
import com.theolympiastone.club.common.kit.*;
import com.theolympiastone.club.common.model.*;
import com.theolympiastone.club.my.common.CommonUtils;
import com.theolympiastone.club.my.yj.MyYjService;
import com.theolympiastone.club.project.ProjectService;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;

import static com.theolympiastone.club.common.CacheKit.KeyEnum.KH;
import static com.theolympiastone.club.common.CacheKit.KeyEnum.YXKH;
import static com.theolympiastone.club.common.CacheKit.refreshCacheTable;
import static com.theolympiastone.club.common.kit.ControllerKit.genFile;
import static com.theolympiastone.club.common.kit.PinyinKit.toPinyin;
import static com.theolympiastone.club.common.kit.StringKit.*;

@Before({FrontAuthInterceptor.class})
public class MyYxkhController extends BaseController {
    private static final List<Map<String, String>> pxList = Lists.newArrayList();

    static {
        pxList.add(ImmutableMap.of("name", "名称顺序", "value", " order by gsmc asc"));
        pxList.add(ImmutableMap.of("name", "名称逆序", "value", " order by gsmc desc"));
        pxList.add(ImmutableMap.of("name", "沟通逆序", "value", " order by a.gtsj desc"));
        pxList.add(ImmutableMap.of("name", "沟通顺序", "value", " order by a.gtsj asc"));
        pxList.add(ImmutableMap.of("name", "编辑逆序", "value", " order by zjxgsj desc"));
        pxList.add(ImmutableMap.of("name", "编辑顺序", "value", " order by zjxgsj asc"));
        pxList.add(ImmutableMap.of("name", "星星等级逆序", "value", " order by zydj desc"));
        pxList.add(ImmutableMap.of("name", "星星等级顺序", "value", " order by zydj asc"));
    }

    @Inject
    MyYxkhService srv;
    @Inject
    MyYjService yjSrv;
    @Inject
    AccountService accountService;
    @Inject
    ProjectService projectService;

    public void getData() {
        Integer limit = getParaToInt("limit", 100);
        Integer page = getParaToInt("page", 1);
        String query = getPara("query", "");
        String queryZhou = getPara("queryZhou", "");
        String querySfqy = getPara("querySfqy", "否");
        String queryBq = getPara("queryBq", "");
        String queryGb = getPara("queryGb", "");
        String queryKhxz = getPara("queryKhxz", "");
        String queryZycp = getPara("queryZycp", "");
        String queryYhz = getPara("queryYhz", "");
        String queryEmail = getPara("queryEmail", "");
        String queryBz2 = getPara("queryBz2", "");
        String queryFg = getPara("queryFg", "");
        String queryPx = getPara("queryPx", pxList.get(0).get("value"));
        String queryGtjy = getPara("queryGtjy", "");
        String queryJczyx = getPara("queryJczyx", "");
        String queryWz = getPara("queryWz", "");
        String queryStars = getPara("queryStars", "");
        setAttr("query", query);
        setAttr("queryZhou", queryZhou);
        setAttr("querySfqy", querySfqy);
        setAttr("queryBq", queryBq);
        setAttr("queryGb", queryGb);
        setAttr("queryPx", queryPx);
        setAttr("queryKhxz", queryKhxz);
        setAttr("queryZycp", queryZycp);
        setAttr("queryYhz", queryYhz);
        setAttr("queryEmail", queryEmail);
        setAttr("queryBz2", queryBz2);
        setAttr("queryFg", queryFg);
        setAttr("queryGtjy", queryGtjy);
        setAttr("queryJczyx", queryJczyx);
        setAttr("queryWz", queryWz);
        setAttr("queryStars", queryStars);
        setAttr("pxList", pxList);
        System.out.println(new Date());
        Page<Record> pageData = srv.getManagerDataList(page, limit, getLoginAccount(), query, queryZhou, querySfqy, queryBq, queryGb, queryEmail, queryBz2, queryFg, queryKhxz, queryZycp, queryYhz, queryGtjy, queryJczyx, queryWz, queryStars, queryPx);
        System.out.println(new Date());
        String jsonText = LayGsonBuilder.create().toJson(new LayRecordData(pageData.getList(), pageData.getTotalRow()));
        System.out.println(new Date());
        List<Record> gtRecords = Db.find("select concat(substr(m.day_short_desc, 6,5), ' 周', substr(m.week_desc, 3, 1)) rq,m.jr,m.fj, ifnull(g.jls, 0) jls " +
                "from m_dim_day m " +
                "         left join (select gtsj, count(*) jls from yxkh_gtjy where ywy =? group by gtsj) g " +
                "                   on m.day_short_desc = g.gtsj " +
                "where day_short_desc >= DATE_SUB(CURDATE(), INTERVAL 13 DAY) " +
                "  and day_short_desc <= CURDATE();", getLoginAccount().getUserName());
        setAttr("gtRecords", gtRecords);
        renderJson(jsonText);
    }

    public void getgtRecords() {
        String ywy = get("ywy", getLoginAccount().getUserName());
        List<Record> gtRecords = Db.find("select concat(substr(m.day_short_desc, 6,5), ' 周', substr(m.week_desc, 3, 1)) rq,m.jr,m.fj, ifnull(g.jls, 0) jls " +
                "from m_dim_day m " +
                "         left join (select gtsj, count(*) jls from yxkh_gtjy where ywy =? and jy is not null and jy<>'' and jy not like '%给客户发了%' and jy not like '%未接%' and jy not like '%打不通%' and jy not like '%未打通%' and jy not like '%转语音%' and jy not like '%无法接通%' and jy not like '%无人接%' group by gtsj) g " +
                "                   on m.day_short_desc = g.gtsj " +
                "where day_short_desc >= DATE_SUB(CURDATE(), INTERVAL 11 DAY) " +
                "  and day_short_desc <= CURDATE();", ywy);
        String gtLastMonthRecords = Db.queryStr("select count(*) jls " +
                "from yxkh_gtjy " +
                "where ywy = ? " +
                "  and jy is not null and jy<>'' and jy not like '%给客户发了%' " +
                "  and jy not like '%未接%' " +
                "  and jy not like '%打不通%' " +
                "  and jy not like '%未打通%' " +
                "  and jy not like '%转语音%' " +
                "  and jy not like '%无法接通%' " +
                "  and jy not like '%无人接%' " +
                "  and gtsj like concat(DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 1 MONTH), '%Y-%m'), '%');", ywy);
        String gtYearRecords = Db.queryStr("select count(*) jls " +
                "from yxkh_gtjy " +
                "where ywy = ? " +
                "  and jy is not null and jy<>'' and jy not like '%给客户发了%' " +
                "  and jy not like '%未接%' " +
                "  and jy not like '%打不通%' " +
                "  and jy not like '%未打通%' " +
                "  and jy not like '%转语音%' " +
                "  and jy not like '%无法接通%' " +
                "  and jy not like '%无人接%' " +
                "  and gtsj like concat(substr(CURDATE(), 1, 4), '%');", ywy);
        renderJson(Ret.ok().set("gtRecordJson", JsonKit.toJson(gtRecords)).set("gtLastMonthRecords", gtLastMonthRecords).set("gtYearRecords", gtYearRecords));
    }


    public void gb() {
        String query = getPara("query", "");
        String queryZhou = getPara("queryZhou", "");
        String querySfqy = getPara("querySfqy", "否");
        String queryBq = getPara("queryBq", "");
        String queryGb = getPara("queryGb", "");
        String queryKhxz = getPara("queryKhxz", "");
        String queryZycp = getPara("queryZycp", "");
        String queryYhz = getPara("queryYhz", "");
        String queryEmail = getPara("queryEmail", "");
        String queryBz2 = getPara("queryBz2", "");
        String queryFg = getPara("queryFg", "");
        String queryPx = getPara("queryPx", pxList.get(0).get("value"));
        setAttr("query", query);
        setAttr("queryZhou", queryZhou);
        setAttr("querySfqy", querySfqy);
        setAttr("queryBq", queryBq);
        setAttr("queryGb", queryGb);
        setAttr("queryPx", queryPx);
        setAttr("queryKhxz", queryKhxz);
        setAttr("queryZycp", queryZycp);
        setAttr("queryYhz", queryYhz);
        setAttr("queryEmail", queryEmail);
        setAttr("queryBz2", queryBz2);
        setAttr("queryFg", queryFg);
        setAttr("pxList", pxList);
        boolean hasRole = AdminAuthService.me.hasRole(getLoginAccount().getId(), new String[]{"权限管理员", "超级管理员", "总经理", "阿旭"});

        String suffix = "";
        if (!StringUtils.isEmpty(query)) {
            query = query.replace("'", "\\'");
            suffix += " and (gsmc like '%" + query + "%' or jcz like '%" + query + "%') ";
        }
        if (!StringUtils.isEmpty(querySfqy)) {
            suffix += " and sfqy='" + querySfqy + "' ";
        }
        if (!StringUtils.isEmpty(queryBq)) {
            suffix += " and bq like '%" + queryBq + "%' ";
        }
        if (!StringUtils.isEmpty(queryGb)) {
            suffix += " and gb like '%" + queryGb + "%' ";
        }
        if (!StringUtils.isEmpty(queryEmail)) {
            suffix += " and jczyx like '%" + queryEmail + "%' ";
        }
        if (!StringUtils.isEmpty(queryKhxz)) {
            suffix += " and khxz like '%" + queryKhxz + "%' ";
        }
        if (!StringUtils.isEmpty(queryZycp)) {
            suffix += " and zycp like '%" + queryZycp + "%' ";
        }
        if (!StringUtils.isEmpty(queryFg)) {
            suffix += " and fg like '%" + queryFg + "%' ";
        }

        List<Record> gbRecords = Lists.newArrayList();
        if (hasRole) {
            gbRecords = Db.find("select gb,count(*) sl from yxkh where 1=1 " + suffix + " group by gb order by gb");
        } else {
            String gb = getLoginAccount().getGb();
            if (!StringUtils.isEmpty(gb)) {
                Gson gson = new Gson();
                Object[] array = gson.fromJson(gb, Object[].class);
                String inPlaceholders = StringUtils.repeat("?,", array.length - 1) + "?";
                String sql = "SELECT gb,count(*) sl FROM yxkh WHERE gb IN (" + inPlaceholders + ") " + suffix + " group by gb order by gb";
                gbRecords = Db.find(sql, array);
            }
        }
        gbRecords.sort((o1, o2) -> {
            String gb1 = o1.getStr("gb");
            String gb2 = o2.getStr("gb");
            return toPinyin(gb1).compareTo(toPinyin(gb2));
        });
        setAttr("gbRecords", gbRecords);

        List<Record> msGbRecords = Lists.newArrayList();
        if (hasRole) {
            msGbRecords = Db.find("select gb,count(*) sl from yxkh where 1=1 and zycp like '%墓碑%' " + suffix + " group by gb order by gb");
        } else {
            String gb = getLoginAccount().getGb();
            if (!StringUtils.isEmpty(gb)) {
                Gson gson = new Gson();
                Object[] array = gson.fromJson(gb, Object[].class);
                String inPlaceholders = StringUtils.repeat("?,", array.length - 1) + "?";
                String sql = "SELECT gb,count(*) sl FROM yxkh WHERE gb IN (" + inPlaceholders + ") and zycp like '%墓碑%' " + suffix + " group by gb order by gb";
                msGbRecords = Db.find(sql, array);
            }
        }
        msGbRecords.sort((o1, o2) -> {
            String gb1 = o1.getStr("gb");
            String gb2 = o2.getStr("gb");
            return toPinyin(gb1).compareTo(toPinyin(gb2));
        });
        setAttr("msGbRecords", msGbRecords);

        List<Record> recordList = Db.find("select ifnull(zycp, '') zycp, count(*) sl  from yxkh where 1=1 " + suffix + " group by zycp");
        Map<String, Integer> zycpMap = Maps.newHashMap();
        for (Record record : recordList) {
            String zycp = record.getStr("zycp");
            Integer sl = record.getInt("sl");
            String[] split = zycp.split("、");
            for (String s : split) {
                zycpMap.put(s, zycpMap.getOrDefault(s, 0) + sl);
            }

        }
        List<Map<String, Object>> zycpList = Lists.newArrayList();
        for (Map.Entry<String, Integer> entry : zycpMap.entrySet()) {
            Map<String, Object> map = Maps.newHashMap();
            map.put("zycp", entry.getKey());
            map.put("sl", entry.getValue());
            zycpList.add(map);
        }
        zycpList.sort((o1, o2) -> {
            String zycp1 = (String) o1.get("zycp");
            String zycp2 = (String) o2.get("zycp");
            return toPinyin(zycp1).compareTo(toPinyin(zycp2));
        });
        setAttr("zycpRecords", zycpList);
        render("gb.html");
    }

    public void index() {
        keepPara();
        List<Record> gtRecords = Db.find("select concat(substr(m.day_short_desc, 6,5), ' 周', substr(m.week_desc, 3, 1)) rq,m.jr,m.fj, ifnull(g.jls, 0) jls " +
                "from m_dim_day m " +
                "         left join (select gtsj, count(*) jls from yxkh_gtjy where ywy =? and jy is not null and jy<>'' and jy not like '%给客户发了%' and jy not like '%未接%' and jy not like '%打不通%' and jy not like '%未打通%' and jy not like '%转语音%' and jy not like '%无法接通%' and jy not like '%无人接%' group by gtsj) g " +
                "                   on m.day_short_desc = g.gtsj " +
                "where day_short_desc >= DATE_SUB(CURDATE(), INTERVAL 11 DAY) " +
                "  and day_short_desc <= CURDATE();", getLoginAccount().getUserName());
        setAttr("gtRecords", gtRecords);
        setAttr("pxList", pxList);
        String gtLastMonthRecords = Db.queryStr("select count(*) jls " +
                "from yxkh_gtjy " +
                "where ywy = ? " +
                "  and jy is not null and jy<>'' and jy not like '%给客户发了%' " +
                "  and jy not like '%未接%' " +
                "  and jy not like '%打不通%' " +
                "  and jy not like '%未打通%' " +
                "  and jy not like '%转语音%' " +
                "  and jy not like '%无法接通%' " +
                "  and jy not like '%无人接%' " +
                "  and gtsj like concat(DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 1 MONTH), '%Y-%m'), '%');", getLoginAccount().getUserName());
        setAttr("gtLastMonthRecords", gtLastMonthRecords);
        setAttr("pxList", pxList);
        String gtYearRecords = Db.queryStr("select count(*) jls " +
                "from yxkh_gtjy " +
                "where ywy = ? " +
                "  and jy is not null and jy<>'' and jy not like '%给客户发了%' " +
                "  and jy not like '%未接%' " +
                "  and jy not like '%打不通%' " +
                "  and jy not like '%未打通%' " +
                "  and jy not like '%转语音%' " +
                "  and jy not like '%无法接通%' " +
                "  and jy not like '%无人接%' " +
                "  and gtsj like concat(substr(CURDATE(), 1, 4), '%');", getLoginAccount().getUserName());
        setAttr("gtYearRecords", gtYearRecords);
        setAttr("pxList", pxList);
        setAttr("gbList", Db.find("select distinct gb from yxkh order by gb"));
        render("index.html");
    }

    public void add() {
        setAttr("isAdd", true);
        setAttr("gbList", Db.find("select distinct gb from yxkh order by gb"));
        setAttr("accountList", projectService.getAccountListJson());
        setAttr("wl", getWl()); // 设置内外网判断

        // 检测是否为移动端
        if (isMobileDevice()) {
            render("add_edit_mobile.html");
        } else {
            render("add_edit.html");
        }
    }

    public void changeJczyx() {
        String id = get("id");
        String jczyx = get("jczyx", "我存在不存在");
        String[] jczyxSplit = jczyx.split("[,;]");
        List<Record> khlxrList = Db.find("select jc, yx from vw_khlxr where jc in (select jc from kh where ifnull(zt, '已合作')='已合作');");
        Map<String, String> khlxrMap = Maps.newHashMap();
        for (Record record : khlxrList) {
            khlxrMap.put(record.getStr("yx"), record.getStr("jc"));
        }
        Set<String> khlxrKeySet = khlxrMap.keySet();
        Set<String> jczyxSet = new HashSet<>(Arrays.asList(jczyxSplit));
        String msg = "";
        for (String s : jczyxSet) {
            s = trueString(s).trim();
            if (khlxrKeySet.contains(s)) {
                msg = "邮箱 " + s + " 为已合作客户[" + khlxrMap.get(s) + "]的邮箱，不需要再创建！";
                break;
            }
            if (StringUtils.isEmpty(id)) {
                Record first = Db.findFirst("select id, gsmc,if(sfqy='是', '暂时不联系', '正常') sfqy, jczyx, fg from yxkh where jczyx like '%" + s + "%'");
                if (first != null) {
                    Integer jId = first.getInt("id");
                    String gsmc = first.getStr("gsmc");
                    String sfqy = first.getStr("sfqy");
                    msg = "邮箱 " + s + " 已在 " + gsmc + " 公司, <br>公司状态: " + sfqy + ", 可以直接在那边做编辑。<br><a href='/my/yxkh/edit?id=" + jId + "' target='_blank'>" + gsmc + "</a>";
                    break;
                }
                Record second = Db.findFirst("select id,gsmc,if(sfqy='是', '暂时不联系', '正常') sfqy, jczyx, fg from yxkh where id in (select khid from yxkhlxr where email like '%" + s + "%')");
                if (second != null) {
                    Integer jId = first.getInt("id");
                    String gsmc = second.getStr("gsmc");
                    String sfqy = second.getStr("sfqy");
                    String fg = second.getStr("fg");
                    msg = "邮箱 " + s + " 已在 " + gsmc + " 公司, <br>公司状态: " + sfqy + ", 可以直接在那边做编辑。<br><a href='/my/yxkh/edit?id=" + jId + "' target='_blank'>" + gsmc + "</a>";
                    break;
                }
            } else {
                Record first = Db.findFirst("select id,gsmc,if(sfqy='是', '暂时不联系', '正常') sfqy, jczyx, fg from yxkh where id<>? and jczyx like '%" + s + "%'", id);
                if (first != null) {
                    Integer jId = first.getInt("id");
                    String gsmc = first.getStr("gsmc");
                    String sfqy = first.getStr("sfqy");
                    String fg = first.getStr("fg");
                    msg = "邮箱 " + s + " 同时已在公司: " + gsmc + ", <br>公司状态: " + sfqy + ", 可以看下哪个客户的资料齐全一些，做合并，删掉资料少的。<br><a href='/my/yxkh/edit?id=" + jId + "' target='_blank'>" + gsmc + "</a>";
                    break;
                }
                Record second = Db.findFirst("select gsmc,if(sfqy='是', '暂时不联系', '正常') sfqy, jczyx, fg from yxkh where fg is not null and id in (select khid from yxkhlxr where khid<>? and email like '%" + s + "%')", id);
                if (second != null) {
                    Integer jId = first.getInt("id");
                    String gsmc = second.getStr("gsmc");
                    String sfqy = second.getStr("sfqy");
                    String fg = second.getStr("fg");
                    msg = "邮箱 " + s + " 同时已在公司: " + gsmc + ", <br>公司状态: " + sfqy + ", 可以看下哪个客户的资料齐全一些，做合并，删掉资料少的。<br><a href='/my/yxkh/edit?id=" + jId + "' target='_blank'>" + gsmc + "</a>";
                    break;
                }
            }
        }
        refreshCacheTable(YXKH);
        renderJson(Ret.ok().set("msg", msg));
    }

    @Before(MyYxkhValidator.class)
    public void save() {
        Yxkh yxkh = getBean(Yxkh.class, true);
        String gtjy = get("gtjy");
        String jczyx = trueString(yxkh.getJczyx());
        Integer qzbc = yxkh.getQzbc();
        List<Record> khlxrList = Db.find("select jc, yx from vw_khlxr where jc in (select jc from kh where ifnull(zt, '已合作')='已合作');");
        Map<String, String> khlxrMap = Maps.newHashMap();
        for (Record record : khlxrList) {
            khlxrMap.put(record.getStr("yx"), record.getStr("jc"));
        }
        Set<String> khlxrKeySet = khlxrMap.keySet();
        String msg = "";
        if (!StringUtils.isEmpty(jczyx) && (qzbc == null || qzbc == 0)) {
            String[] jczyxSplit = jczyx.split("[,;]");
            Set<String> jczyxSet = new HashSet<>(Arrays.asList(jczyxSplit));

            List<Record> records = Db.find("select id,gsmc,if(sfqy='是', '暂时不联系', '正常') sfqy, jczyx, fg from yxkh where id<>? and (jczyx is not null or jczyx<>'')", yxkh.getId());
            for (String s : jczyxSet) {
                if (khlxrKeySet.contains(s)) {
                    msg = "邮箱 " + s + " 为已合作客户[" + khlxrMap.get(s) + "]的邮箱，不需要再创建！";
                    renderJson(Ret.fail().set("msg", msg));
                    return;
                }
            }

            for (Record record : records) {
                String id = record.getStr("id");
                String gsmc = record.getStr("gsmc");
                String sfqy = record.getStr("sfqy");
                String xtJczyx = trueString(record.getStr("jczyx")).trim();
                for (String yx : jczyxSet) {
                    if (!StringUtils.isEmpty(yx) && xtJczyx.contains(yx.trim())) {
                        renderJson(Ret.fail().set("msg", "更新失败!<br>该决策者邮箱已在 " + gsmc + " 公司, <br>公司状态: " + sfqy
                                + "<br><a href='/my/yxkh/edit?id=" + id + "' target='_blank'>点击查看: " + gsmc + "</a>, 可以看下哪个客户的资料齐全一些，做合并，删掉资料少的。"

                        ));
                        return;
                    }
                }
            }
        }
        String userName = getLoginAccount().getUserName();
        yxkh.setCjr(userName);
        if (!"'<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'".contains(userName)) {
            yxkh.setFg(userName);
            yxkh.setZjxgsj(formatPureDate(new Date()));
        } else {
            yxkh.setZjxgsj(plusDay(new Date(), -30));
        }
        yxkh.setCjsj(formatPureDate(new Date()));
        Ret ret = srv.save(yxkh);
        if (!StringUtils.isEmpty(gtjy)) {
            Record yxkhGtjyRecord = Db.findFirst("select * from yxkh_gtjy where khid=? and gtsj=?", yxkh.getId(), yyyy_MM_dd());
            if (yxkhGtjyRecord == null) {
                YxkhGtjy yxkhGtjy = new YxkhGtjy();
                yxkhGtjy.setKhid(yxkh.getId());
                yxkhGtjy.setJy(gtjy);
                yxkhGtjy.setYwy(userName);
                yxkhGtjy.setGtsj(yyyy_MM_dd());
                yxkhGtjy.setGtfs("电话");
                yxkhGtjy.save();
            } else {
                Db.update("update yxkh_gtjy set jy=?, gtfs=? where khid=? and gtsj=?", gtjy, "电话", yxkh.getId(), yyyy_MM_dd());
            }
        }
        refreshCacheTable(YXKH);
        renderJson(ret.set("id", yxkh.getId()));
    }

    public void edit() {
        keepPara();
        Integer id = getParaToInt("id");
        String gsmc = get("gsmc");
        Yxkh yxkh;
        if (id != null) {
            yxkh = srv.findById(id);
        } else if (!StringUtils.isEmpty(gsmc)) {
            yxkh = srv.findByGsmc(gsmc);
        } else {
            renderText("没有对应的客户信息！");
            return;
        }
        List<Record> yxkhLxrList = Db.find("select * from yxkhlxr where khid=?", id);
        String gtjy = Db.queryStr("select jy from yxkh_gtjy where khid=? and ywy=? and gtsj=?", id, getLoginAccount().getUserName(), yyyy_MM_dd());
        setAttr("gtjy", trueString(gtjy));
        setAttr("yxkh", yxkh);
        setAttr("yxkhLxrList", yxkhLxrList);
        List<Record> khwtList = Db.find("select t.*, replace(t.jyms, '\n', '<br>') jyms_hh, replace(t.wtfl, '\n', '<br>') wtfl_hh, a.da ada, a.bz abz, a.qt aqt from khwt t left join khda a on t.id = a.wtid and a.khid =" + id + " order by t.px");
        setAttr("khwtList", khwtList);
        setAttr("isAdd", false);
        setAttr("khxjList", Db.find("select * from xjd where khid=?", id));
        if (yxkh != null) {
            String wj = trueString(yxkh.getWj());
            if (!StringUtils.isEmpty(wj)) {
                String[] wjList = trueString(wj).split(";");
                setAttr("wjList", wjList);
            }
        }
        setAttr("accountList", projectService.getAccountListJson());
        setAttr("wl", getWl()); // 设置内外网判断

        String userName = getLoginAccount().getUserName();
        List<String> usernameFollowList = Db.query("select username_follow from account_guanli where username=?", userName);
        if (usernameFollowList == null) {
            usernameFollowList = Lists.newArrayList();
        }
        usernameFollowList.add(userName);
        String usernameFollow = " ('" + StringUtils.join(usernameFollowList, "','") + "') ";
        List<Record> records;
        boolean hasRole = AdminAuthService.me.hasRole(getLoginAccount().getId(), new String[]{"权限管理员", "超级管理员", "总经理", "阿旭"});
        if (!hasRole) {
            records = Db.find("select * from yxkh_gtjy where khid=? and ywy in " + usernameFollow, id);
        } else {
            records = Db.find("select * from yxkh_gtjy where khid=?", id);
        }
        setAttr("gtjyList", records);
        setAttr("gbList", Db.find("select distinct gb from yxkh order by gb"));

        // 检测是否为移动端
        if (isMobileDevice()) {
            render("add_edit_mobile.html");
        } else {
            render("add_edit.html");
        }
    }

    public void editMobile() {
        keepPara();
        Integer id = getParaToInt("id");
        String gsmc = get("gsmc");
        Yxkh yxkh;
        if (id != null) {
            yxkh = srv.findById(id);
        } else if (!StringUtils.isEmpty(gsmc)) {
            yxkh = srv.findByGsmc(gsmc);
        } else {
            renderText("没有对应的客户信息！");
            return;
        }
        List<Record> yxkhLxrList = Db.find("select * from yxkhlxr where khid=?", id);
        String gtjy = Db.queryStr("select jy from yxkh_gtjy where khid=? and ywy=? and gtsj=?", id, getLoginAccount().getUserName(), yyyy_MM_dd());
        setAttr("gtjy", trueString(gtjy));
        setAttr("yxkh", yxkh);
        setAttr("yxkhLxrList", yxkhLxrList);
        List<Record> khwtList = Db.find("select t.*, replace(t.jyms, '\n', '<br>') jyms_hh, replace(t.wtfl, '\n', '<br>') wtfl_hh, a.da ada, a.bz abz, a.qt aqt from khwt t left join khda a on t.id = a.wtid and a.khid =" + id + " order by t.px");
        setAttr("khwtList", khwtList);
        setAttr("isAdd", false);
        setAttr("khxjList", Db.find("select * from xjd where khid=?", id));
        if (yxkh != null) {
            String wj = trueString(yxkh.getWj());
            if (!StringUtils.isEmpty(wj)) {
                String[] wjList = trueString(wj).split(";");
                setAttr("wjList", wjList);
            }
        }
        setAttr("accountList", projectService.getAccountListJson());

        String userName = getLoginAccount().getUserName();
        List<String> usernameFollowList = Db.query("select username_follow from account_guanli where username=?", userName);
        if (usernameFollowList == null) {
            usernameFollowList = Lists.newArrayList();
        }
        usernameFollowList.add(userName);
        String usernameFollow = " ('" + StringUtils.join(usernameFollowList, "','") + "') ";
        List<Record> records;
        boolean hasRole = AdminAuthService.me.hasRole(getLoginAccount().getId(), new String[]{"权限管理员", "超级管理员", "总经理", "阿旭"});
        if (!hasRole) {
            records = Db.find("select * from yxkh_gtjy where khid=? and ywy in " + usernameFollow, id);
        } else {
            records = Db.find("select * from yxkh_gtjy where khid=?", id);
        }
        setAttr("gtjyList", records);
        setAttr("gbList", Db.find("select distinct gb from yxkh order by gb"));

        render("add_edit_mobile.html");
    }

    public void connect() {
        keepPara();
        Integer id = getParaToInt("id");
        Yxkh yxkh = srv.findById(id);
        String bz3 = yxkh.getBz3();
        if (StringUtils.isEmpty(bz3)) {
            bz3 = "沟通时间: " + formatPureDate(new Date()) + "沟通人: " + getLoginAccount().getNickName();
        } else {
            bz3 += "\n沟通时间: " + formatPureDate(new Date()) + "沟通人: " + getLoginAccount().getNickName();
        }
        yxkh.setBz3(bz3);
        yxkh.setZjxgsj(formatPureDate(new Date()));
        yxkh.update();
        refreshCacheTable(YXKH);
        index();
    }

    @Before(MyYxkhValidator.class)
    public void update() {
        Yxkh yxkh = getBean(Yxkh.class, true);
        String gtjy = get("gtjy");
        String jczyx = trueString(yxkh.getJczyx());
        List<Record> khlxrList = Db.find("select jc, yx from vw_khlxr where jc in (select jc from kh where ifnull(zt, '已合作')='已合作');");
        Map<String, String> khlxrMap = Maps.newHashMap();
        for (Record record : khlxrList) {
            khlxrMap.put(record.getStr("yx"), record.getStr("jc"));
        }
        Set<String> khlxrKeySet = khlxrMap.keySet();
        String msg;
        Set<String> jczyxSet = Sets.newHashSet();
        if (!StringUtils.isEmpty(jczyx)) {
            String[] jczyxSplit = jczyx.split("[,;]");
            jczyxSet = new HashSet<>(Arrays.asList(jczyxSplit));
            for (String s : jczyxSet) {
                if (khlxrKeySet.contains(s)) {
                    msg = "邮箱 " + s + " 为已合作客户[" + khlxrMap.get(s) + "]的邮箱，不需要再创建！";
                    renderJson(Ret.fail().set("msg", msg));
                    return;
                }
            }
        }
        Integer yxkhId = yxkh.getId();
        if (!StringUtils.isEmpty(jczyx)) {
            List<Record> records = Db.find("select id, gsmc,if(sfqy='是', '暂时不联系', '正常') sfqy, jczyx, fg from yxkh where id<>? and (jczyx is not null or jczyx<>'')", yxkhId);
            for (Record record : records) {
                String id = record.getStr("id");
                String gsmc = record.getStr("gsmc");
                String sfqy = record.getStr("sfqy");
                String fg = trueString(record.getStr("fg"));
                String xtJczyx = trueString(record.getStr("jczyx")).trim();
                for (String yx : jczyxSet) {
                    if (!StringUtils.isEmpty(yx) && xtJczyx.contains(yx.trim())) {
                        renderJson(Ret.fail().set("msg", "更新失败!<br>该决策者邮箱已在 " + gsmc + " 公司, <br>公司状态: " + sfqy
                                + "<br><a href='/my/yxkh/edit?id=" + id + "' target='_blank'>点击查看:" + gsmc + "</a>, 可以看下哪个客户的资料齐全一些，做合并，删掉资料少的。"
                        ));
                        return;
                    }
                }
            }
        }

        String userName = getLoginAccount().getUserName();
        if (!"'<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'".contains(userName)) {
            yxkh.setFg(userName);
            yxkh.setZjxgsj(formatPureDate(new Date()));
        } else {
            yxkh.setZjxgsj(plusDay(new Date(), -30));
        }
        Ret ret = srv.update(yxkh);
        String yyyyMmDd = yyyy_MM_dd();
        if (!StringUtils.isEmpty(gtjy)) {
            Record yxkhGtjyRecord = Db.findFirst("select * from yxkh_gtjy where khid=? and gtsj=?", yxkhId, yyyyMmDd);
            if (yxkhGtjyRecord == null) {
                YxkhGtjy yxkhGtjy = new YxkhGtjy();
                yxkhGtjy.setKhid(yxkhId);
                yxkhGtjy.setJy(gtjy);
                yxkhGtjy.setYwy(userName);
                yxkhGtjy.setGtsj(yyyyMmDd);
                yxkhGtjy.setGtfs("电话");
                yxkhGtjy.save();
            } else {
                Db.update("update yxkh_gtjy set jy=?, gtfs=?, ywy=? where khid=? and gtsj=?", gtjy, "电话", userName, yxkhId, yyyyMmDd);
            }
        }
        refreshCacheTable(YXKH);
        YxkhBd bdByKhAndRq = srv.findBdByKhAndRq(yxkhId, yyyyMmDd);
        if (bdByKhAndRq == null) {
            YxkhBd yxkhBd = new YxkhBd();
            yxkhBd.setKhid(yxkhId);
            yxkhBd.setYwy(userName);
            yxkhBd.setBdrq(yyyyMmDd);
            yxkhBd.save();
        }
        renderJson(ret.set("id", yxkh.getId()));
    }

    public void delete() {
        Integer id = getParaToInt("id");
        srv.delete(id);
        String yyyyMmDd = yyyy_MM_dd();
        YxkhBd bdByKhAndRq = srv.findBdByKhAndRq(id, yyyyMmDd);
        if (bdByKhAndRq == null) {
            YxkhBd yxkhBd = new YxkhBd();
            yxkhBd.setKhid(id);
            yxkhBd.setYwy(getLoginAccount().getUserName());
            yxkhBd.setBdrq(yyyyMmDd);
            yxkhBd.save();
        }
        Db.delete("delete from yxkh_kh where yxkh_id=?", id);
        renderJson(Ret.ok().set("msg", "删除成功!"));
    }

    public void deleteLxr() {
        Integer id = getParaToInt("id");
        Integer yxkhlxrId = getParaToInt("yxkhlxrId");
        Db.delete("delete from yxkhlxr where id=?", yxkhlxrId);
        String yyyyMmDd = yyyy_MM_dd();
        YxkhBd bdByKhAndRq = srv.findBdByKhAndRq(id, yyyyMmDd);
        if (bdByKhAndRq == null) {
            YxkhBd yxkhBd = new YxkhBd();
            yxkhBd.setKhid(id);
            yxkhBd.setYwy(getLoginAccount().getUserName());
            yxkhBd.setBdrq(yyyyMmDd);
            yxkhBd.save();
        }
        redirect("/my/yxkh/edit?id=" + id);
    }

    public void addLxr() {
        Integer id = getParaToInt("id");
        Yxkhlxr lxr = getBean(Yxkhlxr.class, "yxkhlxr");
        lxr.setKhid(id);
        lxr.save();
        Yxkh yxkh = srv.findById(id);
        yxkh.setZjxgsj(formatPureDate(new Date()));
        yxkh.update();
        String yyyyMmDd = yyyy_MM_dd();
        YxkhBd bdByKhAndRq = srv.findBdByKhAndRq(id, yyyyMmDd);
        if (bdByKhAndRq == null) {
            YxkhBd yxkhBd = new YxkhBd();
            yxkhBd.setKhid(id);
            yxkhBd.setYwy(getLoginAccount().getUserName());
            yxkhBd.setBdrq(yyyyMmDd);
            yxkhBd.save();
        }
        refreshCacheTable(YXKH);
        renderJson(Ret.ok("增加成功!").set("id", lxr.getId()));
    }

    public void addXj() {
        Integer id = getParaToInt("id");
        String ddbh = getPara("ddbh");
        String bz = getPara("bz");
        Db.update("insert into khxj ( khid, ddbh, bz ) values (" + id + ", '" + ddbh + "','" + bz + "');");
        Yxkh yxkh = srv.findById(id);
        yxkh.setZjxgsj(formatPureDate(new Date()));
        yxkh.update();
        refreshCacheTable(YXKH);
        renderJson(Ret.ok("增加成功!"));
    }

    public void modifyXjbz() {
        Integer id = getParaToInt("id");
        String ddbh = getPara("ddbh");
        String bz = getPara("bz");
        Db.update("update khxj set bz='" + bz + "' where khid=" + id + " and ddbh='" + ddbh + "';");
        Yxkh yxkh = srv.findById(id);
        yxkh.setZjxgsj(formatPureDate(new Date()));
        yxkh.update();
        refreshCacheTable(YXKH);
        renderJson(Ret.ok());
    }

    public void modifyDa() {
        Integer id = getParaToInt("id");
        Integer wtid = getParaToInt("wtid");
        String da = getPara("da");
        Long one = Db.queryFirst("select 1 from khda where khid=" + id + " and wtid=" + wtid + ";");
        if (one == null) {
            Db.update("insert into khda (khid, wtid, da) values (" + id + "," + wtid + ",'" + da + "');");
        } else {
            Db.update("update khda set da='" + da + "' where khid=" + id + " and wtid=" + wtid + ";");
        }
        Yxkh yxkh = srv.findById(id);
        yxkh.setZjxgsj(formatPureDate(new Date()));
        yxkh.update();
        String yyyyMmDd = yyyy_MM_dd();
        YxkhBd bdByKhAndRq = srv.findBdByKhAndRq(id, yyyyMmDd);
        if (bdByKhAndRq == null) {
            YxkhBd yxkhBd = new YxkhBd();
            yxkhBd.setKhid(id);
            yxkhBd.setYwy(getLoginAccount().getUserName());
            yxkhBd.setBdrq(yyyyMmDd);
            yxkhBd.save();
        }
        refreshCacheTable(YXKH);
        renderJson(Ret.ok());
    }

    public void modifyQt() {
        Integer id = getParaToInt("id");
        Integer wtid = getParaToInt("wtid");
        String qt = getPara("qt");
        Long one = Db.queryFirst("select 1 from khda where khid=" + id + " and wtid=" + wtid + ";");
        if (one == null) {
            Db.update("insert into khda (khid, wtid, qt) values (" + id + "," + wtid + ",'" + qt + "');");
        } else {
            Db.update("update khda set qt='" + qt + "' where khid=" + id + " and wtid=" + wtid + ";");
        }
        Yxkh yxkh = srv.findById(id);
        yxkh.setZjxgsj(formatPureDate(new Date()));
        yxkh.update();
        String yyyyMmDd = yyyy_MM_dd();
        YxkhBd bdByKhAndRq = srv.findBdByKhAndRq(id, yyyyMmDd);
        if (bdByKhAndRq == null) {
            YxkhBd yxkhBd = new YxkhBd();
            yxkhBd.setKhid(id);
            yxkhBd.setYwy(getLoginAccount().getUserName());
            yxkhBd.setBdrq(yyyyMmDd);
            yxkhBd.save();
        }
        refreshCacheTable(YXKH);
        renderJson(Ret.ok());
    }

    public void modifyBz() {
        Integer id = getParaToInt("id");
        Integer wtid = getParaToInt("wtid");
        String bz = getPara("bz");
        Long one = Db.queryFirst("select 1 from khda where khid=" + id + " and wtid=" + wtid + ";");
        if (one == null) {
            Db.update("insert into khda (khid, wtid, bz) values (" + id + "," + wtid + ",'" + bz + "');");
        } else {
            Db.update("update khda set bz='" + bz + "' where khid=" + id + " and wtid=" + wtid + ";");
        }
        Yxkh yxkh = srv.findById(id);
        yxkh.setZjxgsj(formatPureDate(new Date()));
        yxkh.update();
        String yyyyMmDd = yyyy_MM_dd();
        YxkhBd bdByKhAndRq = srv.findBdByKhAndRq(id, yyyyMmDd);
        if (bdByKhAndRq == null) {
            YxkhBd yxkhBd = new YxkhBd();
            yxkhBd.setKhid(id);
            yxkhBd.setYwy(getLoginAccount().getUserName());
            yxkhBd.setBdrq(yyyyMmDd);
            yxkhBd.save();
        }
        refreshCacheTable(YXKH);
        renderJson(Ret.ok());
    }

    public void uploadFile() {
        UploadFile file = getFile();
        List<Record> yxkhEmailList = Db.find("select id, jczyx from yxkh");
        List<String> txEmailList = Db.query("select yx from yjzt where zt='退信'");
        Map<String, Integer> idMap = Maps.newHashMap();
        Map<Integer, Set<String>> idEmailSetMap = Maps.newHashMap();
        Set<String> yxkhEmaiTrimlSet = Sets.newHashSet();
        for (Record yxkhRecord : yxkhEmailList) {
            Integer id = yxkhRecord.getInt("id");
            String[] yxkhEmailSplit = trueString(yxkhRecord.getStr("jczyx")).split("[;,]");
            Set<String> yxkhEmailSet = new HashSet<>(Arrays.asList(yxkhEmailSplit));
            idEmailSetMap.put(id, yxkhEmailSet);
            yxkhEmaiTrimlSet.addAll(yxkhEmailSet);
            for (String email : yxkhEmailSet) {
                idMap.put(email, id);
            }
        }
        DataMapListener<Map<Integer, Object>> dataListener = new DataMapListener<>(Lists.newArrayList(
                "gsmc", "wz", "khxz", "cgjgfs", "fjr", "gb", "zhou", "shi", "jczxb", "jcz", "jczyx", "jcsj", "facebook", "zycp", "zgys", "bjcp", "gm", "bz1", "bz2", "bz3", "dz"));
        List<List<String>> head = new ArrayList<>();
        EasyExcel.read(file.getFile(), dataListener).head(head).sheet(0).doRead();
        List<Map<String, Object>> list = dataListener.getList();
        List<Map<String, Object>> errorList = Lists.newArrayList();
        StringBuilder errorResult = new StringBuilder();
        int m = 0, x = 0, y = 0, z = 0;
        for (int i = 0; i < list.size(); i++) {
            int hs = i + 1;
            Map<String, Object> map = list.get(i);
            String jczyx = (String) map.get("jczyx");
            String[] jczyxSplit = jczyx.split("[;,]");
            Set<String> jczyxSet = new HashSet<>(Arrays.asList(jczyxSplit));
            boolean sfcf = false;
            boolean sftx = false;
            for (String yx : jczyxSet) {
                if (txEmailList.contains(yx)) {
                    errorResult.append("第 ").append(hs).append(" 行不导入。 公司名称为: ").append(map.get("gsmc").toString()).append(", 邮箱: ").append(jczyx).append(", 该邮箱为退信邮箱！").append("<br>");
                    sftx = true;
                    m++;
                    break;
                }
                if (!StringUtils.isAllBlank(yx) && (yxkhEmaiTrimlSet.contains(yx.trim()))) {
                    Integer id = idMap.get(yx);
                    Set<String> yxSet = idEmailSetMap.get(id);
                    jczyxSet.addAll(yxSet);
                    Db.update("update yxkh set jczyx='" + StringUtils.join(jczyxSet, ";") + "' where id=" + id + ";");
                    errorResult.append("第 ").append(hs).append(" 行不做重复导入。 公司名称为: ").append(map.get("gsmc").toString()).append(", 邮箱: ").append(jczyx).append(", 该邮箱已存在！").append("<br>");
                    x++;
                    sfcf = true;
                    break;
                }
            }
            if (sftx) {
                continue;
            }
            if (sfcf) {
                continue;
            }
            Yxkh yxkh = new Yxkh();
            String jczyxJoin = StringUtils.join(jczyxSet, ";");
            if (StringUtils.isAllBlank(jczyxJoin)) {
                y++;
                errorList.add(map);
                errorResult.append("第 ").append(hs).append(" 行导入失败。 公司名称为: ").append(map.get("gsmc").toString()).append(", 出错原因: 决策者邮箱是空的。<br>");
                continue;
            }
            map.put("jczyx", jczyxJoin);
            yxkh._setOrPut(map);
            yxkh.setCjr(getLoginAccount().getUserName());
            yxkh.setCjsj(formatPureDate(new Date()));
            yxkh.setZjxgsj(formatPureDate(new Date()));
            try {
                yxkh.save();
                yxkhEmaiTrimlSet.addAll(jczyxSet);
                idEmailSetMap.put(yxkh.getId(), jczyxSet);
                for (String email : jczyxSet) {
                    idMap.put(email, yxkh.getId());
                }
            } catch (Exception e) {
                e.printStackTrace();
                y++;
                errorList.add(map);
                errorResult.append("第 ").append(hs).append(" 行导入失败。 公司名称为: ").append(map.get("gsmc").toString()).append(", 出错原因: ").append(e.getLocalizedMessage()).append("<br>");
            }
            z++;
        }
        if (StringUtils.isEmpty(errorResult.toString())) {
            errorResult.append("全部导入成功！<br>");
        }
        errorResult.append("导入总条数: ").append(list.size()).append(", 退信条数: ").append(m).append(", 重复条数: ").append(x).append(", 错误条数: ").append(y).append(", 成功条数: ").append(z).append("<br>");
        DefaultRowHeightStyleStrategy defaultRowHeightStyleStrategy = new DefaultRowHeightStyleStrategy((short) 40, (short) 40);
        EmailKit.sendEmail(getLoginAccount().getUserName(), "营销客户总表导入结果", errorResult.toString());
        ExcelWriter excelWriter = EasyExcel.write(new File("/root/yxkh_" + formatFileDate(new Date()) + ".xls")).withTemplate(PathKit.getWebRootPath() + "/download/yxkh.xls").build();
        WriteSheet writeSheet = EasyExcel.writerSheet().registerWriteHandler(defaultRowHeightStyleStrategy).build();
        FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
        excelWriter.fill(errorList, fillConfig, writeSheet);
        excelWriter.finish();
        refreshCacheTable(YXKH);
        renderJson(Ret.ok().set("msg", errorResult.toString()));
    }

    public void uploadFileContainsEmpty() {
        UploadFile file = getFile();
        List<Record> yxkhEmailList = Db.find("select id, jczyx from yxkh");
        List<String> txEmailList = Db.query("select yx from yjzt where zt='退信'");
        Map<String, Integer> idMap = Maps.newHashMap();
        Map<Integer, Set<String>> idEmailSetMap = Maps.newHashMap();
        Set<String> yxkhEmaiTrimlSet = Sets.newHashSet();
        for (Record yxkhRecord : yxkhEmailList) {
            Integer id = yxkhRecord.getInt("id");
            String[] yxkhEmailSplit = trueString(yxkhRecord.getStr("jczyx")).split("[;,]");
            Set<String> yxkhEmailSet = new HashSet<>(Arrays.asList(yxkhEmailSplit));
            idEmailSetMap.put(id, yxkhEmailSet);
            yxkhEmaiTrimlSet.addAll(yxkhEmailSet);
            for (String email : yxkhEmailSet) {
                idMap.put(email, id);
            }
        }
        DataMapListener<Map<Integer, Object>> dataListener = new DataMapListener<>(Lists.newArrayList(
                "gsmc", "wz", "khxz", "cgjgfs", "fjr", "gb", "zhou", "shi", "jczxb", "jcz", "jczyx", "jcsj", "facebook", "zycp", "zgys", "bjcp", "gm", "bz1", "bz2", "bz3", "dz"));
        List<List<String>> head = new ArrayList<>();
        EasyExcel.read(file.getFile(), dataListener).head(head).sheet(0).doRead();
        List<Map<String, Object>> list = dataListener.getList();
        List<Map<String, Object>> errorList = Lists.newArrayList();
        StringBuilder errorResult = new StringBuilder();
        int m = 0, x = 0, y = 0, z = 0;
        for (int i = 0; i < list.size(); i++) {
            int hs = i + 1;
            Map<String, Object> map = list.get(i);
            String jczyx = trueString((String) map.get("jczyx"));
            String[] jczyxSplit = jczyx.split("[;,]");
            Set<String> jczyxSet = new HashSet<>(Arrays.asList(jczyxSplit));
            boolean sfcf = false;
            boolean sftx = false;
            for (String yx : jczyxSet) {
                if (txEmailList.contains(yx)) {
                    errorResult.append("第 ").append(hs).append(" 行不导入。 公司名称为: ").append(map.get("gsmc").toString()).append(", 邮箱: ").append(jczyx).append(", 该邮箱为退信邮箱！").append("<br>");
                    sftx = true;
                    m++;
                    break;
                }
                if (!StringUtils.isAllBlank(yx) && (yxkhEmaiTrimlSet.contains(yx.trim()))) {
                    Integer id = idMap.get(yx);
                    Set<String> yxSet = idEmailSetMap.get(id);
                    jczyxSet.addAll(yxSet);
                    Db.update("update yxkh set jczyx='" + StringUtils.join(jczyxSet, ";") + "' where id=" + id + ";");
                    errorResult.append("第 ").append(hs).append(" 行不做重复导入。 公司名称为: ").append(map.get("gsmc").toString()).append(", 邮箱: ").append(jczyx).append(", 该邮箱已存在！").append("<br>");
                    x++;
                    sfcf = true;
                    break;
                }
            }
            if (sftx) {
                continue;
            }
            if (sfcf) {
                continue;
            }
            Yxkh yxkh = new Yxkh();
            String jczyxJoin = StringUtils.join(jczyxSet, ";");
            if (StringUtils.isAllBlank(jczyxJoin)) {
                y++;
                errorList.add(map);
                errorResult.append("第 ").append(hs).append(" 行导入成功。 公司名称为: ").append(map.get("gsmc").toString()).append(", 出错原因: 决策者邮箱是空的。<br>");
//                continue;
            }
            map.put("jczyx", jczyxJoin);
            yxkh._setOrPut(map);
            yxkh.setCjr(getLoginAccount().getUserName());
            yxkh.setCjsj(formatPureDate(new Date()));
            yxkh.setZjxgsj(formatPureDate(new Date()));
            try {
                yxkh.save();
                yxkhEmaiTrimlSet.addAll(jczyxSet);
                idEmailSetMap.put(yxkh.getId(), jczyxSet);
                for (String email : jczyxSet) {
                    idMap.put(email, yxkh.getId());
                }
            } catch (Exception e) {
                e.printStackTrace();
                y++;
                errorList.add(map);
                errorResult.append("第 ").append(hs).append(" 行导入失败。 公司名称为: ").append(map.get("gsmc").toString()).append(", 出错原因: ").append(e.getLocalizedMessage()).append("<br>");
            }
            z++;
        }
        if (StringUtils.isEmpty(errorResult.toString())) {
            errorResult.append("全部导入成功！<br>");
        }
        errorResult.append("导入总条数: ").append(list.size()).append(", 退信条数: ").append(m).append(", 重复条数: ").append(x).append(", 错误条数: ").append(y).append(", 成功条数: ").append(z).append("<br>");
        DefaultRowHeightStyleStrategy defaultRowHeightStyleStrategy = new DefaultRowHeightStyleStrategy((short) 40, (short) 40);
        EmailKit.sendEmail(getLoginAccount().getUserName(), "营销客户总表导入结果", errorResult.toString());
        ExcelWriter excelWriter = EasyExcel.write(new File("/root/yxkh_" + formatFileDate(new Date()) + ".xls")).withTemplate(PathKit.getWebRootPath() + "/download/yxkh.xls").build();
        WriteSheet writeSheet = EasyExcel.writerSheet().registerWriteHandler(defaultRowHeightStyleStrategy).build();
        FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
        excelWriter.fill(errorList, fillConfig, writeSheet);
        excelWriter.finish();
        refreshCacheTable(YXKH);
        renderJson(Ret.ok().set("msg", errorResult.toString()));
    }

    public void uploadMoreFile() {
        UploadFile file = getFile();
        List<Record> yxkhEmailList = Db.find("select id, jczyx from yxkh");
        List<String> txEmailList = Db.query("select yx from yjzt where zt='退信'");
        Map<String, Integer> idMap = Maps.newHashMap();
        Map<Integer, Set<String>> idEmailSetMap = Maps.newHashMap();
        Set<String> yxkhEmaiTrimlSet = Sets.newHashSet();
        for (Record yxkhRecord : yxkhEmailList) {
            Integer id = yxkhRecord.getInt("id");
            String jczyx = trueString(yxkhRecord.getStr("jczyx"));
            String[] yxkhEmailSplit = jczyx.split("[;,]");
            Set<String> yxkhEmailSet = new HashSet<>(Arrays.asList(yxkhEmailSplit));
            idEmailSetMap.put(id, yxkhEmailSet);
            yxkhEmaiTrimlSet.addAll(yxkhEmailSet);
            for (String email : yxkhEmailSet) {
                idMap.put(email, id);
            }
        }
        DataMapListener<Map<Integer, Object>> dataListener = new DataMapListener<>(Lists.newArrayList(
                "gsmc", "wz", "khxz", "cgjgfs", "fjr", "gb", "zhou", "shi", "jczxb", "jcz", "jczyx", "lxr", "jcsj", "facebook", "zycp", "zgys", "bjcp", "gm", "bz1", "bz2", "bz3", "dz"));
        List<List<String>> head = new ArrayList<>();
        EasyExcel.read(file.getFile(), dataListener).head(head).sheet(0).doRead();
        List<Map<String, Object>> list = dataListener.getList();
        List<Map<String, Object>> errorList = Lists.newArrayList();
        StringBuilder errorResult = new StringBuilder();
        int m = 0, x = 0, y = 0, z = 0;
        for (int i = 0; i < list.size(); i++) {
            int hs = i + 1;
            Map<String, Object> map = list.get(i);
            String jczyx = trueString((String) map.get("jczyx"));
            String lxr = trueString((String) map.get("lxr"));
            String[] jczyxSplit = (jczyx + ";" + lxr).split("[;,]");
            Set<String> jczyxSet = new HashSet<>(Arrays.asList(jczyxSplit));
            boolean sfcf = false;
            boolean sftx = false;
            for (String yx : jczyxSet) {
                if (txEmailList.contains(yx)) {
                    errorResult.append("第 ").append(hs).append(" 行不导入。 公司名称为: ").append(map.get("gsmc").toString()).append(", 邮箱: ").append(jczyx).append(", 该邮箱为退���邮箱！").append("<br>");
                    sftx = true;
                    m++;
                    break;
                }
                if (!StringUtils.isAllBlank(yx) && (yxkhEmaiTrimlSet.contains(yx.trim()))) {
                    Integer id = idMap.get(yx);
                    Set<String> yxSet = idEmailSetMap.get(id);
                    jczyxSet.addAll(yxSet);
                    Db.update("update yxkh set jczyx='" + StringUtils.join(jczyxSet, ";") + "' where id=" + id + ";");
                    errorResult.append("第 ").append(hs).append(" 行不做重复导入。 公司名称为: ").append(map.get("gsmc").toString()).append(", 邮箱: ").append(jczyx).append(", 该邮箱已存在！").append("<br>");
                    x++;
                    sfcf = true;
                    break;
                }
            }
            if (sftx) {
                continue;
            }
            if (sfcf) {
                continue;
            }
            Yxkh yxkh = new Yxkh();
            String jczyxJoin = StringUtils.join(jczyxSet, ";");
            if (StringUtils.isAllBlank(jczyxJoin)) {
                y++;
                errorList.add(map);
                errorResult.append("第 ").append(hs).append(" 行导入失败。 公司名称为: ").append(map.get("gsmc").toString()).append(", 出错原因: 决策者邮箱是空的。<br>");
                continue;
            }
            map.put("jczyx", jczyxJoin);
            yxkh._setOrPut(map);
            yxkh.setCjr(getLoginAccount().getUserName());
            yxkh.setCjsj(formatPureDate(new Date()));
            yxkh.setZjxgsj(formatPureDate(new Date()));
            try {
                yxkh.save();
                yxkhEmaiTrimlSet.addAll(jczyxSet);
                idEmailSetMap.put(yxkh.getId(), jczyxSet);
                for (String email : jczyxSet) {
                    idMap.put(email, yxkh.getId());
                }
            } catch (Exception e) {
                e.printStackTrace();
                y++;
                errorList.add(map);
                errorResult.append("第 ").append(hs).append(" 行导入失败。 公司名称为: ").append(map.get("gsmc").toString()).append(", 出错原因: ").append(e.getLocalizedMessage()).append("<br>");
            }
            z++;
        }
        if (StringUtils.isEmpty(errorResult.toString())) {
            errorResult.append("全部导入成功！<br>");
        }
        errorResult.append("导入总条数: ").append(list.size()).append(", 退信条数: ").append(m).append(", 重复条数: ").append(x).append(", 错误条数: ").append(y).append(", 成功条数: ").append(z).append("<br>");
        DefaultRowHeightStyleStrategy defaultRowHeightStyleStrategy = new DefaultRowHeightStyleStrategy((short) 40, (short) 40);
        EmailKit.sendEmail(getLoginAccount().getUserName(), "营销客户总表导入结果", errorResult.toString());
        ExcelWriter excelWriter = EasyExcel.write(new File("/root/yxkh_" + formatFileDate(new Date()) + ".xls")).withTemplate(PathKit.getWebRootPath() + "/download/yxkh.xls").build();
        WriteSheet writeSheet = EasyExcel.writerSheet().registerWriteHandler(defaultRowHeightStyleStrategy).build();
        FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
        excelWriter.fill(errorList, fillConfig, writeSheet);
        excelWriter.finish();
        refreshCacheTable(YXKH);
        renderJson(Ret.ok().set("msg", errorResult.toString()));
    }

    public void redu() {
        List<Record> records = Db.find("select id,jczyx from yxkh");
        List<String> updateSqlList = Lists.newArrayList();
        for (Record record : records) {
            Integer id = record.getInt("id");
            String jczyx = record.getStr("jczyx");
            String[] jczyxSplit = jczyx.split("[,;]");
            Set<String> jczyxSet = new HashSet<>(Arrays.asList(jczyxSplit));
            updateSqlList.add("update yxkh set jczyx='" + StringUtils.join(jczyxSet, ";") + "', zjxgsj='" + formatPureDate(new Date()) + "' where id=" + id + ";");
        }
        Db.batch(updateSqlList, 1000);
        refreshCacheTable(YXKH);
        index();
    }

    public void jytx() {
        List<String> txEmailList = Db.query("select yx from yjzt where zt='退信'");
        List<Record> records = Db.find("select id,jczyx from yxkh where sfqy='否'");
        List<String> sqlList = Lists.newArrayList();
        for (Record record : records) {
            Integer id = record.getInt("id");
            String jczyx = record.getStr("jczyx");
            for (String email : txEmailList) {
                if (jczyx.contains(email)) {
                    sqlList.add("update yxkh set sfqy='是', zjxgsj='" + formatPureDate(new Date()) + "' where id=" + id);
                }
            }
        }
        Db.batch(sqlList, 1000);
        refreshCacheTable(YXKH);
        index();
    }

    public void export() {
        String downloadFile = genFile("营销客户", "select * from yxkh order by gb,gsmc");
        redirect(downloadFile);
    }


    public void uploadWj() {
        List<UploadFile> files = getFiles();
        Integer upload_id = getParaToInt("upload_id");
        String uploadPath = PathKit.getWebRootPath() + "/upload/" + "yxkh/" + upload_id;

        if (files == null || files.isEmpty() || upload_id == null) {
            redirect("/my/yxkh/edit?id=" + upload_id);
            return;
        }
        try {
            FileUtils.forceMkdir(new File(uploadPath));
        } catch (IOException e) {
            e.printStackTrace();
        }
        Yxkh yxkh = srv.findById(upload_id);
        StringBuilder wj = new StringBuilder(trueString(yxkh.getWj()));
        for (UploadFile file : files) {
            try {
                String fileName = file.getFileName();
                File destFile = new File(uploadPath + "/" + fileName);
                FileUtils.deleteQuietly(destFile);
                FileUtils.moveFile(file.getFile(), destFile);
                wj.append(fileName).append(";");
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        yxkh.setWj(wj.toString());
        yxkh.setZjxgsj(formatPureDate(new Date()));
        yxkh.update();
        refreshCacheTable(YXKH);
        String yyyyMmDd = yyyy_MM_dd();
        YxkhBd bdByKhAndRq = srv.findBdByKhAndRq(upload_id, yyyyMmDd);
        if (bdByKhAndRq == null) {
            YxkhBd yxkhBd = new YxkhBd();
            yxkhBd.setKhid(upload_id);
            yxkhBd.setYwy(getLoginAccount().getUserName());
            yxkhBd.setBdrq(yyyyMmDd);
            yxkhBd.save();
        }
        redirect("/my/yxkh/edit?id=" + upload_id);
    }

    public void deleteFile() {
        try {
            Integer id = getParaToInt("id");
            String wj = getPara("wj");
            Yxkh yxkh = srv.findById(id);
            String yxkhWj = yxkh.getWj();
            yxkh.setWj(StringUtils.replace(yxkhWj, wj + ";", ""));
            yxkh.setZjxgsj(formatPureDate(new Date()));
            yxkh.update();
            refreshCacheTable(YXKH);
            String yyyyMmDd = yyyy_MM_dd();
            YxkhBd bdByKhAndRq = srv.findBdByKhAndRq(id, yyyyMmDd);
            if (bdByKhAndRq == null) {
                YxkhBd yxkhBd = new YxkhBd();
                yxkhBd.setKhid(id);
                yxkhBd.setYwy(getLoginAccount().getUserName());
                yxkhBd.setBdrq(yyyyMmDd);
                yxkhBd.save();
            }
            renderJson(Ret.ok().set("msg", "删除成功！"));
        } catch (Exception e) {
            renderJson(Ret.fail().set("msg", e.getMessage()));
            e.printStackTrace();
        }
    }

    public void changeJybz() {
        Integer id = getParaToInt("id");
        String value = getPara("value", "");
        String gtfs = getPara("gtfs", "电话");
        Yxkh yxkh = srv.findById(id);
        if (yxkh == null) {
            renderJson(Ret.fail("没有找到对应的客户信息"));
            return;
        }
        String jczyx = trueString(yxkh.getJczyx());
        String[] jczyxSplit = jczyx.split("[,;]");
        List<String> khlxrList = Db.query("select yx from vw_khlxr");
        Set<String> jczyxSet = new HashSet<>(Arrays.asList(jczyxSplit));
        for (String jczyxOne : jczyxSet) {
            jczyxOne = trueString(jczyxOne).trim();
            if (khlxrList.contains(jczyxOne)) {
                renderJson(Ret.fail("邮箱 " + jczyxOne + " 为已合作客户，不好再联系！"));
                return;
            }
        }
        String userName = getLoginAccount().getUserName();
        String fg = yxkh.getFg();
        if (!"'<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'".contains(userName) && StringUtils.isEmpty(fg)) {
            yxkh.setFg(userName);
        }
        yxkh.setBz3(value);
        yxkh.setZjxgsj(formatPureDate(new Date()));
        yxkh.update();
        Record yxkhGtjyRecord = Db.findFirst("select * from yxkh_gtjy where khid=? and gtsj=?", id, yyyy_MM_dd());
        if (yxkhGtjyRecord == null) {
            YxkhGtjy yxkhGtjy = new YxkhGtjy();
            yxkhGtjy.setKhid(id);
            yxkhGtjy.setJy(value);
            yxkhGtjy.setYwy(userName);
            yxkhGtjy.setGtsj(yyyy_MM_dd());
            yxkhGtjy.setGtfs(gtfs);
            yxkhGtjy.save();
        } else {
            Db.update("update yxkh_gtjy set jy=?, gtfs=? where khid=? and gtsj=?", value, gtfs, id, yyyy_MM_dd());
        }
        String yyyyMmDd = yyyy_MM_dd();
        YxkhBd bdByKhAndRq = srv.findBdByKhAndRq(id, yyyyMmDd);
        if (bdByKhAndRq == null) {
            YxkhBd yxkhBd = new YxkhBd();
            yxkhBd.setKhid(id);
            yxkhBd.setYwy(userName);
            yxkhBd.setBdrq(yyyyMmDd);
            yxkhBd.save();
        }
        refreshCacheTable(YXKH);
        renderJson(Ret.ok("添加成功!"));
    }

    public void rate() {
        Integer id = getInt("id");
        Integer stars = getInt("stars");
        if (id == null) {
            renderJson(Ret.fail("没有要评分的客户"));
            return;
        }
        Yxkh yxkh = srv.findById(id);
        yxkh.setZydj(stars);
        yxkh.setZjxgsj(formatPureDate(new Date()));
        yxkh.update();
        renderJson(Ret.ok("评分成功!"));
    }

    public void good() {
        Integer id = getParaToInt("id");
        Yxkh yxkh = srv.findById(id);
        yxkh.setSfqy("是");
        yxkh.update();
        Kh kh = new Kh();
        String gsmc = yxkh.getGsmc();
        kh.setJc(gsmc);
        kh.setGb(yxkh.getGb());
//        kh.setYy();
        String fjr = trueString(yxkh.getFjr());
        boolean linstone = StringUtils.containsIgnoreCase(fjr, "linstone");
        String tdtt = linstone ? "LINSTONE CO.,LTD" : "XIAMEN OLYMPIA STONE CO.,LTD";
        String bq = linstone ? "<EMAIL>" : "<EMAIL>";
        if (StringUtils.containsIgnoreCase(fjr, "linstone")) {
            tdtt = "LINSTONE CO.,LTD";
            bq = "<EMAIL>";
        } else if (StringUtils.containsIgnoreCase(fjr, "theolympiastone")) {
            tdtt = "XIAMEN OLYMPIA STONE CO.,LTD";
            bq = "<EMAIL>";
        } else if (StringUtils.containsIgnoreCase(fjr, "getgroupltd")) {
            tdtt = "GREEN ENERGY TECHNOLOGY";
            bq = "<EMAIL>";
        }
        kh.setDylxr(fjr);
        kh.setKhdylxr(yxkh.getJczyx());
        kh.setKhdylxrfd(yxkh.getJcz());
        kh.setKhdylxrm(yxkh.getJcz());
        kh.setBq(bq);
        kh.setMc(gsmc);
        kh.setGsmc(gsmc);
        kh.setDz(yxkh.getDz());
        kh.setGswz(yxkh.getWz());
        kh.setMdg(yxkh.getMdg());
        kh.setHzywy(getLoginAccount().getUserName());
        kh.setHzsj(yyyy_MM_dd());
        kh.setTdtzr("SAME AS CONSIGNEE");
        kh.setBg("代理");
        kh.setTdtt(tdtt);//LINSTONE CO.,LTD   XIAMEN OLYMPIA STONE CO.,LTD
        kh.setGsz("私账");
        kh.setQgzl("清单发票;熏蒸单");
        kh.setYwy(getLoginAccount().getUserName());
        kh.save();
        refreshCacheTable(KH);
        refreshCacheTable(YXKH);
        redirect("/my/kh/edit?id=" + kh.getId());
    }

    public void getMsg() {
        String id = get("id");
        String userName = getLoginAccount().getUserName();
        List<String> usernameFollowList = Db.query("select username_follow from account_guanli where username=?", userName);
        if (usernameFollowList == null) {
            usernameFollowList = Lists.newArrayList();
        }
        usernameFollowList.add(userName);
        String usernameFollow = " ('" + StringUtils.join(usernameFollowList, "','") + "') ";

        List<Record> records;
        boolean hasRole = AdminAuthService.me.hasRole(getLoginAccount().getId(), new String[]{"权限管理员", "超级管理员", "总经理", "阿旭"});
        if (!hasRole) {
            records = Db.find("select * from yxkh_gtjy where khid=? and ywy in " + usernameFollow, id);
        } else {
            records = Db.find("select * from yxkh_gtjy where khid=?", id);
        }
        StringBuilder sb = new StringBuilder("<div style=\"margin: 20px\"><table class=\"layui-table\" lay-size=\"sm\"><thead><tr><th>联系人</th><th>联系时间</th><th>联系内容</th></tr></thead><tbody>");
        for (Record record : records) {
            sb.append("<tr><td>").append(record.getStr("ywy")).append("</td><td>").append(record.getStr("gtsj")).append("</td><td>").append(record.getStr("jy")).append("</td></tr>");
        }
        sb.append("</tbody></table></div>");
        renderJson(Ret.ok().set("content", sb.toString()));
    }

    public void getGtjy() {
        String id = get("customerId");
        String userName = getLoginAccount().getUserName();
        List<String> usernameFollowList = Db.query("select username_follow from account_guanli where username=?", userName);
        if (usernameFollowList == null) {
            usernameFollowList = Lists.newArrayList();
        }
        usernameFollowList.add(userName);
        String usernameFollow = " ('" + StringUtils.join(usernameFollowList, "','") + "') ";

        List<Record> records;
        boolean hasRole = AdminAuthService.me.hasRole(getLoginAccount().getId(), new String[]{"权限管理员", "超级管理员", "总经理", "阿旭"});
        if (!hasRole) {
            records = Db.find("select * from yxkh_gtjy where khid=? and ywy in " + usernameFollow + " order by gtsj desc", id);
        } else {
            records = Db.find("select * from yxkh_gtjy where khid=?" + " order by gtsj desc", id);
        }
        renderJson(LayGsonBuilder.create().toJson(new LayRecordData(records)));
    }

    public void getHzkh() {
        List<Yxkh> all = srv.findAll();
        List<Record> list = Db.find("select * from vw_khlxr");
        Map<String, Record> khMap = Maps.newHashMap();
        for (Record record : list) {
            khMap.put(record.get("yx"), record);
        }
        StringBuilder result = new StringBuilder();
        StringBuilder idResult = new StringBuilder();
        StringBuilder hzjcResult = new StringBuilder();
        for (Yxkh yxkh : all) {
            Integer id = yxkh.getId();
            String gsmc = yxkh.getGsmc();
            String jczyx = yxkh.getJczyx();
            if (StringUtils.isEmpty(jczyx)) {
                continue;
            }
            String[] split = jczyx.split("[,;]");
            for (String s : split) {
                if (khMap.containsKey(s)) {
                    result.append(id).append(", ").append(gsmc).append("; ");
                    idResult.append(id).append(", ");
                    hzjcResult.append(khMap.get(s).getStr("jc")).append(", ");
                    break;
                }
            }
        }
        renderText(result + "\n" + idResult + "\n" + hzjcResult);
    }

    public void batchDelete() {
        String content = get("content", "");
        String[] emails = content.split("[,;\n]");
        List<String> sqlList = Lists.newArrayList();
        for (String email : emails) {
            sqlList.add("update yxkh set jczyx=replace(jczyx, '" + email + "', '') where jczyx like '" + email + "%';");
        }
        sqlList.add("update yxkh set jczyx=replace(jczyx,';;',';')");
        Db.batch(sqlList, sqlList.size());
        refreshCacheTable(YXKH);
        renderJson(Ret.ok("批量删除成功!"));
    }

    public void batchAddLabel() {
        String ids = get("ids");
        String bq = get("bq");
        if (StringUtils.isEmpty(ids)) {
            renderJson(Ret.fail("没有选中的客户!"));
            return;
        }
        Db.update("update yxkh set bq=replace(if(bq is null, '" + bq + "', concat(bq, ',', '" + bq + "')), ',,', ',') where id in (" + ids + ");");
//        Db.update("update yxkh set bq=GROUP_CONCAT(DISTINCT bq ORDER BY bq ASC SEPARATOR ',') where id in (" + ids + ");");
        refreshCacheTable(YXKH);
        renderJson(Ret.ok("添加标签成功!"));
    }

    public void batchDeleteKh() {
        String ids = get("ids");
        if (StringUtils.isEmpty(ids)) {
            renderJson(Ret.fail("没有选中的客户!"));
            return;
        }
        Db.update("delete from yxkh where id in (" + ids + ");");
        refreshCacheTable(YXKH);
        renderJson(Ret.ok("批量删除客户成功!"));
    }

    public void batchExportKh() {
        String ids = get("ids");
        if (StringUtils.isEmpty(ids)) {
            renderJson(Ret.fail("没有选中的客户!"));
            return;
        }

        List<Record> records = Db.find("select gsmc,jcz,jczxb,jczyx,bq from yxkh where id in (" + ids + ");");

        try {
            // 生成临时文件名
            String fileName = "客户信息_" + formatFileDate(new Date()) + ".xlsx";
            String tempPath = PathKit.getWebRootPath() + "/upload/temp/";
            File tempDir = new File(tempPath);
            if (!tempDir.exists()) {
                tempDir.mkdirs();
            }
            File file = new File(tempPath + fileName);

            // 创建Excel写入器并写入数据
            ExcelWriter excelWriter = EasyExcel.write(file)
                    .registerWriteHandler(new SimpleColumnWidthStyleStrategy(25)) // 设置列宽
                    .build();

            // 创建写入的sheet
            WriteSheet writeSheet = EasyExcel.writerSheet("客户信息")
                    .head(Arrays.asList(
                            Arrays.asList("公司名称"),
                            Arrays.asList("决策者"),
                            Arrays.asList("决策者性别"),
                            Arrays.asList("决策者邮箱"),
                            Arrays.asList("标签")
                    )).build();

            // 转换数据格式
            List<List<String>> dataList = new ArrayList<>();
            for (Record record : records) {
                List<String> rowData = new ArrayList<>();
                rowData.add(record.getStr("gsmc"));
                rowData.add(record.getStr("jcz"));
                rowData.add(record.getStr("jczxb"));
                rowData.add(record.getStr("jczyx"));
                rowData.add(record.getStr("bq"));
                dataList.add(rowData);
            }

            // 写入数据并关闭
            excelWriter.write(dataList, writeSheet);
            excelWriter.finish();

            // 设置响应头
            getResponse().setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            getResponse().setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));

            // 使用FileUtils复制文件到响应流
            FileUtils.copyFile(file, getResponse().getOutputStream());

            // 删除临时文件
            file.delete();

            // 重要：不要调用renderJson或其他render方法
            renderNull();

        } catch (Exception e) {
            e.printStackTrace();
            renderJson(Ret.fail("导出失败：" + e.getMessage()));
        }
    }

    public void chooseEmail() {
        Integer id = getParaToInt("id");
        File file = new File(Objects.requireNonNull(getClass().getResource("/")).getPath() + "tpl/response/yxkh_send_email.tpl");

        List<Record> yjList;
        boolean hasRole = AdminAuthService.me.hasRole(getLoginAccountId(), new String[]{"权限管理员", "超级管理员", "总经理"});
        yjList = hasRole ? Db.find("select id,mc from yj where fl='营销邮件' order by mc ")
                : Db.find("select id,mc from yj where fl='营销邮件' and (sfgy='是' or cjr=?) order by mc ", getLoginAccount().getUserName());

        String addContent = "";
        try {
            String addTpl = FileUtils.readFileToString(file, "UTF-8");
            Engine engine = Engine.use();
            Template historyTemplate = engine.getTemplateByString(addTpl);
            addContent = historyTemplate.renderToString(Kv.by("id", id).set("yjList", yjList));
        } catch (IOException e) {
            e.printStackTrace();
        }
        renderText(addContent);
    }

    public void sendEmail() {
        Integer id = getParaToInt("id");
        Integer yjId = getParaToInt("yj");
        Yxkh yxkh = srv.findById(id);
        Yj yj = yjSrv.findById(yjId);
        String jczyx = yxkh.getJczyx();
        if (StringUtils.isEmpty(jczyx)) {
            renderJson(Ret.fail("该客户没有决策者邮箱!"));
            return;
        }
        String fsyx = getLoginAccount().getUserName();
        String title = yj.getBt();
        String nr = yj.getNr();
        String content = nr.replace("\n", "<br>").replace("src=\"/upload", "src=\"" + OSConstants.url + "/upload");
        Template titleTemplate = TipHelperKit.engine.getTemplateByString(title);
        Template contentTemplate = TipHelperKit.engine.getTemplateByString(content);
        Record lxrRecord = new Record();
        lxrRecord.set("mc", yxkh.getJcz());
        lxrRecord.set("xb", yxkh.getJczxb());
        lxrRecord.set("yx", jczyx);
        lxrRecord.set("dh", yxkh.getJcsj());
        lxrRecord.set("bz", "");
        lxrRecord.set("gs", yxkh.getGsmc());
        String wflxr;
        if (StringUtils.isEmpty(fsyx)) {
            wflxr = "Emma, Linstone";
        } else if (fsyx.contains("linstone")) {
            wflxr = "Emma, Linstone";
        } else if (fsyx.contains("theolympiastone")) {
            wflxr = "Apple, Olympia Stone";
        } else if (fsyx.contains("getgroupltd")) {
            wflxr = "Best Regards, Apple";
        } else {
            wflxr = "Emma, Linstone";
        }
        Kv data = Kv.by("lxr", yxkh).set("wflxr", wflxr);
        String emailTitle = titleTemplate.renderToString(data);
        String emailContent = contentTemplate.renderToString(data);
        Record fsyxPz = Db.findFirst("select * from yxpz where yx=?", fsyx);
        if (fsyxPz == null) {
            fsyxPz = Db.findFirst("select * from yxpz where yx='<EMAIL>'");
        }
        try {
            YxkhEmailKit.sendEmail(fsyxPz.getStr("fwq"), fsyx, fsyxPz.getStr("mm"), jczyx, emailTitle, emailContent);
            YxkhEmailKit.sendEmail(fsyxPz.getStr("fwq"), fsyx, fsyxPz.getStr("mm"), fsyx, emailTitle, emailContent);
        } catch (Exception e) {
            e.printStackTrace();
            renderJson(Ret.fail("发送失败！\n" + e.getMessage()));
            return;
        }
        renderJson(Ret.ok("发送成功！"));
    }

    public void deleteGtjy() {
        Integer id = getInt("id");
        if (id == null) {
            renderJson(Ret.fail("删除失败!没有找到对应的记录"));
            return;
        }
        Db.delete("delete from yxkh_gtjy where id=?", id);
        renderJson(Ret.ok("删除成功!"));
    }

    public void hqjwd() {
        String country = getPara("country");
        String zhou = getPara("zhou");
        String shi = getPara("shi");
        String address = getPara("address");
        if (StringUtils.isEmpty(address)) {
            renderJson(Ret.fail("请输入地址"));
            return;
        }
        CommonUtils.AddressInfo addressInfo = CommonUtils.getLatLngFromAddress(address, shi, zhou, country);
        renderJson(Ret.ok("获取成功!").set("data", addressInfo));
    }

    public void updateAllLatLng() {
        List<Record> records = Db.find("select id, dz, gb, zhou, shi from yxkh where (jd is null or wd is null) and dz is not null and dz<>''");
        int total = records.size();
        int current = 0;

        StringBuilder result = new StringBuilder();
        result.append("开始更新地址信息，共 ").append(total).append(" 条记录需要更新<br>");
        renderJson(Ret.ok("msg", result.toString()).set("total", total).set("current", current));

        for (Record record : records) {
            current++;
            Integer id = record.getInt("id");
            String dz = record.getStr("dz");
            String gb = record.getStr("gb");
            String zhou = record.getStr("zhou");
            String shi = record.getStr("shi");

            try {
                CommonUtils.AddressInfo addressInfo = CommonUtils.getLatLngFromAddress(dz, shi, zhou, gb);
                if (addressInfo != null) {
                    Db.update("update yxkh set jd=?, wd=?, bzdz=? where id=?",
                            addressInfo.getLongitude(),
                            addressInfo.getLatitude(),
                            addressInfo.getFormattedAddress(),
                            id);
                    result.append("更新成功 ").append(current).append("/").append(total).append(": ID=").append(id)
                            .append(", 地址=").append(dz).append("<br>");
                } else {
                    result.append("更新失败 ").append(current).append("/").append(total).append(": ID=").append(id)
                            .append(", 地址=").append(dz).append(" (无法获取地理信息)<br>");
                }
            } catch (Exception e) {
                result.append("更新出错 ").append(current).append("/").append(total).append(": ID=").append(id)
                        .append(", 地址=").append(dz).append(" (").append(e.getMessage()).append(")<br>");
            }

            // 每处理1条记录就更新一次进度
            renderJson(Ret.ok("msg", result.toString())
                    .set("total", total)
                    .set("current", current)
                    .set("percent", Math.round((float) current / total * 100)));
        }

        result.append("更新完成，共处理 ").append(total).append(" 条记录");
        renderJson(Ret.ok("msg", result.toString())
                .set("total", total)
                .set("current", current)
                .set("percent", 100)
                .set("finished", true));
    }

    public void khdt() {
        List<Record> records = Db.find("select id, gsmc, jd, wd, gb, zhou, shi, bzdz, jcz, jczxb, jczyx, ifnull(zydj, 0) zydj, wz, ifnull(concat('http://', replace(replace(wz, 'http://', ''), 'https://', '')), '') zwz from yxkh where sfqy<>'是' and jd is not null and wd is not null and jd <> '' and wd <> ''");
        System.out.println("Found " + records.size() + " records with coordinates");

        List<Map<String, Object>> resultMapList = Lists.newArrayList();
        for (Record record : records) {
            try {
                String jd = record.getStr("jd");
                String wd = record.getStr("wd");

                // 尝试解析为数值
                double jdValue = Double.parseDouble(jd.trim());
                double wdValue = Double.parseDouble(wd.trim());

                // 验证坐标范围
                if (jdValue >= -180 && jdValue <= 180 && wdValue >= -90 && wdValue <= 90) {
                    Map<String, Object> map = Maps.newHashMap();
                    map.put("id", record.getStr("id"));
                    map.put("gsmc", jsonString(record.getStr("gsmc")));
                    map.put("jd", jdValue);  // 使用解析后的数值
                    map.put("wd", wdValue);  // 使用解析后的数值
                    map.put("zydj", record.getStr("zydj"));
                    map.put("wz", jsonString(record.getStr("wz")));
                    map.put("zwz", jsonString(record.getStr("zwz")));
                    map.put("gb", jsonString(record.getStr("gb")));
                    map.put("zhou", jsonString(record.getStr("zhou")));
                    map.put("shi", jsonString(record.getStr("shi")));
                    map.put("bzdz", jsonString(record.getStr("bzdz")));
                    map.put("jcz", jsonString(record.getStr("jcz")));
                    map.put("jczxb", jsonString(record.getStr("jczxb")));
                    map.put("jczyx", jsonString(record.getStr("jczyx")));
                    resultMapList.add(map);
                } else {
                    System.out.println("Invalid coordinate range - jd: " + jdValue + ", wd: " + wdValue);
                }
            } catch (Exception e) {
                System.out.println("Error processing record: " + e.getMessage());
            }
        }

        String jsonResult = JsonKit.toJson(resultMapList);
        setAttr("records", jsonResult);
        render("khdt.html");
    }

    public void updateLatLngPage() {
        render("update_latlng.html");
    }

    /**
     * 显示录音上传页面
     */
    public void uploadVoiceRecord() {
        Integer id = getParaToInt("id");
        if (id == null) {
            renderText("客户ID不能为空");
            return;
        }

        Yxkh yxkh = srv.findById(id);
        if (yxkh == null) {
            renderText("未找到对应的客户信息");
            return;
        }

        // 获取该客户的录音记录
        List<Record> voiceRecordList = Db.find(
                "select id, file_name as fileName, upload_time as uploadTime, remark " +
                        "from yxkh_voice_record where customer_id = ? order by upload_time desc", id);

        setAttr("yxkh", yxkh);
        setAttr("voiceRecordList", voiceRecordList);
        render("uploadVoiceRecord.html");
    }

    /**
     * 处理录音文件上传
     */
    public void uploadVoiceRecordFile() throws UnsupportedEncodingException {
        System.out.println("=== 开始处理录音文件上传 ===");
        UploadFile voiceFile = getFile("voiceFile");

        Integer customerId = getParaToInt("customerId");
        String remark = getPara("remark", "");
        String needManagerAnalysis = getPara("needManagerAnalysis", "");

        System.out.println("客户ID: " + customerId);
        System.out.println("备注: " + remark);
        System.out.println("需要主管分析: " + needManagerAnalysis);

        if (customerId == null) {
            System.out.println("错误: 客户ID为空");
            redirect("/my/yxkh/uploadVoiceRecord?error=" + URLEncoder.encode("客户ID不能为空", "UTF-8"));
            return;
        }

        Yxkh yxkh = srv.findById(customerId);
        if (yxkh == null) {
            System.out.println("错误: 未找到客户信息, ID=" + customerId);
            redirect("/my/yxkh/uploadVoiceRecord?id=" + customerId + "&error=" + URLEncoder.encode("未找到对应的客户信息", "UTF-8"));
            return;
        }

        if (voiceFile == null) {
            System.out.println("错误: 未选择录音文件");
            redirect("/my/yxkh/uploadVoiceRecord?id=" + customerId + "&error=" + URLEncoder.encode("请选择要上传的录音文件", "UTF-8"));
            return;
        }

        System.out.println("上传文件名: " + voiceFile.getFileName());
        System.out.println("文件大小: " + voiceFile.getFile().length() + " bytes");

        try {
            // 创建上传目录
            String uploadPath = PathKit.getWebRootPath() + "/upload/voice/" + customerId;
            System.out.println("创建上传目录: " + uploadPath);

            File uploadDir = new File(uploadPath);
            if (!uploadDir.exists()) {
                boolean created = uploadDir.mkdirs();
                System.out.println("目录创建结果: " + created);
            }

            // 生成新的文件名（带时间戳避免重名）
            String originalFileName = voiceFile.getFileName();
            String fileExtension = "";
            if (originalFileName != null && originalFileName.contains(".")) {
                fileExtension = originalFileName.substring(originalFileName.lastIndexOf("."));
            }
            String newFileName = "voice_" + formatFileDate(new Date()) + "_" + System.currentTimeMillis() + fileExtension;
            System.out.println("新文件名: " + newFileName);

            // 移动文件到目标目录
            File destFile = new File(uploadPath + "/" + newFileName);
            System.out.println("目标文件路径: " + destFile.getAbsolutePath());

            FileUtils.moveFile(voiceFile.getFile(), destFile);
            System.out.println("文件移动完成");

            // 保存录音记录到数据库
            String insertSql = "insert into yxkh_voice_record (customer_id, file_name, file_path, upload_time, remark, uploader) values (?, ?, ?, ?, ?, ?)";
            String filePath = "/upload/voice/" + customerId + "/" + newFileName;
            String uploadTime = formatFileDate(new Date());
            String uploader = getLoginAccount().getUserName();

            System.out.println("插入数据库:");
            System.out.println("  客户ID: " + customerId);
            System.out.println("  文件名: " + newFileName);
            System.out.println("  文件路径: " + filePath);
            System.out.println("  上传时间: " + uploadTime);
            System.out.println("  上传者: " + uploader);

            // 先检查表是否存在
            try {
                Long count = Db.queryLong("SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'yxkh_voice_record'");
                if (count == 0) {
                    System.out.println("错误: yxkh_voice_record 表不存在，请先创建数据库表");
                    redirect("/my/yxkh/uploadVoiceRecord?id=" + customerId + "&error=" + URLEncoder.encode("数据库表不存在，请联系管理员创建yxkh_voice_record表", "UTF-8"));
                    return;
                }
            } catch (Exception tableCheckException) {
                System.out.println("检查表存在性时出错: " + tableCheckException.getMessage());
            }

            Db.update(insertSql, customerId, newFileName, filePath, uploadTime, remark, uploader);
            System.out.println("数据库插入完成");

            // 更新客户的最后修改时间
            yxkh.setZjxgsj(formatPureDate(new Date()));
            yxkh.update();
            refreshCacheTable(YXKH);
            System.out.println("客户信息更新完成");

            // 添加变更记录
            String yyyyMmDd = yyyy_MM_dd();
            YxkhBd bdByKhAndRq = srv.findBdByKhAndRq(customerId, yyyyMmDd);
            if (bdByKhAndRq == null) {
                YxkhBd yxkhBd = new YxkhBd();
                yxkhBd.setKhid(customerId);
                yxkhBd.setYwy(uploader);
                yxkhBd.setBdrq(yyyyMmDd);
                yxkhBd.save();
                System.out.println("变更记录添加完成");
            }

            // 如果选择了需要主管分析，发送通知给管理员
            if ("1".equals(needManagerAnalysis)) {
                try {
                    sendNoticeToManagers(yxkh, uploader, newFileName, remark);
                    System.out.println("管理员通知发送完成");
                } catch (Exception e) {
                    System.out.println("发送管理员通知失败: " + e.getMessage());
                    e.printStackTrace();
                }
            }

            System.out.println("=== 录音文件上传成功 ===");
            redirect("/my/yxkh/uploadVoiceRecord?id=" + customerId + "&success=1");

        } catch (Exception e) {
            System.out.println("=== 录音文件上传失败 ===");
            e.printStackTrace();
            String errorMsg = "上传失败：" + e.getMessage();
            System.out.println("错误信息: " + errorMsg);
            redirect("/my/yxkh/uploadVoiceRecord?id=" + customerId + "&error=" + URLEncoder.encode(errorMsg, "UTF-8"));
        }
    }

    /**
     * 获取客户的录音文件列表
     */
    public void getVoiceRecords() {
        Integer customerId = getParaToInt("customerId");
        if (customerId == null) {
            renderJson(Ret.fail("客户ID不能为空"));
            return;
        }

        try {
            List<Record> voiceRecords = Db.find(
                    "select id, file_name as fileName, file_path as filePath, upload_time as uploadTime, remark, uploader " +
                            "from yxkh_voice_record where customer_id = ? order by upload_time desc", customerId);

            // 转换数据格式
            List<Map<String, Object>> result = Lists.newArrayList();
            for (Record record : voiceRecords) {
                Map<String, Object> map = Maps.newHashMap();
                map.put("id", record.getInt("id"));
                map.put("fileName", record.getStr("fileName"));
                map.put("filePath", record.getStr("filePath"));
                map.put("uploadTime", record.getStr("uploadTime"));
                map.put("remark", record.getStr("remark"));
                map.put("uploader", record.getStr("uploader"));
                result.add(map);
            }

            renderJson(result);

        } catch (Exception e) {
            e.printStackTrace();
            renderJson(Ret.fail("获取录音文件列表失败：" + e.getMessage()));
        }
    }

    /**
     * 删除录音记录
     */
    public void deleteVoiceRecord() {
        Integer id = getParaToInt("id");
        if (id == null) {
            renderJson(Ret.fail("录音记录ID不能为空"));
            return;
        }

        try {
            // 查询录音记录信息
            Record voiceRecord = Db.findFirst("select * from yxkh_voice_record where id = ?", id);
            if (voiceRecord == null) {
                renderJson(Ret.fail("未找到对应的录音记录"));
                return;
            }

            // 删除物理文件
            String filePath = voiceRecord.getStr("file_path");
            if (StringUtils.isNotEmpty(filePath)) {
                File file = new File(PathKit.getWebRootPath() + filePath);
                if (file.exists()) {
                    file.delete();
                }
            }

            // 删除数据库记录
            Db.delete("delete from yxkh_voice_record where id = ?", id);

            // 更新客户的最后修改时间
            Integer customerId = voiceRecord.getInt("customer_id");
            if (customerId != null) {
                Yxkh yxkh = srv.findById(customerId);
                if (yxkh != null) {
                    yxkh.setZjxgsj(formatPureDate(new Date()));
                    yxkh.update();
                    refreshCacheTable(YXKH);

                    // 添加变更记录
                    String yyyyMmDd = yyyy_MM_dd();
                    YxkhBd bdByKhAndRq = srv.findBdByKhAndRq(customerId, yyyyMmDd);
                    if (bdByKhAndRq == null) {
                        YxkhBd yxkhBd = new YxkhBd();
                        yxkhBd.setKhid(customerId);
                        yxkhBd.setYwy(getLoginAccount().getUserName());
                        yxkhBd.setBdrq(yyyyMmDd);
                        yxkhBd.save();
                    }
                }
            }

            renderJson(Ret.ok("删除成功"));

        } catch (Exception e) {
            e.printStackTrace();
            renderJson(Ret.fail("删除失败：" + e.getMessage()));
        }
    }

    /**
     * 电话拨打代理接口
     * 解决前端跨域问题
     */
    public void callProxy() {
        String caller = getPara("caller");
        String callees = getPara("callees");

        if (StringUtils.isEmpty(caller)) {
            renderJson(Ret.fail("主叫号码不能为空"));
            return;
        }

        if (StringUtils.isEmpty(callees)) {
            renderJson(Ret.fail("被叫号码不能为空"));
            return;
        }

        // 清理号码中的空格
        caller = caller.replaceAll("\\s", "");
        callees = callees.replaceAll("\\s", "");

        System.out.println("电话拨打请求 - 主叫: " + caller + ", 被叫: " + callees);

        try {
            // 构建请求参数
            String params = "caller=" + caller + "&callees=" + callees;

            // 使用Java发送HTTP请求到外部API
            java.net.URL url = new java.net.URL("https://www.tutucall.com/m/call_data.php");
            java.net.HttpURLConnection connection = (java.net.HttpURLConnection) url.openConnection();

            // 设置请求方法和属性
            connection.setRequestMethod("POST");
            connection.setDoOutput(true);
            connection.setConnectTimeout(10000); // 10秒连接超时
            connection.setReadTimeout(15000);    // 15秒读取超时
            connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");

            // 发送请求参数
            try (java.io.OutputStream os = connection.getOutputStream()) {
                os.write(params.getBytes(StandardCharsets.UTF_8));
                os.flush();
            }

            // 读取响应
            int responseCode = connection.getResponseCode();
            System.out.println("电话API响应状态码: " + responseCode);

            if (responseCode == 200) {
                try (java.io.BufferedReader reader = new java.io.BufferedReader(
                        new java.io.InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8))) {

                    StringBuilder response = new StringBuilder();
                    String line;
                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }

                    String responseText = response.toString();
                    System.out.println("电话API响应内容: " + responseText);

                    // 解析响应JSON
                    try {
                        com.google.gson.JsonObject jsonResponse = new com.google.gson.JsonParser().parse(responseText).getAsJsonObject();
                        int code = jsonResponse.get("code").getAsInt();
                        String msg = jsonResponse.has("msg") ? jsonResponse.get("msg").getAsString() : "";

                        if (code == 0) {
                            renderJson(Ret.ok("拨打成功，请接听系统来电").set("data", jsonResponse));
                        } else {
                            renderJson(Ret.fail("拨打失败: " + msg).set("data", jsonResponse));
                        }
                    } catch (Exception parseException) {
                        System.out.println("解析响应JSON失败: " + parseException.getMessage());
                        renderJson(Ret.ok("拨打请求已发送，请接听系统来电").set("response", responseText));
                    }
                }
            } else {
                System.out.println("电话API请求失败，状态码: " + responseCode);
                renderJson(Ret.fail("拨打请求失败，HTTP状态码: " + responseCode));
            }

        } catch (java.net.SocketTimeoutException e) {
            System.out.println("电话API请求超时: " + e.getMessage());
            renderJson(Ret.fail("请求超时，请稍后重试"));
        } catch (java.net.ConnectException e) {
            System.out.println("电话API连接失败: " + e.getMessage());
            renderJson(Ret.fail("无法连接到电话服务，请检查网络"));
        } catch (Exception e) {
            System.out.println("电话拨打代理出错: " + e.getMessage());
            e.printStackTrace();
            renderJson(Ret.fail("拨打失败: " + e.getMessage()));
        }
    }

    public static void main(String[] args) {
        DruidPlugin druidPlugin = OSClubConfig.getDruidPlugin();
        ActiveRecordPlugin arp = new ActiveRecordPlugin("mysql", druidPlugin);
        _MappingKit.mapping(arp);
        druidPlugin.start();
        arp.start();

        Map<String, CommonUtils.AddressInfo> addressInfoMap = Maps.newHashMap();
        List<Record> records = Db.find("select id, dz, gb, zhou, shi from yxkh where (jd is null or wd is null) and dz is not null and dz<>''");
        int total = records.size();
        int current = 0;

        System.out.println("开始更新地址信息，共 " + total + " 条记录需要更新");

        for (Record record : records) {
            current++;
            Integer id = record.getInt("id");
            String dz = record.getStr("dz");
            String gb = record.getStr("gb");
            String zhou = record.getStr("zhou");
            String shi = record.getStr("shi");

            try {
                CommonUtils.AddressInfo addressInfo;
                if (addressInfoMap.containsKey(dz)) {
                    addressInfo = addressInfoMap.get(dz);
                } else {
                    addressInfo = CommonUtils.getLatLngFromAddress(dz, shi, zhou, gb);
                    addressInfoMap.put(dz, addressInfo);
                }
                if (addressInfo != null) {
                    Db.update("update yxkh set jd=?, wd=?, bzdz=? where id=?",
                            addressInfo.getLongitude(),
                            addressInfo.getLatitude(),
                            addressInfo.getStandardAddress(),
                            id);
                    System.out.println("更新成功 " + id + ": " + addressInfo.getStandardAddress());
                } else {
                    System.out.println("更新失败 " + id + ": " + dz + " (无法获取地理信息)");
                }
            } catch (Exception e) {
                System.out.println("更新出错 " + current + "/" + total + ": ID=" + id + ": " + dz + " (" + e.getMessage() + ")");
            }
            if (current % 100 == 0) {
                System.out.println("  已更新: " + current + "/" + total);
            }
            try {
                Thread.sleep(3000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
        System.out.println("共处理 " + total + " 条记录");
        System.out.println("******************更新完毕********************");
    }

    public void getGbCount() {
        renderJson(Ret.ok().set("data", Db.query("select gb, count(*) cs from yxkh group by gb order by cs desc")));
    }

    /**
     * 检测是否为移动端设备
     */
    private boolean isMobileDevice() {
        String userAgent = getRequest().getHeader("User-Agent");
        if (userAgent == null) {
            return false;
        }

        userAgent = userAgent.toLowerCase();

        // 检测移动端关键词
        String[] mobileKeywords = {
                "mobile", "android", "iphone", "ipad", "ipod", "blackberry",
                "windows phone", "opera mini", "iemobile", "webos", "palm"
        };

        for (String keyword : mobileKeywords) {
            if (userAgent.contains(keyword)) {
                return true;
            }
        }

        // 检测屏幕宽度（如果有的话）
        String screenWidth = getRequest().getHeader("X-Screen-Width");
        if (screenWidth != null) {
            try {
                int width = Integer.parseInt(screenWidth);
                return width <= 768;
            } catch (NumberFormatException e) {
                // 忽略解析错误
            }
        }

        return false;
    }

    /**
     * 获取客户的调研答案数据（用于字段填充）
     */
    public void getSurveyAnswersForFields() {
        Long customerId = getParaToLong("customerId");
        if (customerId == null) {
            renderJson(Ret.fail("msg", "客户ID不能为空"));
            return;
        }

        try {
            // 获取该客户所有调研答案，按业务员分组
            String sql = "SELECT a.question_code, a.answer_content, a.surveyor_username, " +
                    "a.survey_date, acc.nickName as surveyor_nick_name " +
                    "FROM market_research_answer a " +
                    "LEFT JOIN account acc ON a.surveyor_username = acc.userName " +
                    "WHERE a.customer_id = ? AND a.answer_content IS NOT NULL AND a.answer_content != '' " +
                    "ORDER BY a.question_code, a.survey_date DESC";

            List<Record> answers = Db.find(sql, customerId);

            // 按问题编号分组
            Map<String, List<Record>> groupedAnswers = new HashMap<>();
            for (Record answer : answers) {
                String questionCode = answer.getStr("question_code");
                if (!groupedAnswers.containsKey(questionCode)) {
                    groupedAnswers.put(questionCode, new ArrayList<>());
                }
                groupedAnswers.get(questionCode).add(answer);
            }

            renderJson(Ret.ok("data", groupedAnswers));
        } catch (Exception e) {
            e.printStackTrace();
            renderJson(Ret.fail("msg", "获取调研答案失败：" + e.getMessage()));
        }
    }

    /**
     * 发送通知给管理员
     */
    private void sendNoticeToManagers(Yxkh yxkh, String uploader, String fileName, String remark) {
        try {
            // 查询具有管理员角色的用户
            List<Integer> managerIds = Db.query(
                "SELECT DISTINCT ar.accountId FROM account_role ar " +
                "INNER JOIN role r ON ar.roleId = r.id " +
                "INNER JOIN account a ON ar.accountId = a.id " +
                "WHERE r.name IN ('超级管理员', '总经理', '权限管理员') " +
                "AND a.status = 1"
            );

            if (managerIds.isEmpty()) {
                System.out.println("未找到管理员用户");
                return;
            }

            // 构建通知内容
            String customerName = yxkh.getGsmc() != null ? yxkh.getGsmc() : "未知客户";
            String content = String.format(
                "【录音分析请求】%s 上传了<a href='http://360.theolympiastone.com/my/yxkh/edit?id=%d' target='_blank'>客户「%s」</a>的录音文件，请求主管协助分析。\n" +
                uploader, yxkh.getId(), customerName
            );

            // 批量插入通知
            StringBuilder sql = new StringBuilder();
            sql.append("INSERT INTO notice (user_id, content, status, create_at, showed, type) VALUES ");

            List<Object> params = Lists.newArrayList();
            for (int i = 0; i < managerIds.size(); i++) {
                if (i > 0) sql.append(", ");
                sql.append("(?, ?, 0, NOW(), 0, '通知')");
                params.add(managerIds.get(i));
                params.add(content);
            }

            Db.update(sql.toString(), params.toArray());
            System.out.println("已向 " + managerIds.size() + " 位管理员发送通知");

        } catch (Exception e) {
            System.out.println("发送管理员通知时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public String getWl() {
        String oip = trueString(RedisKit.get("ip", "127.0.0.1")).trim();
        String nip = trueString(RequestKit.getIpAddr(getRequest())).trim();
        LogKit.info("oip:" + oip + ", nip:" + nip);
        return (StringUtils.equalsIgnoreCase(oip, nip) || "127.0.0.1".equalsIgnoreCase(nip)) ? "内网" : "外网";
    }
}
