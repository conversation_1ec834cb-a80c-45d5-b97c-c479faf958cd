package com.theolympiastone.club.my.zb;

import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Page;
import com.theolympiastone.club._admin.auth.AdminAuthService;
import com.theolympiastone.club.common.model.Account;
import com.theolympiastone.club.common.model.Zb;
import org.apache.commons.lang3.StringUtils;

import java.text.ParseException;

import static com.theolympiastone.club.common.kit.DateUtil.getWeekOfYear;

public class MyZbService {
    public static final MyZbService me = new MyZbService();
    private final Zb dao = new Zb().dao();

    private String getSuffixString(Account account, String query, String queryKsrq, String queryJsrq, String queryPx) throws ParseException {
        boolean hasRole = AdminAuthService.me.hasRole(account.getId(), new String[]{"权限管理员", "超级管理员", "总经理"});
        String suffix = "";
        if (!hasRole) {
            suffix += " and username='" + account.getUserName() + "' ";
        }
        if (!StringUtils.isEmpty(query)) {
            suffix += " and (ljnr like '%" + query + "%' or jzjx1 like '%" + query + "%' or jzjx2 like '%" + query + "%' or jzjx3 like '%" + query + "%' or jzjx4 like '%" + query + "%') ";
        }
        if (!StringUtils.isEmpty(queryKsrq)) {
            int weekOfYearStart = getWeekOfYear(queryKsrq, "yyyy-MM-dd");
            suffix += " and jlz >= '" + weekOfYearStart + "' ";
        }
        if (!StringUtils.isEmpty(queryJsrq)) {
            int weekOfYearEnd = getWeekOfYear(queryKsrq, "yyyy-MM-dd");
            suffix += " and jlz <= '" + weekOfYearEnd + "' ";
        }
        suffix += queryPx;
        return suffix;
    }

    public Page<Zb> paginate(int pageNum, int pageSize, Account account, String query, String queryKsrq, String queryJsrq, String queryPx) throws ParseException {
        String suffix = getSuffixString(account, query, queryKsrq, queryJsrq, queryPx);
        return dao.paginate(pageNum, pageSize, "select *, replace(jlnr, '\n', '<br>') jlnr_hh, replace(py, '\n', '<br>') py_hh ", " from zb where 1=1 " + suffix + " ");
    }

    public Ret save(Zb zb) {
        zb.save();
        return Ret.ok("msg", "创建成功");
    }

    public Zb findById(Integer id) {
        return dao.findById(id);
    }

    public Ret update(Zb zb) {
        zb.update();
        return Ret.ok("msg", "修改成功");
    }

    public Ret delete(Integer id) {
        dao.deleteById(id);
        return Ret.ok("msg", "删除成功");
    }

    public Zb findByUD(String username, String jlz) {
        return dao.findFirst("select * from zb where username=? and jlz=?", username, jlz);
    }
}
