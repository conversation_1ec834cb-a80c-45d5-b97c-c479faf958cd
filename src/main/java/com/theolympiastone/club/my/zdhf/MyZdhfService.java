package com.theolympiastone.club.my.zdhf;

import com.alibaba.druid.util.StringUtils;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Page;
import com.theolympiastone.club._admin.auth.AdminAuthService;
import com.theolympiastone.club.common.model.Account;
import com.theolympiastone.club.common.model.Zdhf;

public class MyZdhfService {
    public static final MyZdhfService me = new MyZdhfService();
    private final Zdhf dao = new Zdhf().dao();


    private String getSuffixString(Account account, String query, String queryKsrq, String queryJsrq, String queryKh, String queryYj, String queryKhddh, String queryHgbh, String queryPx) {
        boolean hasRole = AdminAuthService.me.hasRole(account.getId(), new String[]{"权限管理员", "超级管理员", "总经理"});
        String suffix = "";
        if (!hasRole) {
            suffix += " and z.cjr='" + account.getUserName() + "' ";
        }
        if (!StringUtils.isEmpty(query)) {
            suffix += " and z.ddbh like '%" + query + "%' ";
        }
        if (!StringUtils.isEmpty(queryKsrq)) {
            suffix += " and z.cjsj >= '" + queryKsrq + "' ";
        }
        if (!StringUtils.isEmpty(queryJsrq)) {
            suffix += " and z.cjsj <= '" + queryJsrq + "' ";
        }
        if (!StringUtils.isEmpty(queryKh)) {
            suffix += " and z.khid=" + queryKh + " ";
        }
        if (!StringUtils.isEmpty(queryYj)) {
            suffix += " and z.yjid=" + queryYj + " ";
        }
        if (!StringUtils.isEmpty(queryKhddh)) {
            suffix += " and z.gcbh like '%" + queryKhddh + "%' ";
        }
        if (!StringUtils.isEmpty(queryHgbh)) {
            suffix += " and z.ddbh like '%" + queryHgbh + "%' ";
        }
        suffix += queryPx;
        return suffix;
    }

    public Page<Zdhf> paginate(int pageNum, Account account, String query, String queryKsrq, String queryJsrq, String queryKh, String queryYj, String queryKhddh, String queryHgbh, String queryPx) {
        String suffix = getSuffixString(account, query, queryKsrq, queryJsrq, queryKh, queryYj, queryKhddh, queryHgbh, queryPx);
        return dao.paginate(pageNum, 100, "select z.*, DATE_FORMAT(z.cjsj,'%m%d %h%m') cjsjgsh,  k.jc jc, y.mc yjmc, c.mc szmc, j.mc jgmc, (select max(ifnull(status,'')) status from zdhfzt t where t.zdhf_id=z.id) ", "from zdhf z left join kh k on z.khid = k.id left join yj y on z.yjid = y.id left join cl c on z.zwsz = c.id left join jgfs j on z.zwjg = j.id  where 1=1 " + suffix + " ");
    }

    public Ret save(Zdhf zdhf) {
        zdhf.save();
        return Ret.ok("msg", "创建成功");
    }

    public Zdhf findById(Integer id) {
        return dao.findById(id);
    }

    public Ret update(Zdhf zdhf) {
        zdhf.update();
        return Ret.ok("msg", "修改成功");
    }

    public Ret delete(Integer id) {
        dao.deleteById(id);
        return Ret.ok("msg", "删除成功");
    }
}
