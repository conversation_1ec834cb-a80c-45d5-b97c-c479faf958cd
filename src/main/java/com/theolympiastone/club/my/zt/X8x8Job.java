package com.theolympiastone.club.my.zt;

import com.google.common.collect.Sets;
import org.apache.commons.io.FileUtils;
import us.codecraft.webmagic.Page;
import us.codecraft.webmagic.Site;
import us.codecraft.webmagic.Spider;
import us.codecraft.webmagic.processor.PageProcessor;

import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Set;

public class X8x8Job implements PageProcessor {
    private static final Set<String> urlSet = Sets.newHashSet();
    private final Site site = Site.me().setRetryTimes(3).setSleepTime(100);

    public static void main(String[] args) throws IOException {
        Spider spider = Spider.create(new X8x8Job());
        for (int i = 1; i < 1700; i++) {
            spider.addUrl("https://8x1288x.com/html/category/video/page_" + i + ".html");
        }
        spider.thread(5).run();
        FileUtils.writeLines(new File("/Users/<USER>/Downloads/x8x8.txt"), urlSet);
    }

    @Override
    public void process(Page page) {
        List<String> allLinks = page.getHtml().xpath("//*[@id='main']").links().regex("(https://8x1288x\\.com/\\w+/\\d+/)").all();
        urlSet.addAll(allLinks);
    }

    @Override
    public Site getSite() {
        return site;
    }
}
