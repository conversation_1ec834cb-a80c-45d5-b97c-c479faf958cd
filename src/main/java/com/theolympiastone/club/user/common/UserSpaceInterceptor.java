package com.theolympiastone.club.user.common;

import com.jfinal.aop.Interceptor;
import com.jfinal.aop.Invocation;
import com.theolympiastone.club.common.account.AccountService;
import com.theolympiastone.club.common.controller.BaseController;
import com.theolympiastone.club.common.model.Account;

/**
 * UserSpaceInterceptor 用户空间拦截器
 */
public class UserSpaceInterceptor implements Interceptor {

    public void intercept(Invocation inv) {
        BaseController c = (BaseController) inv.getController();

        // 如果登录用户进入的用户空间是自己的空间，则重定向到 /my/xxx
        if (c.isLogin() && c.getParaToInt() == c.getLoginAccountId()) {
            String newActionKey = inv.getActionKey().replaceFirst("/user", "/my");
            c.redirect(newActionKey, true);
            return;
        }

        // 为用户空间注入 Account user 对象，如果未传入 userId 或者用户找不到，则返回 404
        c.checkUrlPara(1);
        Account user = AccountService.me.getUsefulById(c.getParaToInt());
        if (user == null) {
            c.renderError(404);
            return;
        }
        c.setAttr("user", user);

        inv.invoke();
    }
}
