package com.theolympiastone.com.excel;

import com.jfinal.kit.PathKit;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.theolympiastone.club.common.kit.StringKit.isAbsolutePath;

/**
 * JBolt提供Excel导出封装类
 *
 * @ClassName: JBoltExcel
 * @author: JFinal学院-小木 QQ:909854136
 * @date: 2020年3月10日
 */
public class Excel {
    public static final int OPT_MODE_IMPORT = 1;//导入
    public static final int OPT_MODE_EXPORT = 2;//普通导出
    public static final int OPT_MODE_EXPORT_BY_TPL = 3;//普通加载模板后导出
    public static final int OPT_MODE_EXPORT_BY_DYNAMIC_TPL = 4;//加载动态模板后导出

    private final int optMode;//1  导入  2 导出操作类型 3 普通加载模板后导出 4 加载动态模板后导出
    private String fileName;//文件名
    private boolean big;//是否大文件导出设置
    private boolean xlsx;//格式设置
    private File fromFile;//读取用的文件
    private List<ExcelSheet> sheets;

    private Excel(int optMode) {
        this.optMode = optMode;
        this.big = false;
        this.xlsx = false;
    }

    private Excel(int optMode, File file) {
        if (file == null || !file.exists()) {
            throw new RuntimeException("File not exist:" + Objects.requireNonNull(file).getAbsolutePath());
        }
        this.optMode = optMode;
        this.big = false;
        this.xlsx = false;
        this.fromFile = file;
    }

    private Excel(File fromFile) {
        this(OPT_MODE_IMPORT);
        if (fromFile == null || !fromFile.exists()) {
            throw new RuntimeException("File not exist:" + Objects.requireNonNull(fromFile).getAbsolutePath());
        }
        this.fromFile = fromFile;
    }

    private Excel(String fromFilePath) {
        this(new File(fromFilePath));
    }

    public static Excel create() {
        return new Excel(OPT_MODE_EXPORT);
    }

    public static Excel useTpl(String tplPath) {
        return createByTpl(tplPath);
    }

    public static Excel useTpl(File tplFile) {
        return createByTpl(tplFile);
    }

    public static Excel createByTpl(String tplPath) {
        if (!tplPath.contains("exceltpl") && !isAbsolutePath(tplPath)) {
            tplPath = PathKit.getRootClassPath() + File.separator + "exceltpl" + File.separator + tplPath;
        }
        return createByTpl(new File(tplPath));
    }

    public static Excel createByTpl(File tplFile) {
        return new Excel(OPT_MODE_EXPORT_BY_TPL, tplFile);
    }

    public static Excel from(String filePath) {
        return new Excel(filePath);
    }

    public static Excel from(File file) {
        return new Excel(file);
    }

    public boolean isXlsx() {
        return xlsx;
    }

    public Excel setXlsx(boolean isXlsx) {
        if (this.big) {
            this.xlsx = true;
        } else {
            this.xlsx = isXlsx;
        }
        return this;
    }


    public byte[] toByteArray() {
        return ExcelUtil.getExcelBytes(this);
    }

    public boolean isBig() {
        return big;
    }

    public Excel setBig(boolean isBig) {
        this.big = isBig;
        if (isBig) {
            this.xlsx = true;
        }
        return this;
    }

    public String getFileName() {
        return fileName;
    }

    public Excel setFileName(String fileName) {
        this.fileName = fileName;
        return this;
    }

    public File getFromFile() {
        return fromFile;
    }

    public int getOptMode() {
        return optMode;
    }

    public Excel addSheet(ExcelSheet sheet) {
        if (this.sheets == null) {
            this.sheets = new ArrayList<>();
        }
        sheet.setExcel(this);
        this.sheets.add(sheet);
        return this;
    }

    public ExcelSheet getSheet(String name) {
        if (this.sheets == null || this.sheets.isEmpty()) {
            return null;
        }
        Optional<ExcelSheet> first = sheets.stream().filter(sheet -> sheet.getName().equals(name)).findFirst();
        return first.orElse(null);
    }

    public boolean isExportByTpl() {
        return optMode == OPT_MODE_EXPORT_BY_TPL;
    }

    public boolean isExport() {
        return optMode == OPT_MODE_EXPORT;
    }

    public boolean isImport() {
        return optMode == OPT_MODE_IMPORT;
    }

    public List<ExcelSheet> getSheets() {
        return sheets;
    }

    public Excel setSheets(ExcelSheet... sheets) {
        if (sheets != null) {
            for (ExcelSheet sheet : sheets) {
                addSheet(sheet);
            }
        }
        return this;
    }

    public void setSheets(List<ExcelSheet> sheets) {
        this.sheets = sheets;
    }


}
