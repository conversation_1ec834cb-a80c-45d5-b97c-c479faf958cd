#sql("orderType")
    select lmid, lmmc, lmsm, szlx from lcxmlb where px>(select px from lcxmlb where xmid='ddlcjk' and lmid=#para(0) ) order by px asc
#end

#sql("orderTypeList")
 select lmid, lmmc, lmsm, szlx, px from lcxmlb where sflr<>'否' and px < #para(0) order by px asc
#end

#sql("orderTypeByList")
 select lmid, lmmc, lmsm, szlx, px from lcxmlb where sflr<>'否' and #para(0) order by px asc
#end

#sql("orderMx")
select x.id,
       x.jkid,
       x.lmid,
       l.lmmc,
       l.szlx,
       l.lmsm,
       x.sz,
       x.czr,
       (select count(id)
        from xmjkrz
        where jkid = x.jkid
          and lmid = x.lmid) bzs
from xmjkmx x left join lcxmlb l on l.xmid='ddlcjk' and x.lmid=l.lmid where x.jkid=#para(0) order by x.id asc
#end

#sql("getReplyPage")
	select x.*, a.nickName, a.avatar
	    from xmjkrz x inner join account a on x.jlr = a.id
	    where jkid = #para(0)
	    and lmid = #para(1)
#end

#sql("getJklsrzList")
    select j.*, a.id as accountId, a.nickname, a.avatar from jklsrz j
    left join account a on j.czr=a.nickName where j.jkid=#para(0)
    order by j.id asc
#end
