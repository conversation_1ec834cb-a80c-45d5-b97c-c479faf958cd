#set(seoTitle="$[tableNameCN]管理 "+ (isAdd ? "创建" : "编辑"))
#@layout()
#define main()
#include("/_view/my/common/_my_menu_bar.html")

#define content()
<div>
    <div class="jf-breadcrumb-box">
        <ol class="jf-breadcrumb">
            <li><a href="/">首页</a></li>
            <li><a href="/my/$[tableName]">$[tableNameCN]</a></li>
            <li class="active">#(isAdd ? "创建$[tableNameCN]" : "编辑$[tableNameCN]")</li>
        </ol>
    </div>
    <div class="layui-card-body">
        <form id="mainForm" class="layui-form" style="margin-top: 30px;white-space:nowrap!important;" action="/my/$[tableName]/#(isAdd ? 'save' : 'update')" method="post">
            <input type="hidden" name="$[tableName].$[key]" value="#($[tableName].$[key]??)">
            <input type="hidden" name="$[tableName].cjr" value="#($[tableName].cjr??)">
            <input type="hidden" name="$[tableName].cjsj" value="#($[tableName].cjsj??)">

            <div class="layui-form-item">
                <div class="layui-input-inline">
                    <button class="layui-btn layui-btn-sm" lay-submit lay-filter="component-form-element">保存</button>
                    <button type="reset" class="layui-btn layui-btn-sm layui-btn-primary">重置</button>
                </div>
            </div>

            $[add-columns]
        </form>
    </div>
</div>
#end
#end

#define js()
<script type="text/javascript">
    $(document).ready(function () {
        $("form").ajaxForm({
            dataType: "json",
            before: function(){
                layer.load(0, {shade: false});
            },
            success: function (ret) {
                layer_alert_with_callback(ret.msg, ret.state, "/my/$[tableName]/edit?id="+ret.id);
            }, error: function (ret) {
                layer_alert(ret);
            }
        });
    });
</script>
#end
