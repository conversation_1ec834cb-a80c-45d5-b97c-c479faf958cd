#@adminLayout()

#define main()
<div class="jfa-header-box" id="jfa-header-box">
    <div class="jfa-crumbs" id="jfa-crumbs">
        系统菜单表
    </div>
    <div class="jfa-search-box"></div>
    #include("/_view/_admin/common/_header_right.html")
</div>

<div class="layui-panel">
    <div class="jfa-toolbar">
        <a class="layui-btn layui-btn-sm" href="/admin/cd/removeCdRecords">
            <i class="fa fa-trash"></i>
            清除菜单缓存
        </a>
        <a class="layui-btn layui-btn-sm" href="/admin/cd/add">
            <i class="fa fa-plus"></i>
            添加 系统菜单表
        </a>
    </div>
    <br>
    <form class="layui-form" method="post" action="/admin/cd">
        <div class="layui-form-item">
            <div class="form-inline">
                <div class="layui-input-inline">
                    <input class="layui-input" type="text" name="query" id="query" value="#(query??)" placeholder="输入内容查询">
                </div>
                <div class="layui-input-inline">
                    <input type="text" class="layui-input" name="queryKsrq" id="queryKsrq" value="#(queryKsrq??)" autocomplete="off" placeholder="yyyy-MM-dd">
                </div>
                <div class="layui-input-inline">
                    <input type="text" class="layui-input" name="queryJsrq" id="queryJsrq" value="#(queryJsrq??)" autocomplete="off" placeholder="yyyy-MM-dd">
                </div>
                <div class="layui-input-inline">
                    <select name="queryPx" lay-search>
                        <option value=" order by mc desc" #if(queryPx??==" order by mc desc") selected #end>名称倒序</option>
                        <option value=" order by mc asc" #if(queryPx??==" order by mc asc") selected #end>名称顺序</option>
                    </select>
                </div>
                <div class="layui-input-inline">
                    <select name="pageSize" lay-search>
                        <option value="30" #if(pageSize??==30) selected #end>30条/页</option>
                        <option value="50" #if(pageSize??==50) selected #end>50条/页</option>
                        <option value="100" #if(pageSize??==100) selected #end>100条/页</option>
                        <option value="200" #if(pageSize??==200) selected #end>200条/页</option>
                        <option value="300" #if(pageSize??==300) selected #end>300条/页</option>
                        <option value="500" #if(pageSize??==500) selected #end>500条/页</option>
                        <option value="1000" #if(pageSize??==1000) selected #end>1000条/页</option>
                    </select>
                </div>
                <div class="layui-input-inline" style="width: 400px;">
                    <button class="layui-btn layui-btn-sm" lay-submit lay-filter="component-form-element">查询</button>
                    <button type="button" class="layui-btn layui-btn-sm" id="uploadFile"><i class="layui-icon"></i>导入</button>
                    <a class="layui-btn layui-btn-sm" href="/download/系统菜单表模版.xls">模版下载</a>
                    <a class="layui-btn layui-btn-sm" href="/admin/cd/export" target="_blank">导出</a>
                </div>
            </div>
        </div>
    </form>
    <table class="layui-table" lay-size="sm">
        <thead>
        <tr>
            <th>父ID</th>
					<th>名称</th>
					<th>菜单图标</th>
					<th>链接</th>
					<th>链接打开方式</th>
					<th>菜单排序</th>
					<th>状态(0:禁用,1:启用)</th>
					<th>备注信息</th>
					<th>创建时间</th>
					<th>更新时间</th>
					<th>删除时间</th>
					
            <th style="width: 100px;">操作</th>
        </tr>
        </thead>
        <tbody>
        #for(x : page.list)
        <tr>
            <td>#(x.pid??)</td>
					<td>#(x.title??)</td>
					<td>#(x.icon??)</td>
					<td>#(x.href??)</td>
					<td>#(x.target??)</td>
					<td>#(x.sort??)</td>
					<td>#(x.status??)</td>
					<td>#(x.remark??)</td>
					<td>#(x.create_at??)</td>
					<td>#(x.update_at??)</td>
					<td>#(x.delete_at??)</td>
					
            <td>
                <div class="layui-btn-group">
                    <a class="layui-btn layui-btn-sm" href="/admin/cd/edit?id=#(x.id)">编辑</a>&nbsp;&nbsp;
                    #role("权限管理员", "超级管理员", "总经理")
                    <button class="layui-btn layui-btn-sm" onclick="confirmLayer('删除 #escape(x.title??) 后无法恢复，确定要删除？', '/admin/cd/delete?id=#(x.id)', this);">删除</button>
                    #end
                </div>
            </td>
        </tr>
        #end
        </tbody>
    </table>
    #@paginate(page.pageNumber, page.totalPage, "/admin/cd?query=" + query + "&queryKsrq=" + queryKsrq + "&queryJsrq=" + queryJsrq + "&queryPx=" + queryPx + "&pageSize" + pageSize + "&p=")
</div>
#end

#define js()
<script type="application/javascript">
    layui.laydate.render({
        elem: '#queryKsrq'
    });
    layui.laydate.render({
        elem: '#queryJsrq'
    });
</script>
#end
