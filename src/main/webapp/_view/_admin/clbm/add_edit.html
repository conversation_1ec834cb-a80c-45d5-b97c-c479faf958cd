#@adminLayout()

#define main()
#set(isAdd = clbm == null ? true : false, isEdit = !isAdd)

<div class="jfa-header-box" id="jfa-header-box">
    <div class="jfa-crumbs" id="jfa-crumbs">
        <a data-pjax class="layui-btn layui-btn-sm" href="/admin/clbm"><i class="layui-icon"></i></a>材料别名 / #(isAdd ? "创建" : "编辑")
    </div>

    <div class="jfa-search-box"></div>
    #include("/_view/_admin/common/_header_right.html")
</div>

<div class="layui-panel">
    <div class="jfa-content" id="jfa-content">
        <form class="layui-form" action="/admin/clbm/#(isAdd ? 'save' : 'update')"
              method="post">
            <input type="hidden" name="clbm.id" value="#(clbm.id??)">

            <div class="layui-form-item">
                <label class="layui-form-label">名称</label>
                 <div class="layui-input-inline">
                    <input type="text" class="layui-input" id="clbm.mc" name="clbm.mc" value="#(clbm.mc??)">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">对应石种名称</label>
                 <div class="layui-input-inline">
                    <select lay-verify="required" lay-search="" id="clbm.clid" name="clbm.clid">
                        <option></option>
                        #for( cl : clList )
                            <option value="#(cl.id??)" #if(cl.id??==clbm.clid??) selected #end>#(cl.mc??)</option>
                        #end
                    </select>
                </div>
            </div>

            

            <div class="layui-form-item">
                 <div class="layui-input-inline">
                    <input class="layui-btn layui-btn-sm" type="submit" value="提交"/>
                </div>
            </div>
        </form>
    </div>
</div>

<script type="text/javascript">
    $(document).ready(function () {
        $("form").ajaxForm({
            dataType: "json"
            , success: function (ret) {
                layer_alert_with_callback(ret.msg, ret.state, "/admin/clbm/edit?id="+ret.id);
            }
            , error: function (data) {
                layer_alert("网络操作失败，请咨询老柳! QQ75272683");
            }
        });
    });
</script>

#end
