#set(seoTitle="创建文档")
#@layout()
#define main()
<!-- 后台管理左侧菜单栏 -->
#include("/_view/_admin/common/_admin_menu_bar.html")

	<!-- 内容容器 -->
	<div class="jf-admin-panel-box jf-pull-right">

		<!-- 项目 -->
		<div class="jf-my-panel doc-admin-panel">
			<div class="jf-breadcrumb-box">
				<ol class="jf-breadcrumb">
					<li><a data-pjax href="/admin">后台管理</a></li>
					<li><a data-pjax href="/admin/doc">文档</a></li>
					<li class="active">创建文档</li>
				</ol>
			</div>

			<form class="layui-form" id="myArticleForm" action="/admin/doc/save" method="post">
				<div class="jf-my-article-add">
					<div class="doc-admin-menu-input-box">
						<div class="main-menu">
							<span class="label">主菜单</span>
							<input name="doc.mainMenu" placeholder="直接填写序号" >
						</div>
						<div class="sub-menu">
							<span class="label">子菜单</span>
							<input name="doc.subMenu" placeholder="一级菜单填写 0" >
						</div>
					</div>

					<div class="doc-admin-title">
						<span class="label">标题</span>
						<input type="text" name="doc.title" />
					</div>

					<div class="content-box"  style="line-height: 20px;" >
						<script id="container" name="doc.content" style="line-height: 20px;"  type="text/plain"></script>
					</div>
					<div class="submit-box doc-admin-btn">
						<select lay-verify="required" lay-search="" name="doc.publish" class="publish-select">
							<option value="0">草稿</option>
							<option value="1">发布</option>
						</select>
						<input class="submit-btn layui-btn" type="submit" value="提&nbsp;交" />
						<!--<a data-pjax class="submit-btn layui-btn" onclick="$('#myArticleForm').submit();">提&nbsp;交</a>-->
					</div>
				</div>
			</form>

		</div>

	</div>
#end

#define css()
	<link rel="stylesheet" type="text/css" href="/assets/css/jfinal-com-my-space-v1.0.css">
	<link rel="stylesheet" type="text/css" href="/assets/css/jfinal-com-admin-v1.0.css">
	<link rel="stylesheet" type="text/css" href="/assets/fancyBox/jquery.fancybox.css" media="screen" />
#end

#define js()
	#include("/_view/_admin/common/_admin_article_form_js.html")
	<script type="text/javascript">
		// 上传时在 url 中用问号挂参的方式添加额外的参数 uploadType，用于分类管理图片
		ue.ready(function() {
			ue.execCommand('serverparam', {
				'uploadType': 'document'
			});
		});

		// 选中左侧菜单项
		$(document).ready(function() {
			setCurrentMyMenu();
		});

		// 也用 ajax 提交比较好，这样有利于在出异常的时候信息不会丢失
		$(document).ready(function() {
			$("#myArticleForm").ajaxForm({
				dataType: "json"
				, beforeSubmit: function(formData, jqForm, options) {
					ue.sync();  // 同步一下 ueditor 中的数据到表单域
				}
				, success: function(ret) {
					layer_alert_with_callback(ret.msg, ret.state, "/admin/doc");
				}
				, error: function(ret) {
					layer_alert(ret);
				}
				, complete: function(ret) { }       // 无论是 success 还是 error，最终都会被回调
			});
		});
	</script>
#end