#@adminLayout()

#define main()
<div class="jfa-header-box" id="jfa-header-box">
	<div class="jfa-crumbs" id="jfa-crumbs">
		货代联系人
	</div>
	<div class="jfa-search-box"></div>
	#include("/_view/_admin/common/_header_right.html")
</div>

<div class="layui-panel">
	<div class="jfa-content" id="jfa-content">

		<div class="jfa-toolbar">
			<a data-pjax class="layui-btn layui-btn-sm" href="/admin/hdlxr/add">
				<i class="fa fa-plus"></i>
				添加 货代联系人
			</a>
		</div>

		<div class="jfa-table-box">
			<table class="layui-table" lay-size="sm">
				<thead>
				<tr>
					<th></th>
					<th>货代ID</th>
					<th>名称</th>
					<th>岗位</th>
					<th>座机</th>
					<th>手机</th>
					<th>邮箱</th>
					<th>QQ</th>
					
					<th style="width: 100px;">操作</th>
				</tr>
				</thead>
				<tbody>
				#for(x : page.getList())
				<tr>
					<td scope="row">#(x.id)</td>
					<td>#(x.hdid??)</td>
					<td>#(x.mc??)</td>
					<td>#(x.gw??)</td>
					<td>#(x.zj??)</td>
					<td>#(x.sj??)</td>
					<td>#(x.yx??)</td>
					<td>#(x.qq??)</td>
					
					<td class="jfa-operation-button">
						<a data-pjax href="/admin/hdlxr/edit?id=#(x.id)&p=#(page.pageNumber)">
							<i class="fa fa-pencil" title="编辑"></i>
						</a>
						<a data-delete
						   data-title="#escape(x.mc)"
						   data-action="/admin/hdlxr/delete?id=#(x.id)">
							<i class="fa fa-trash" title="删除"></i>
						</a>
					</td>
				</tr>
				#end
				</tbody>
			</table>
			<div>
				#@adminPaginate(page.pageNumber, page.totalPage, "/admin/hdlxr?p=")
			</div>
		</div>

	</div>
</div>

#end

