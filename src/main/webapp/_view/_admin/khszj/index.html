#@adminLayout()

#define main()
<div class="jfa-header-box" id="jfa-header-box">
    <div class="jfa-crumbs" id="jfa-crumbs">
        客户石种价格
    </div>
    #include("/_view/_admin/common/_header_right.html")
</div>

<div class="layui-panel">
    <div class="jfa-content" id="jfa-content">
        <div class="layui-row">
            <div class="layui-input-inline">
                <form method="post" class="layui-form" action="/admin/khszj">
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <div class="layui-input-inline" style="width:28px;text-align:center;line-height:40px;">客户</div>
                            <div class="layui-input-inline" style="width:150px;">
                                <select id="khid" name="khid" lay-verify="required" lay-search="">
                                    <option value=""></option>
                                    #for( kh : khList)
                                    <option value="#(kh.id??)" #if(kh.id.toString()??==khid??) selected #end>#(kh.jc??)</option>
                                    #end
                                </select>&nbsp;&nbsp;
                            </div>
                        </div>

                        <div class="layui-inline">
                            <div class="layui-input-inline" style="width:28px;text-align:center;line-height:40px;">石种</div>
                            <div class="layui-input-inline" style="width:150px;">
                                <select lay-verify="required" lay-search="" id="szid" name="szid">
                                    <option value=""></option>
                                    #for( sz : szList)
                                    <option value="#(sz.id??)" #if(sz.id.toString()??==szid??) selected #end>#(sz.mc??)</option>
                                    #end
                                </select>
                            </div>

                            <div class="layui-inline">
                                <div class="layui-input-inline" style="width:28px;text-align:center;line-height:40px;">货币</div>
                                <div class="layui-input-inline" style="width:90px;">
                                    <select lay-verify="required" lay-search="" id="hb" name="hb">
                                        <option value=""></option>
                                        <option value="美元" #if(hb??=="美元") selected #end>美元</option>
                                        <option value="欧元" #if(hb??=="欧元") selected #end>欧元</option>
                                        <option value="澳元" #if(hb??=="澳元") selected #end>澳元</option>
                                    </select>
                                </div>
                            </div>

                            <div class="layui-inline">
                                <div class="layui-input-inline" style="width:56px;text-align:center;line-height:40px;">贸易条款</div>
                                <div class="layui-input-inline" style="width:90px;">
                                    <select lay-verify="required" lay-search="" id="mytk" name="mytk">
                                        <option value=""></option>
                                        <option value="FOB" #if(mytk??=="FOB") selected #end>FOB</option>
                                        <option value="DDU" #if(mytk??=="DDU") selected #end>DDU</option>
                                        <option value="DDP" #if(mytk??=="DDP") selected #end>DDP</option>
                                        <option value="CIF" #if(mytk??=="CIF") selected #end>CIF</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <div class="layui-input-inline" style="width:56px;text-align:center;line-height:40px;">产品价格条款</div>
                                <div class="layui-input-inline" style="width:90px;">
                                    <select lay-verify="required" lay-search="" id="cpjgtk" name="cpjgtk">
                                        <option value=""></option>
                                        <option value="FOB" #if(cpjgtk??=="FOB") selected #end>FOB</option>
                                        <option value="DDU" #if(cpjgtk??=="DDU") selected #end>DDU</option>
                                        <option value="DDP" #if(cpjgtk??=="DDP") selected #end>DDP</option>
                                        <option value="CIF" #if(cpjgtk??=="CIF") selected #end>CIF</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <div class="layui-input-inline" style="width:56px;text-align:center;line-height:40px;">是否正常</div>
                                <div class="layui-input-inline" style="width:90px;">
                                    <select lay-verify="required" lay-search="" id="sfzc" name="sfzc">
                                        <option value=""></option>
                                        <option value="0" #if(sfzc??=="0") selected #end>正常</option>
                                        <option value="1" #if(sfzc??=="1") selected #end>不正常</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <div class="layui-input-inline" style="width:70px;">
                                    <input type="submit" class="layui-btn layui-btn-sm" value="查找">
                                </div>
                                <div class="layui-input-inline" style="width:70px;text-align:center;line-height:40px;">
                                    <a href="#" onclick="doExport('#myTable01', {type: 'xlsx', htmlHyperlink: 'content'});">
                                        <img src="/assets/img/xls.png" alt="XLSX" style="width:24px">导出</a>
                                </div>
                            </div>
                        </div>

                        <div class="layui-input-inline" style="width: 900px;">
                            <button type="button" class="layui-btn layui-btn-sm" id="uploadFile"><i class="layui-icon"></i>导入</button>
                            <button type="button" class="layui-btn layui-btn-sm" id="uploadUpdateFile"><i class="layui-icon"></i>导出模版导入</button>
                            <a class="layui-btn layui-btn-sm" href="/download/客户石种价格导入模版和说明.csv" target="_blank">模板下载</a>
                            <button type="button" class="layui-btn layui-btn-sm" onclick="pl('plsc')">批量删除</button>
                            <button type="button" class="layui-btn layui-btn-sm" onclick="pl('plqy')">启用</button>
                            <button type="button" class="layui-btn layui-btn-sm" onclick="pl('pljy')">禁用</button>
                            <a data-pjax class="layui-btn layui-btn-sm" href="/admin/khszj/add">
                                <i class="fa fa-plus"></i>
                                添加 客户石种价格
                            </a>
                        </div>
                        <div class="form-inline">
                            <label class="layui-form-label">结束时间设置</label>
                            <div class="layui-input-inline">
                                <input type="text" class="layui-input" id="jssj" name="jssj" autocomplete="off">
                            </div>
                            <div class="layui-input-inline">
                                <button type="button" class="layui-btn layui-btn-sm" onclick="gx()">更新时间</button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

        </div>

        <div class="jfa-table-box">
            <table id="myTable01" class="layui-table" lay-size="sm">
                <thead>
                <tr>
                    <th style="width: 2%"><input type="checkbox" name="allCheckBox" id="allCheckBox" onclick="checkAllCheckBox()"></th>
                    <th>ID</th>
                    <th>客户</th>
                    <th>石种</th>
                    <th>石种英文</th>
                    <th>价格</th>
                    <th>不正常?</th>
                    <th>单位</th>
                    <th>币种</th>
                    <th>结束时间</th>
                    <th>产品价格条款</th>
                    <th>状态</th>
                    <th style="width: 100px;">操作</th>
                </tr>
                </thead>
                <tbody>
                #for(x : page.getList())
                <tr>
                    <td><input type="checkbox" name="oneCheckBox" value="#(x.id??)"></td>
                    <th scope="row">#(x.id)</th>
                    <td>#(x.khjc??)</td>
                    <td>#(x.clmc??)</td>
                    <td>#(x.ywmc??)</td>
                    <td><input type="text" value="#(x.jg??)" onchange="modify('#(x.id??)', this.value)" onblur="modify('#(x.id??)', this.value)" size="20"></td>
                    <td><input data-id="#(x.id)" data-lx="zc" #if(x.zc??!=0) checked #end type="checkbox" name="mgcCheck" class="mgc-switch mgc-tiny"></td>
                    <td>#(x.dw??)</td>
                    <td>#(x.hb??)</td>
                    <td>#(x.jssj??)</td>
                    <td><input type="text" value="#(x.bz??)" onchange="modifyBz('#(x.id??)', this.value)" onblur="modifyBz('#(x.id??)', this.value)" size="20"></td>
                    <td>
                        <input data-id="#(x.id)" data-lx="zt" #if(x.isEnabledOk()) checked #end type="checkbox" class="mgc-switch mgc-tiny">
                    </td>
                    <td class="jfa-operation-button">
                        <a data-pjax href="/admin/khszj/edit?id=#(x.id)&p=#(page.pageNumber)">
                            <i class="fa fa-pencil" title="编辑"></i>
                        </a>
                        <a data-delete
                           data-title="#escape(x.khjc) - #escape(x.clmc)"
                           data-action="/admin/khszj/delete?id=#(x.id)">
                            <i class="fa fa-trash" title="删除"></i>
                        </a>
                    </td>
                </tr>
                #end
                </tbody>
            </table>
            <div>
                #@adminPaginate(page.pageNumber, page.totalPage, "/admin/khszj?p=")
            </div>
        </div>

    </div>
</div>

<script type="application/javascript" src="/assets/export/js-xlsx/xlsx.core.min.js"></script>
<script type="application/javascript" src="/assets/export/tableExport.min.js"></script>
<script>
    $(document).ready(function() {
        initMagicInput(prepareAction);
    });

    function prepareAction($this, state) {
        return {
            url: "/admin/khszj/enable",
            data : {
                id: $this.attr("data-id"),
                lx: $this.attr("data-lx")
            }
        }
    }

    layui.use('laydate', function() {
        var laydate = layui.laydate;
        laydate.render({
            elem: document.getElementById('jssj')
        });
    });
    function checkAllCheckBox() {
        if ($("#allCheckBox").is(':checked')) {
            $.each($('input:checkbox[name="oneCheckBox"]'), function () {
                $(this).prop("checked", true);
            });
        } else {
            $.each($('input:checkbox:checked[name="oneCheckBox"]'), function () {
                $(this).prop("checked", false);
            });
        }
    }
    function gx() {
        if ($('input[type=checkbox]:checked[name="oneCheckBox"]').not("#allCheckBox").length === 0) {
            layer_alert("没有选中的要更新的客户石种价格");
        } else {
            let ids = $.map($('input[type=checkbox]:checked[name="oneCheckBox"]').not("#allCheckBox"), e => $(e).val()).join(',');
            $.ajax({
                url: "/admin/khszj/gx",
                async: true,
                type: "POST",
                cache: false,
                data: {
                    jssj: $("#jssj").val(),
                    ids: ids
                },
                success: function (data) {
                    layer_alert(data.msg);
                    window.location.reload();
                },
                error: function (data) {
                    layer_alert(data.msg);
                },
                dataType: "json"
            });
        }
    }

    function pl(action) {
        if ($('input[type=checkbox]:checked[name="oneCheckBox"]').not("#allCheckBox").length === 0) {
            layer_alert("没有选中的要删除的客户石种价格");
        } else {
            let ids = $.map($('input[type=checkbox]:checked[name="oneCheckBox"]').not("#allCheckBox"), e => $(e).val()).join(',');
            console.log(ids);
            $.ajax({
                url: "/admin/khszj/"+action,
                async: true,
                type: "POST",
                cache: false,
                data: {
                    ids: ids
                },
                success: function (data) {
                    layer_alert(data.msg);
                    window.location.reload();
                },
                error: function (data) {
                    layer_alert(data.msg);
                },
                dataType: "json"
            });
        }
    }
    function modify(id, value){
        $.ajax({
            url: "/admin/khszj/editJg",
            type: "POST",
            cache: false,
            dataType: "json",
            before: function(){
                layer.load(0, {shade: false});
            },
            data: {
                "id": id,
                "jg": value,
            }
        });
    }
    function modifyBz(id, value){
        $.ajax({
            url: "/admin/khszj/editBz",
            type: "POST",
            cache: false,
            dataType: "json",
            before: function(){
                layer.load(0, {shade: false});
            },
            data: {
                "id": id,
                "bz": value,
            }
        });
    }

    layui.upload.render({
        elem: '#uploadFile'
        , url: '/admin/khszj/importFile'
        , accept: 'file'
        , timeout: 300000
        , done: function (res) {
            location.reload();
        }
    });
    layui.upload.render({
        elem: '#uploadUpdateFile'
        , url: '/admin/khszj/importUpdateFile'
        , accept: 'file'
        , timeout: 300000
        , done: function (res) {
            location.reload();
        }
    });

    layui.form.render('select');

    function doExport(selector, params) {
        var options = {
            fileName: '客户石种价格'
        };

        jQuery.extend(true, options, params);

        $(selector).tableExport(options);
    }

</script>
#end
