#@adminLayout()

#define main()
<div class="jfa-header-box" id="jfa-header-box">
    <div class="jfa-crumbs" id="jfa-crumbs">
        客户回访审核
    </div>
    <div class="jfa-search-box">
    </div>
    #include("/_view/_admin/common/_header_right.html")
</div>
<div class="layui-panel">
    <br>
    <form method="post" action="/admin/khxszz" class="layui-form layui-bg-gray">
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">客户名称</label>
                 <div class="layui-input-inline">
                    <input type="text" name="query" value="#(query??)" autocomplete="off" class="layui-input">
                </div>
            </div>

            <div class="layui-inline">
                <label class="layui-form-label">范围</label>
                 <div class="layui-input-inline">
                    <input type="text" id="queryZjgtsj" name="queryZjgtsj" value="#(queryZjgtsj??)" placeholder="yyyy-MM-dd" autocomplete="off" class="layui-input">
                </div>
                <div class="layui-form-mid">-</div>
                 <div class="layui-input-inline">
                    <input type="text" id="queryJssj" name="queryJssj" value="#(queryJssj??)" placeholder="yyyy-MM-dd" autocomplete="off" class="layui-input">
                </div>
            </div>

            #role("权限管理员", "超级管理员", "总经理")
            <div class="layui-inline">
                <label class="layui-form-label">业务员邮箱</label>
                 <div class="layui-input-inline">
                    <input type="text" name="queryTxr" value="#(queryTxr??)" autocomplete="off" class="layui-input">
                </div>
            </div>
            #end

            <div class="layui-inline">
                <label class="layui-form-label">国别</label>
                 <div class="layui-input-inline">
                    <input type="text" name="queryGb" value="#(queryGb??)" autocomplete="off" class="layui-input">
                </div>
            </div>

            <div class="layui-inline">
                <label class="layui-form-label">采购源地</label>
                 <div class="layui-input-inline">
                    <input type="text" name="queryCgyd" value="#(queryCgyd??)" autocomplete="off" class="layui-input">
                </div>
            </div>

            <div class="layui-inline">
                <label class="layui-form-label">排序</label>
                 <div class="layui-input-inline">
                    <select lay-verify="required" lay-search="" name="queryPx">
                        <option value=" order by a.gtrq desc">最近沟通倒序</option>
                        <option value=" order by a.gtrq asc">最近沟通顺序</option>
                        <option value=" order by b.mc desc">客户名称倒序</option>
                        <option value=" order by b.mc asc">客户名称顺序</option>
                    </select>
                </div>
            </div>

            <div class="layui-inline">
                <label class="layui-form-label"></label>
                 <div class="layui-input-inline">
                    <input type="submit" class="layui-btn layui-btn-sm" value="查找">
                    <input type="button" class="layui-btn layui-btn-sm" onclick="pljy()" value="批量审核">
                </div>
            </div>
        </div>
    </form>

    <div class="jfa-table-box">
        <table class="layui-table" lay-size="sm">
            <thead>
            <tr>
                <th style="width: 1%"><input type="checkbox" name="allCheckBox" id="allCheckBox" onclick="checkAllCheckBox()"></th>
                <th style="width: 4%">业务员</th>
                <th style="width: 4%">国别</th>
                <th style="width: 10%">客户名称</th>
                <th style="width: 5%">客户性质</th>
                <th style="width: 5%">采购源地</th>
                <th style="width: 6%">沟通日期</th>
                <th style="width: 10%">沟通纪要</th>
                <th style="width: 20%">沟通结果和行动</th>
                <th style="width: 10%">沟通文件</th>
                <th style="width: 5%">有效沟通?</th>
                <th style="width: 10%">建议</th>

                <th style="width: 5%">操作</th>
            </tr>
            </thead>
            <tbody>
            #for(x : page.getList())
            <tr>
                <td><input type="checkbox" value="#(x.id??)"></td>
                <td>#(x.ywy??)</td>
                <td>#(x.gb??)</td>
                <td>#(x.mc??)</td>
                <td>#(x.xz??)</td>
                <td>#(x.cgyd??)</td>
                <td>#(x.gtrq??)</td>
                <td>#(x.gtjy??)</td>
                <td>#(x.gtjg??)</td>
                <td><a data-pjax href="/upload/khxszz/#(x.khid??)/#(x.wj??)" target="_blank">#(x.wj??)</a></td>
                <td>#(x.yxgt??)</td>
                <td>#(x.jy??)</td>
                <td class="jfa-operation-button">
                    <a data-pjax href="/admin/khxszz/edit?id=#(x.id)&p=#(page.pageNumber)">
                        <i class="fa fa-pencil" title="编辑"></i>
                    </a>
                    <a data-delete
                       data-title="#escape(x.mc)"
                       data-action="/admin/khxszz/delete?id=#(x.id)">
                        <i class="fa fa-trash" title="删除"></i>
                    </a>
                </td>
            </tr>
            #end
            </tbody>
        </table>
        <div>
            #@adminPaginate(page.pageNumber, page.totalPage, "/admin/khxszz?p=")
        </div>
    </div>
</div>
<div id="plModal" style="display: none; width: 600px; height: 400px;">
    <form class="layui-form" action="">
        <div class="layui-form-item">
            <label class="layui-form-label">有效沟通?</label>
            <div class="layui-input-block">
                <select lay-verify="required" lay-search="" id="yxgt" name="yxgt">
                    <option value="">请选择</option>
                    <option value="有效">有效</option>
                    <option value="无效">无效</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label">建议</label>
            <div class="layui-input-block">
                <textarea id="jy" name="jy" placeholder="请填写建议" class="layui-textarea"></textarea>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-input-block">
                <button class="layui-btn layui-btn-sm" onclick="addPlzz()">立即提交</button>
                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
            </div>
        </div>
    </form>
</div>
#end

#define js()
<script type="text/javascript">
    layui.use('laydate', function() {
        var laydate = layui.laydate;

        laydate.render({
            elem: '#queryZjgtsj'
        });
        laydate.render({
            elem: '#queryJssj'
        });
    });
    function addPlzz() {
        let ids = $.map($('input[type=checkbox]:checked').not("#allCheckBox"), e => $(e).val()).join();
        var jy = $('#jy').val();
        if(jy==undefined || jy==null){
            jy = "";
        }
        $.ajax({
            url: "/admin/khxszz/plJy",
            async: true,
            type: "POST",
            cache: false,
            data: {
                ids: ids,
                yxgt: $('#yxgt').val(),
                jy: jy
            },
            success: function (data) {
                showReplyErrorMsg(data.msg);
                window.location.reload();
            },
            error: function (data) {
                showReplyErrorMsg(data.msg);
            },
            dataType: "json"
        });
    }

    function pljy() {
        if ($('input[type=checkbox]:checked').length === 0) {
            layer_alert("没有选中的要审核的回访");
        } else {
            layer.open({
                type: 1,
                title: "批量审核",
                content: $('#plModal')
            });
        }
    }

    function checkAllCheckBox() {
        if ($("#allCheckBox").is(':checked')) {
            $.each($('input:checkbox'), function () {
                $(this).prop("checked", true);
            });
        } else {
            $.each($('input:checkbox:checked'), function () {
                $(this).prop("checked", false);
            });
        }
    }
</script>
#end
