#@adminLayout()

#define main()
<div class="jfa-header-box" id="jfa-header-box">
	<div class="jfa-crumbs" id="jfa-crumbs">
		面试人
	</div>
	<div class="jfa-search-box"></div>
	#include("/_view/_admin/common/_header_right.html")
</div>

<div class="layui-panel">
	<div class="jfa-content" id="jfa-content">

		<div class="jfa-toolbar">
			<a data-pjax class="layui-btn layui-btn-sm" href="/admin/msr/add">
				<i class="fa fa-plus"></i>
				添加 面试人
			</a>
		</div>

		<div class="jfa-table-box">
			<table class="layui-table" lay-size="sm">
				<thead>
				<tr>
					<th>ID</th>
					<th>姓名</th>
					<th>电话</th>
					<th>邮箱</th>
					<th>性别</th>
					<th>户口所在地</th>
					<th>微信</th>
					<th>QQ</th>
					<th>最高院校</th>
					
					<th style="width: 100px;">操作</th>
				</tr>
				</thead>
				<tbody>
				#for(x : page.getList())
				<tr>
					<th scope="row">#(x.id)</th>
					<td>#(x.xm??)</td>
					<td>#(x.dh??)</td>
					<td>#(x.yx??)</td>
					<td>#(x.xb??)</td>
					<td>#(x.hk??)</td>
					<td>#(x.wx??)</td>
					<td>#(x.qq??)</td>
					<td>#(x.zgyx??)</td>
					
					<td class="jfa-operation-button">
						<a data-pjax href="/admin/msr/edit?id=#(x.id)&p=#(page.pageNumber)">
							<i class="fa fa-pencil" title="编辑"></i>
						</a>
						<a data-delete
						   data-title="#escape(x.xm)"
						   data-action="/admin/msr/delete?id=#(x.id)">
							<i class="fa fa-trash" title="删除"></i>
						</a>
					</td>
				</tr>
				#end
				</tbody>
			</table>
			<div>
				#@adminPaginate(page.pageNumber, page.totalPage, "/admin/msr?p=")
			</div>
		</div>

	</div>
</div>

#end

