#@adminLayout()

#define main()
<div class="jfa-header-box" id="jfa-header-box">
	<div class="jfa-crumbs" id="jfa-crumbs">
		日报
	</div>
	<div class="jfa-search-box"></div>
	#include("/_view/_admin/common/_header_right.html")
</div>

<div class="layui-panel">
	<div class="jfa-content" id="jfa-content">

		<div class="jfa-toolbar">
			<a data-pjax class="layui-btn layui-btn-sm" href="/admin/rb/add">
				<i class="fa fa-plus"></i>
				添加 日报
			</a>
		</div>

		<div class="jfa-table-box">
			<table class="layui-table" lay-size="sm">
				<thead>
				<tr>
					<th>记录人</th>
					<th>记录日期</th>
					<th>记录内容</th>
					<th>附件</th>
					<th>评语</th>
					
					<th style="width: 100px;">操作</th>
				</tr>
				</thead>
				<tbody>
				#for(x : page.getList())
				<tr>
					<td>#(x.username??)</td>
					<td>#(x.jlrq??)</td>
					<td>#(x.jlnr??)</td>
					<td>
						#if(x.wj??)
						#set(wjAttr=(x.wj).split(";"))
						#for(wj : wjAttr)
						<a data-pjax href="/upload/rb/#(wj??)" title="#(wj??)" target="_blank">#(wj??)</a>&nbsp;
						#end
						#end
					</td>
					<td>#(x.py??)</td>
					
					<td class="jfa-operation-button">
						<a data-pjax href="/admin/rb/edit?id=#(x.id)&p=#(page.pageNumber)">
							<i class="fa fa-pencil" title="编辑"></i>
						</a>
						<a data-delete
						   data-title="#escape(x.jlnr)"
						   data-action="/admin/rb/delete?id=#(x.id)">
							<i class="fa fa-trash" title="删除"></i>
						</a>
					</td>
				</tr>
				#end
				</tbody>
			</table>
			<div>
				#@adminPaginate(page.pageNumber, page.totalPage, "/admin/rb?p=")
			</div>
		</div>

	</div>
</div>

#end

