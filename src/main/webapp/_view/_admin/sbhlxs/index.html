#@adminLayout()

#define main()
<div class="jfa-header-box" id="jfa-header-box">
	<div class="jfa-crumbs" id="jfa-crumbs">
		申报汇率系数
	</div>
	<div class="jfa-search-box"></div>
	#include("/_view/_admin/common/_header_right.html")
</div>

<div class="layui-panel">
	<div class="jfa-content" id="jfa-content">

		<div class="jfa-toolbar">
			<a data-pjax class="layui-btn layui-btn-sm" href="/admin/sbhlxs/add">
				<i class="fa fa-plus"></i>
				添加 申报汇率系数
			</a>
		</div>

		<div class="jfa-table-box">
			<table class="layui-table" lay-size="sm">
				<thead>
				<tr>
					<th>货币</th>
					<th>系数</th>
					<th>开始时间</th>
					<th>结束时间</th>

					<th style="width: 100px;">操作</th>
				</tr>
				</thead>
				<tbody>
				#for(x : page.getList())
				<tr>
					<td>#(x.hb??)</td>
					<td>#(x.xs??)</td>
					<td>#(x.kssj??)</td>
					<td>#(x.jssj??)</td>

					<td class="jfa-operation-button">
						<a data-pjax href="/admin/sbhlxs/edit?id=#(x.id)&p=#(page.pageNumber)">
							<i class="fa fa-pencil" title="编辑"></i>
						</a>
						<a data-delete
						   data-title="#escape(x.hb) - #escape(x.xs)"
						   data-action="/admin/sbhlxs/delete?id=#(x.id)">
							<i class="fa fa-trash" title="删除"></i>
						</a>
					</td>
				</tr>
				#end
				</tbody>
			</table>
			<div>
				#@adminPaginate(page.pageNumber, page.totalPage, "/admin/sbhlxs?p=")
			</div>
		</div>

	</div>
</div>

#end

