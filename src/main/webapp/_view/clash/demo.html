<svg width="700" height="50" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <pattern id="backgroundPattern" width="680" height="130" patternUnits="userSpaceOnUse">
        <image xlink:href="data:image/png;base64,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" width="680" height="130"/>
        <animateTransform attributeName="patternTransform" type="translate" from="0,0" to="-680,0" begin="0s" dur="30s" repeatCount="indefinite"/>
    </pattern>
    <style>
        .text-style {
            font-size: 32px;
            font-weight: bold;
            text-anchor: middle;
            fill: url(#backgroundPattern);
        }
    </style>
    <text x="50%" y="0%" class="text-style">
        <tspan x='50%' dy='1.2em'>&#xF8FF;己不由心 身又岂能由己</tspan>
    </text>
</svg>

<span style="color:#ff6699; border: 1px solid #ff6699;border-radius: 5px; padding-left: 5px; font-size: 24px;">
    <svg height="30" width="30">
         <use href="#beats"></use>   <!--href="#beats"所指向的就是上面<symbol>标签的id-->
    </svg>
    <span>直播中</span>
</span>

<svg height="0" width="0">
    <symbol id="clock" viewBox="-52 -52 104 104">
        <circle fill="none" stroke="currentColor" stroke-width="6" stroke-miterlimit="10" cx="0" cy="0" r="48"/>
        <line class="fast-hand" fill="none" stroke-linecap="round" stroke="currentColor" stroke-width="6" stroke-miterlimit="10" x1="0" y1="0" x2="35" y2="0.5"></line>
        <line class="slow-hand" fill="none" stroke-linecap="round" stroke="currentColor" stroke-width="6" stroke-miterlimit="10" x1="0" y1="0" x2="-0.5" y2="-24"></line>
    </symbol>
</svg>
<div style="color:#fa8919; font-size: 16px;" >
    <svg height="26" width="26" style="vertical-align: top; margin-top: -2px;margin-right:3px;">
        <use href="#clock"></use>
    </svg>
    <span>等待中</span>
</div>

<svg id="robot" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0" y="0" width="320px" height="300px" viewBox="0 0 160 300" enable-background="new 0 0 320 300" xml:space="preserve">
    <rect x="59.722" y="72.779" fill="#333" width="40.557" height="27.564" />
    <g id="head" class="up">
        <g id="leftAntenna">
            <path fill="none" stroke="#ccc" stroke-width="1.5" stroke-miterlimit="10" d="M77.519,25.869
		C75.85,13.604,65.745,3.39,53.972,3.39">
                <animate attributeName="d" calcMode="spline" keySplines="0.42 0 0.58 1; 0.42 0 0.58 1; 0.42 0 0.58 1" begin="0s" dur="0.75s" repeatCount="indefinite" values="
                          M77.519,25.869C75.85,13.604,65.745,3.39,53.972,3.39
                          ;
                          M77.519,25.869C75.85,13.604,65.745,3.39,53.972,12
                          ;
                          M77.519,25.869C75.85,13.604,65.745,3.39,53.972,0
                          ;
                          M77.519,25.869C75.85,13.604,65.745,3.39,53.972,3.39
                          " />
            </path>
            <ellipse fill="#ccc" cx="55.021" cy="3.39" rx="3.344" ry="3.391">
                <animate dur="0.75s" attributeName="cy" calcMode="spline" keySplines="0.42 0 0.58 1; 0.42 0 0.58 1; 0.42 0 0.58 1" begin="0s" values="
                           3.39;
                           12;
                           0;
                           3.39
                           " repeatCount="indefinite" />
            </ellipse>
        </g>
        <g id="rightAntenna">
            <path fill="none" stroke="#ccc" stroke-width="1.5" stroke-miterlimit="10" d="M82.48,25.869
		C84.15,13.604,94.255,3.39,106.028,3.39">
                <animate attributeName="d" calcMode="spline" keySplines="0.42 0 0.58 1; 0.42 0 0.58 1; 0.42 0 0.58 1" begin="0s" dur="0.6s" repeatCount="indefinite" values="
                          M82.48,25.869C84.15,13.604,94.255,3.39,106.028,3.39
                          ;
                          M82.48,25.869C84.15,13.604,94.255,3.39,106.028,10.39
                          ;
                          M82.48,25.869C84.15,13.604,94.255,3.39,106.028,-5.39
                          ;
                          M82.48,25.869C84.15,13.604,94.255,3.39,106.028,3.39
                          " />
            </path>
            <ellipse fill="#ccc" cx="104.979" cy="3.39" rx="3.344" ry="3.391">
                <animate dur="0.6s" attributeName="cy" calcMode="spline" keySplines="0.42 0 0.58 1; 0.42 0 0.58 1; 0.42 0 0.58 1" begin="0s" values="
                           3.39;
                           10.39;
                           -5.39;
                           3.39
                           " repeatCount="indefinite" />
            </ellipse>
        </g>
        <path fill="#333" d="M96.079,32.57v-8.546c-10.72-3.765-21.437-3.98-32.156,0v8.546H96.079z" />
        <path fill="#f54f59" d="M112.809,28.372H80H47.19c-5.814,18.663-5.499,37.322,0,55.983H80h32.811
		C118.309,65.694,118.625,47.035,112.809,28.372z" />
        <g>
            <g id="eyeLeft">
                <path fill="#FFFFFF" d="M72.116,47.955c0,5.443-4.045,9.853-9.033,9.853h-1.971c-4.988,0-9.032-4.41-9.032-9.853
				s4.044-9.856,9.032-9.856h1.971C68.071,38.099,72.116,42.512,72.116,47.955z">
                    <animate attributeName="d" calcMode="spline" keySplines="0.42 0 0.58 1; 0.42 0 0.58 1" begin="5s" dur="0.8s" repeatCount="indefinite" values="
                          M72.116,47.955c0,5.443-4.045,9.853-9.033,9.853h-1.971c-4.988,0-9.032-4.41-9.032-9.853s4.044-9.856,9.032-9.856h1.971C68.071,38.099,72.116,42.512,72.116,47.955z
                          ;
                          M72.116,47.955c0,5.443-0.045,9.853-9.033,9.853h-1.971c-4.988,0-9.032-4.41-9.032-9.853s4.044-4.856,9.032-4.856h1.971C68.071,45.099,72.116,42.512,72.116,47.955z
                          ;
                          M72.116,47.955c0,5.443-4.045,9.853-9.033,9.853h-1.971c-4.988,0-9.032-4.41-9.032-9.853s4.044-9.856,9.032-9.856h1.971C68.071,38.099,72.116,42.512,72.116,47.955z
                          " />
                </path>
                <path d="M66.614,47.955c0,2.176-1.618,3.942-3.613,3.942h-1.807c-1.994,0-3.612-1.766-3.612-3.942
				c0-2.178,1.618-3.943,3.612-3.943H63C64.996,44.012,66.614,45.777,66.614,47.955z" />
            </g>
            <g>
                <path fill="#FFFFFF" d="M107.92,47.955c0,5.443-4.045,9.853-9.031,9.853h-1.973c-4.986,0-9.031-4.41-9.031-9.853
				s4.045-9.856,9.031-9.856h1.973C103.875,38.099,107.92,42.512,107.92,47.955z">
                    <animate attributeName="d" calcMode="spline" keySplines="0.42 0 0.58 1; 0.42 0 0.58 1" begin="5s" dur="0.8s" repeatCount="indefinite" values="
                          M107.92,47.955c0,5.443-4.045,9.853-9.031,9.853h-1.973c-4.986,0-9.031-4.41-9.031-9.853s4.045-9.856,9.031-9.856h1.973C103.875,38.099,107.92,42.512,107.92,47.955z
                          ;
                          M107.92,47.955c0,5.443-4.045,9.853-9.031,9.853h-1.973c-4.986,0-9.031-4.41-9.031-9.853s4.045-4.856,9.031-4.856h1.973C103.875,45.099,107.92,42.512,107.92,47.955z
                          ;
                          M107.92,47.955c0,5.443-4.045,9.853-9.031,9.853h-1.973c-4.986,0-9.031-4.41-9.031-9.853s4.045-9.856,9.031-9.856h1.973C103.875,38.099,107.92,42.512,107.92,47.955z
                          " />
                </path>
                <path d="M102.417,47.955c0,2.176-1.616,3.942-3.612,3.942h-1.807c-1.994,0-3.611-1.766-3.611-3.942
				c0-2.178,1.617-3.943,3.611-3.943h1.807C100.801,44.012,102.417,45.777,102.417,47.955z" />
            </g>
        </g>
        <path fill="#FFFFFF" d="M103.383,69.778c0,1.381-0.836,2.499-1.871,2.499c-10.756,0-32.269,0-43.025,0
		c-1.033,0-1.871-1.118-1.871-2.499c0-1.378,0.838-2.496,1.871-2.496c10.756,0,32.269,0,43.025,0
		C102.547,67.282,103.383,68.4,103.383,69.778z">
            <animate attributeName="d" calcMode="spline" keySplines="0.42 0 0.58 1; 0.42 0 0.58 1; 0.42 0 0.58 1" begin="0s" dur="1s" repeatCount="indefinite" values="
                          M103.383,69.778c0,1.381-0.836,2.499-1.871,2.499c-10.756,0-32.269,0-43.025,0
		c-1.033,0-1.871-1.118-1.871-2.499c0-1.378,0.838-2.496,1.871-2.496c10.756,0,32.269,0,43.025,0
		C102.547,67.282,103.383,68.4,103.383,69.778z
                          ;
                          M103.383,69.778c0,1.381-0.862,2.268-1.871,2.499c-11.669,2.677-29.396,3.207-43.025,0
		c-1.005-0.236-1.871-1.118-1.871-2.499c0-1.378,0.868-2.249,1.871-2.496c11.349-2.795,31.753-2.53,43.025,0
		C102.521,67.508,103.383,68.4,103.383,69.778z
                          ;
                          M103.383,69.778c0,1.381-0.86,2.724-1.871,2.499c-11.271-2.506-29.956-2.201-43.025,0
		c-1.019,0.171-1.871-1.118-1.871-2.499c0-1.378,0.89-2.819,1.871-2.496c15.191,4.995,30.429,3.433,43.025,0
		C102.511,67.01,103.383,68.4,103.383,69.778z
                          ;
                          M103.383,69.778c0,1.381-0.836,2.499-1.871,2.499c-10.756,0-32.269,0-43.025,0
		c-1.033,0-1.871-1.118-1.871-2.499c0-1.378,0.838-2.496,1.871-2.496c10.756,0,32.269,0,43.025,0
		C102.547,67.282,103.383,68.4,103.383,69.778z
                          " />
        </path>
    </g>
    <g id="upperTorso">
        <g id="leftArm">
            <g class="forearm">
                <path fill="#333" d="M9.068,131.177c-4.78,12.558-5.183,25.372-2.497,40.71c0,0,0.68,4.312,6.107,3.39
		c4.571-0.776,4.348-5.001,4.348-5.001c-2.351-13.388-2.234-24.244,1.89-35.134c0,0,1.75-4.725-2.833-6.6
		C11.02,126.471,9.068,131.177,9.068,131.177z" />
                <path fill="#f54f59" d="M9.604,166.5c-6.984,1.975-11.067,9.316-9.117,16.398c1.008,3.662,3.439,6.522,6.51,8.172
		c-0.167-0.363-0.315-0.742-0.426-1.141c-1.235-4.484,1.703-9.234,6.562-10.609c4.861-1.377,9.804,1.145,11.037,5.631
		c0.111,0.396,0.18,0.798,0.221,1.197c1.785-3.021,2.399-6.748,1.393-10.407C23.833,168.661,16.589,164.523,9.604,166.5z" />
            </g>
            <path fill="#333" d="M18.917,135.142c1.731-4.573,4.169-9.151,7.428-13.925c4.23-6.199,7.573-9.281,13.553-13.761
		c0,0,3.335-2.905,0.961-6.63c-2.797-4.389-7.415-1.908-7.415-1.908c-4.981,3.145-10.421,8.413-15.576,15.872
		c-3.827,5.537-6.726,10.938-8.8,16.387c0,0-1.877,4.187,2.599,6.24C16.75,139.75,18.917,135.142,18.917,135.142z" />
            <g>
                <ellipse stroke="#f54f59" cx="13.581" cy="132.93" rx="8.505" ry="8.623" />
                <g>
                    <path fill="#f54f59" d="M9.299,135.199c1.237,2.396,4.154,3.322,6.52,2.07c2.364-1.252,3.278-4.211,2.042-6.605
				c-1.236-2.398-4.152-3.324-6.52-2.072C8.978,129.844,8.065,132.803,9.299,135.199z" />
                </g>
            </g>
        </g>
        <g id="rightArm">
            <g class="forearm">
                <path fill="#333" d="M143.916,128.542c-4.583,1.875-2.833,6.6-2.833,6.6c4.124,10.89,4.241,21.746,1.89,35.134
		c0,0-0.223,4.225,4.348,5.001c5.428,0.922,6.107-3.39,6.107-3.39c2.688-15.338,2.283-28.152-2.496-40.71
		C150.932,131.177,148.98,126.471,143.916,128.542z" />
                <path fill="#f54f59" d="M134.216,175.741c-1.006,3.659-0.392,7.386,1.394,10.407c0.041-0.399,0.109-0.801,0.221-1.197
		c1.232-4.486,6.176-7.008,11.037-5.631c4.859,1.375,7.797,6.125,6.562,10.609c-0.111,0.398-0.26,0.777-0.427,1.141
		c3.071-1.649,5.502-4.51,6.511-8.172c1.949-7.082-2.134-14.424-9.117-16.398C143.411,164.523,136.167,168.661,134.216,175.741z" />
            </g>
            <path fill="#333" d="M148.333,137.417c4.476-2.054,2.599-6.24,2.599-6.24c-2.074-5.449-4.973-10.85-8.8-16.387
		c-5.155-7.459-10.595-12.727-15.576-15.872c0,0-4.618-2.48-7.415,1.908c-2.374,3.725,0.961,6.63,0.961,6.63
		c5.98,4.479,9.323,7.562,13.553,13.761c3.26,4.773,5.697,9.352,7.429,13.925C141.083,135.142,143.25,139.75,148.333,137.417z" />
            <g>
                <ellipse stroke="#f54f59" cx="146.419" cy="132.93" rx="8.505" ry="8.623" />
                <g>
                    <path fill="#f54f59" d="M148.659,128.592c-2.368-1.252-5.284-0.326-6.521,2.072c-1.236,2.395-0.322,5.354,2.043,6.605
				s5.282,0.326,6.52-2.07C151.936,132.803,151.021,129.844,148.659,128.592z" />
                </g>
            </g>
        </g>
        <path d="M42.356,94.049l-8.341-1.248c-5.238,10.201-7.014,20.918-4.697,32.248l8.34,1.248L42.356,94.049z" />
        <path d="M122.342,126.297l8.34-1.248c2.317-11.33,0.541-22.047-4.697-32.248l-8.34,1.248L122.342,126.297z" />
        <path fill="#f54f59" d="M125.283,131.334c0.048-13.081-1.633-26.163-5.186-39.244H80H39.903
		c-3.552,13.081-5.232,26.162-5.184,39.242L125.283,131.334z" />
    </g>
    <g id="lowerTrunk">
        <g id="leftFoot">
            <path fill="#333" d="M61.27,164.817c0-3.526-2.858-6.386-6.385-6.386c-3.527,0-6.386,2.859-6.386,6.386v0.001l0,0l0,36.132
			c0,3.526,2.859,6.386,6.386,6.386c3.526,0,6.385-2.859,6.385-6.386L61.27,164.817L61.27,164.817L61.27,164.817z" />
            <g class="lowerLeg">
                <path fill="#333" d="M61.27,200.63c0-3.526-2.858-6.386-6.385-6.386c-3.527,0-6.386,2.859-6.386,6.386v0.001l0,0l0,36.132
			c0,3.526,2.859,6.386,6.386,6.386c3.526,0,6.385-2.859,6.385-6.386L61.27,200.63L61.27,200.63L61.27,200.63z" />
                <path fill="#f54f59" d="M54.885,234.096c-9.526,0-17.244,7.119-17.244,15.903H72.13C72.13,241.215,64.41,234.096,54.885,234.096z" />
            </g>
            <g>
                <ellipse stroke="#f54f59" cx="54.885" cy="200.79" rx="9.294" ry="9.423" />
                <g>
                    <path fill="#f54f59" d="M60.607,203.823c-1.653,3.202-5.553,4.44-8.715,2.768c-3.163-1.677-4.383-5.628-2.73-8.832
					c1.651-3.204,5.556-4.442,8.715-2.771C61.036,196.664,62.258,200.62,60.607,203.823z" />
                </g>
            </g>
        </g>
        <g id="rightFoot">
            <path fill="#333" d="M98.73,164.817c0-3.526,2.858-6.386,6.385-6.386c3.527,0,6.386,2.859,6.386,6.386v0.001l0,0l0.001,36.132
			c0,3.526-2.859,6.386-6.387,6.386c-3.525,0-6.385-2.859-6.385-6.386V164.817L98.73,164.817L98.73,164.817z" />
            <g class="lowerLeg">
                <path fill="#333" d="M98.73,200.63c0-3.526,2.858-6.386,6.385-6.386c3.527,0,6.386,2.859,6.386,6.386v0.001l0,0l0.001,36.132
			c0,3.526-2.859,6.386-6.387,6.386c-3.525,0-6.385-2.859-6.385-6.386V200.63L98.73,200.63L98.73,200.63z" />
                <path fill="#f54f59" d="M87.87,249.999h34.489c0-8.784-7.719-15.903-17.244-15.903S87.87,241.215,87.87,249.999z" />
            </g>
            <g>
                <ellipse stroke="#f54f59" cx="105.115" cy="200.79" rx="9.294" ry="9.423" />
                <g>
                    <path fill="#f54f59" d="M102.123,194.988c3.159-1.672,7.064-0.434,8.715,2.771c1.653,3.204,0.434,7.155-2.73,8.832
					c-3.162,1.673-7.062,0.435-8.715-2.768C97.742,200.62,98.964,196.664,102.123,194.988z" />
                </g>
            </g>
        </g>
        <path fill="#f7727a" d="M34.719,131.334c0.048,13.082,1.824,26.164,5.184,39.246H80h40.098c3.361-13.08,5.138-26.162,5.186-39.244L34.719,131.334z" />
    </g>
</svg>
<div class="col">
    <h1>404</h1>
    <p>SOVOG has a configuration problem...</p>
</div>
<style>
    svg#robot {
        display: block;
        position: relative;
        margin: 6em auto 0 auto;
        padding: 10px;
        transform-origin: bottom;
        transform: rotate(0deg) translate3d(0px,0px,0px);
        animation: move 2s ease infinite;
    }

    @keyframes move {
        0%, 100% {
            transform: rotate(0deg) translate3d(0px,0px,0px);
        }
        25% {
            transform: rotate(5deg) translate3d(5px,5px,0px);
        }
        75% {
            transform: rotate(-6deg) translate3d(-5px,5px,0px);
        }
    }

    g#head {
        transform: rotate(0deg) translate3d(0px,0px,0px);
        transform-origin: bottom center;
        animation: head 1s 1s ease infinite;
        #leftAntenna, #rightAntenna {
            > ellipse {
                animation: color 350ms linear infinite;
            }
        }
    }

    @keyframes head {
        0%, 46%, 50%, 55%, 100% {
            transform: rotate(0deg) translate3d(0px,0px,0px);
        }
        49% {
            transform: rotate(20deg) translate3d(0px,0px,0px);
        }
        51% {
            transform: rotate(-10deg) translate3d(0px,0px,0px);
        }
    }

    @keyframes color {
        0%, 100% {
            fill: #ccc;
        }
        50% {
            fill: rgb(255,0,0);
        }
    }

    #upperTorso {
        transform: rotate(0deg);
        transform-origin: center;
        animation: torso 600ms 1s ease infinite;
        #leftArm {
            transform: rotate(0deg);
            transform-origin: 80% 5%;
            animation: left 1s ease infinite;
            .forearm {
                transform: rotate(0deg);
                transform-origin: 68% 10%;
                animation: forearm 800ms 1s ease infinite;
            }
        }
        #rightArm {
            transform: rotate(0deg);
            transform-origin: 18% 0%;
            animation: right 1s ease infinite;
            .forearm {
                transform: rotate(0deg);
                transform-origin: 68% 10%;
                animation: forearm 700ms 1s ease infinite;
            }
        }
    }

    @keyframes torso {
        40%, 50%, 60% {
            transform: rotate(0deg);
        }
        45% {
            transform: rotate(5deg);
        }
        55% {
            transform: rotate(-5deg);
        }
    }

    @keyframes left {
        60% {
            transform: rotate(100deg);
        }
    }

    @keyframes right {
        50% {
            transform: rotate(-70deg);
        }
    }

    @keyframes forearm {
        0%, 30%, 50%, 70%, 100% {
            transform: rotate(0deg);
        }
        40% {
            transform: rotate(25deg);
        }
        60% {
            transform: rotate(-25deg);
        }
    }

    #lowerTrunk {
        transform: rotate(0deg);
        transform-origin: 60% 5%;
        animation: lowerTrunk 1s ease infinite;
    }

    @keyframes lowerTrunk {
        0%, 100% {
            transform: rotate(0deg) translate3d(0px,0px,0px);
        }
        25% {
            transform: rotate(2deg) translate3d(2px,0px,0px);
        }
        75% {
            transform: rotate(-2deg) translate3d(-2px,0px,0px);
        }
    }

    #leftFoot {
        transform: rotate(0deg);
        transform-origin: 60% 5%;
        animation: leftFoot 2s ease infinite;
        > .lowerLeg {
            transform: rotate(0deg);
            transform-origin: 68% 10%;
            animation: lowerLeg 2s ease infinite;
        }
    }

    @keyframes leftFoot {
        0%, 50% {
            transform: rotate(0deg);
        }
        25% {
            transform: rotate(70deg);
        }
    }

    @keyframes lowerLeg {
        0%, 50% {
            transform: rotate(0deg);
        }
        8%, 14% {
            transform: rotate(-15deg);
        }
        11%, 18% {
            transform: rotate(15deg);
        }
    }

    #rightFoot {
        transform: rotate(0deg);
        transform-origin: 50% 5%;
        animation: rightFoot 2s ease infinite;
        > .lowerLeg {
            transform: rotate(0deg);
            transform-origin: 68% 10%;
            animation: lowerLegIz 2s ease infinite;
        }
    }

    @keyframes rightFoot {
        75% {
            transform: rotate(-60deg);
        }
        50%, 100% {
            transform: rotate(0deg);
        }
    }

    @keyframes lowerLegIz {
        50%, 100% {
            transform: rotate(0deg);
        }
        60%, 70% {
            transform: rotate(15deg);
        }
        65%, 85% {
            transform: rotate(-15deg);
        }
    }

    html{
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        text-rendering: optimizelegibility;
    }

    html,
    body {
        width: 100%;
        height: 100%;
        overflow: hidden;
    }

    body {
        background: #404853;
        font-family: 'Roboto Condensed', sans-serif;
    }

    .col {
        text-align: center;
        h1 {
            text-shadow: 0 3px 0px #404853, 0 6px 0px #333;
            color: #f54f59;
            font-size: 6em;
            font-weight: 700;
            line-height: 0.6em;
        }
        p {
            color: #ccc;
            font-family: monospace;
            font-size: 0.95em;
            font-weight: 300;
            line-height: 7em;
        }
    }
</style>
