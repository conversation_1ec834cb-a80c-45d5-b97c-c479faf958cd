#define layout()
<!DOCTYPE html>
<html lang="zh-CN" xml:lang="zh-CN" xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="content-type" content="text/html; charset=utf-8">
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">

    <meta name="keywords" content="#(seoKeywords ?? '厦门澳林匹亚石材有限公司,厦门澳林匹亚管理系统')"/>
    <meta name="description" content="#(seoDescr ?? '厦门澳林匹亚石材有限公司')"/>
    <meta http-equiv="Cache-Control" content="no-transform"/>
    <meta http-equiv="Cache-Control" content="no-siteapp"/>
    <meta name="viewport"
          content="width=device-width,initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no"/>
    <title>#(seoTitle ?? "澳林匹亚管理系统")</title>

    <link rel="icon" type="image/x-icon" href="/assets/img/favicon.ico">
    <link rel="stylesheet" type="text/css" href="/assets/font-awesome/css/font-awesome.min.css">
    <link rel="stylesheet" type="text/css" href="/assets/css/os-my-space-v1.0.css?v=1.2.1">
    <link rel="stylesheet" type="text/css" href="/assets/iconfont/iconfont.css">
    <link rel="stylesheet" type="text/css" href="/assets/magic_input/magic-input.css">
    <link rel="stylesheet" type="text/css" href="/assets/layui/css/layui.css" media="all">
    <link rel="stylesheet" type="text/css" href="/assets/layui/css/layui_patch.css?v=1.2.1" media="all">
    <script type="text/javascript" src="/assets/jquery/jquery.min-v2.1.3.js"></script>
    <script type="text/javascript" src="/assets/jquery_form/jquery.form.min.js"></script>
    <script>
        var _hmt = _hmt || [];
        (function () {
            var hm = document.createElement("script");
            hm.src = "https://hm.baidu.com/hm.js?ed8a3ad0719764b13239fdbc03bb23a7";
            var s = document.getElementsByTagName("script")[0];
            s.parentNode.insertBefore(hm, s);
        })();
    </script>
    <style>
    /* 防止横向滚动和溢出 */
    html, body {
        width: 100%;
        max-width: 100%;
        overflow-x: hidden;
    }
    
    /* 确保内容不超出屏幕 */
    .layui-card, .layui-tab, .layui-form, .layui-form-item,
    .layui-input-block, .layui-input-inline, .layui-textarea,
    .layui-table-view, .jf-breadcrumb-box, fieldset {
        max-width: 100%;
        box-sizing: border-box;
    }
    
    /* 响应式表格样式 - 适用于所有设备上的宽表格 */
    .table-responsive {
        width: 100%;
        margin-bottom: 15px;
        overflow-y: hidden;
        -ms-overflow-style: -ms-autohiding-scrollbar;
        border: none;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        max-width: 100%;
        position: relative;
    }
    
    /* 让表格内容保持完整，不会被截断 */
    .table-responsive .layui-table {
        width: auto;
        max-width: none;
        margin-bottom: 0;
        white-space: nowrap;
        table-layout: auto;
    }
    
    /* 固定表头样式 */
    .table-responsive .layui-table thead th {
        position: sticky;
        top: 0;
        background-color: #f2f2f2;
        z-index: 10;
        box-shadow: 0 1px 0 #e6e6e6;
    }
    
    /* 表格滚动提示指示器 */
    .table-scroll-indicator {
        position: relative;
        height: 2px;
        background-color: #e2e2e2;
        margin: 3px 0 6px;
        display: none;
    }
    
    .table-scroll-indicator::after {
        content: '';
        position: absolute;
        height: 100%;
        width: 30%;
        background-color: #009688;
        left: 0;
        border-radius: 1px;
        animation: table-scroll-hint 1.5s ease-in-out infinite;
    }
    
    @keyframes table-scroll-hint {
        0% { left: 0; }
        50% { left: 70%; }
        100% { left: 0; }
    }
    
    /* 滑动提示样式 */
    .swipe-hint {
        text-align: center;
        font-size: 13px;
        color: #666;
        padding: 5px;
        background-color: #f9f9f9;
        border-radius: 4px;
        margin: 5px 0;
        border: 1px dashed #ddd;
    }
    
    .swipe-hint span {
        display: inline-block;
        animation: swipe-animation 1.5s infinite;
        font-weight: bold;
    }
    
    @keyframes swipe-animation {
        0% { transform: translateX(-5px); }
        50% { transform: translateX(5px); }
        100% { transform: translateX(-5px); }
    }
    
    /* 移动设备适配 */
    @media screen and (max-width: 768px) {
        .layui-card-header h3 {
            font-size: 14px;
            line-height: 1.4;
            word-break: break-word;
            white-space: normal;
        }
        
        .layui-form-item .layui-form-label {
            text-overflow: ellipsis;
            overflow: hidden;
        }
        
        /* 确保表单控件适应容器 */
        .layui-input, .layui-textarea, .layui-select {
            width: 100% !important;
        }
        
        /* 修复移动导航 */
        .layui-nav {
            overflow-x: hidden;
        }
    }
    
    /* 多列菜单的基础样式 */
    .layui-nav .layui-nav-item .multi-column-nav {
        display: none !important;
        position: absolute !important;
        background-color: #fff;
        box-shadow: 0 2px 4px rgba(0,0,0,0.12);
        width: auto;
        z-index: 1000;
        left: auto !important;
        padding: 5px 0;
        margin-top: -2px;  /* 减少与父菜单的间隙 */
    }
    
    /* 后4个菜单向左展开 */
    .layui-nav .layui-nav-item.right-expand .multi-column-nav {
        right: 0 !important;
        left: auto !important;
    }

    /* 为父菜单项添加一个小的底部padding，作为鼠标移动的缓冲区 */
    .layui-nav .layui-nav-item > a {
        padding-bottom: 20px;
        margin-bottom: -18px;
    }

    /* 菜单列样式 */
    .layui-nav .multi-column-nav .menu-column {
        min-width: 150px;
        max-width: 200px;
        padding: 5px 0;
        border-right: 1px solid #eee;
        display: inline-block;
        vertical-align: top;
    }
    
    /* 最后一列不需要右边框 */
    .layui-nav .multi-column-nav .menu-column:last-child {
        border-right: none;
    }

    /* 菜单项样式 */
    .layui-nav .multi-column-nav dd a {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        padding: 0 20px;
        color: #333 !important;
    }
    
    /* 当只有一列时的特殊样式 */
    .layui-nav .multi-column-nav.single-column {
        min-width: 150px;
    }

    /* 鼠标悬停时显示菜单 */
    .layui-nav .layui-nav-item:hover > .multi-column-nav {
        display: flex !important;
    }

    /* 子菜单hover效果 */
    .layui-nav .multi-column-nav dd a:hover {
        background-color: #f2f2f2;
    }

    /* 清除浮动 */
    .layui-nav .multi-column-nav:after {
        content: '';
        display: table;
        clear: both;
    }
    /* 通知弹框样式 */
    .notification-box {
        position: fixed;
        bottom: 20px;
        right: 20px;
        width: 300px;
        background-color: #fff;
        border: 1px solid #ddd;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        z-index: 1000;
        display: none;
        flex-direction: column;
    }
    .notification-box-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px;
        background-color: #f5f5f5;
        border-bottom: 1px solid #ddd;
    }
    .notification-box-content {
        padding: 10px;
        font-size: 14px;
    }
    .text-ellipsis {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        cursor: pointer;
    }
    .notice-detail-content {
        max-height: 400px;
        overflow-y: auto;
        white-space: pre-wrap;
        word-break: break-word;
    }
    .notification-box-footer {
        padding: 10px;
        text-align: right;
        border-top: 1px solid #ddd;
    }
    .notification-icon {
        position: fixed;
        bottom: 20px;
        right: 20px;
        width: 40px;
        height: 40px;
        color: #fff;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        font-size: 14px;
        font-weight: bold;
        transition: background-color 0.3s;
    }
    .notification-icon.unread {
        background-color: #ff4d4f;
        animation: blink 0.8s ease-in-out infinite;
    }
    .notification-icon.read {
        background-color: #52c41a;
    }
    @keyframes blink {
        0%, 100% { opacity: 1; transform: scale(1); }
        50% { opacity: 0.7; transform: scale(1.1); }
    }
    /* 新增：早晚报填写提示样式 */
    .daily-report-reminder {
        position: fixed;
        bottom: 20px;
        left: 20px;
        background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
        color: white;
        padding: 15px 25px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        cursor: move;
        z-index: 9999;
        display: flex;
        align-items: center;
        gap: 10px;
        font-size: 14px;
        user-select: none;
        border: 2px dashed rgba(255, 255, 255, 0.3);
        opacity: 0;
        visibility: hidden;
    }

    .daily-report-reminder.show {
        opacity: 1;
        visibility: visible;
        transition: opacity 0.3s ease;
    }

    .daily-report-reminder:hover {
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
        border-color: rgba(255, 255, 255, 0.6);
    }

    .daily-report-reminder i {
        font-size: 18px;
    }

    .daily-report-reminder::before {
        content: '⋮⋮';
        position: absolute;
        left: 5px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 16px;
        opacity: 0.7;
    }

    .daily-report-reminder .reminder-content {
        margin-left: 20px;
        cursor: pointer;
        position: relative;
        z-index: 1;
    }

    /* 图标状态样式 */
    .daily-report-reminder.icon-mode {
        padding: 10px;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
    }

    .daily-report-reminder.icon-mode .reminder-content {
        display: none;
    }

    .daily-report-reminder.icon-mode::before {
        content: '\e6b2';
        font-family: layui-icon;
        font-size: 20px;
        opacity: 1;
        position: static;
        transform: none;
    }
    #@css?()
</style>
    #@css?()
</head>

<body>
<ul class="layui-nav layui-show-md-block layui-show-lg-block layui-hide-xs layui-hide-sm"
    style="z-index:999;position:relative;">
    <li class="layui-nav-item"><a href="/">厦门澳林匹亚石材</a></li>
    #for(first:firstLevel)
    #set(firstArray = first.split(";"))
    <li class="layui-nav-item #if(for.index >= (firstLevel.size()/2))right-expand#end">
        <a href="javascript:void(0);">#(firstArray[0]??)</a>
        #set(menuItems = secondLevel.get(first))
        <dl class="layui-nav-child multi-column-nav #if(menuItems.size() <= 10)single-column#end">
            #for(second:menuItems)
            #if(for.index%10==0)
            <div class="menu-column">
            #end
                #set(secondArray = second.split(";"))
                <dd><a href="#(secondArray[1]??)">#(secondArray[0]??)</a></dd>
            #if(for.index!=0 && (for.index%10==9 || for.index==for.last))
            </div>
            #end
            #end
        </dl>
    </li>
    #end
    <li class="layui-nav-item">
        <a href="javascript:void(0);"><img src="/upload/avatar/#(loginAccount.avatar??)" class="layui-nav-img">我</a>
        <dl class="layui-nav-child">
            <dd><a href="/my">动态消息</a></dd>
            <dd><a href="/my/share">我的分享</a></dd>
            <dd><a href="/my/feedback">我的反馈</a></dd>
            <dd><a href="/my/project">我的项目</a></dd>
            <dd><a href="/my/message">我的私信</a></dd>
            <dd><a href="/my/favorite">我的收藏</a></dd>
            <dd><a href="/my/tip">我的提醒</a></dd>
            <dd><a href="/login/forget_password.html">忘记密码</a></dd>
            <dd><a href="retrieve_password.html">更改密码</a></dd>
            <!-- autoMyMenu -->
            <dd><a href="javascript:void(0);" onclick="logout();">退出</a></dd>
        </dl>
    </li>
</ul>

<ul class="layui-nav layui-show-xs-block layui-show-sm-block layui-hide-md layui-hide-lg" style="z-index:999;position:relative;">
    <li class="layui-nav-item"><a href="/" style="display: inline-block; width: 1ch; overflow: hidden; white-space: nowrap;"><i class="layui-icon layui-icon-home"></i></a></li>
    #for(first:firstLevel)
    #set(firstArray = first.split(";"))
    <li class="layui-nav-item">
        <a href="javascript:void(0);" onclick="openModuleModal('#(firstArray[0]??)')" style="display: inline-block; width: 1ch; overflow: hidden; white-space: nowrap;">#(firstArray[0]??)</a>
    </li>
    #end
    <li class="layui-nav-item">
        <a href="javascript:void(0);" onclick="openMyModuleModal()" style="display: inline-block; width: 5ch; overflow: hidden; white-space: nowrap;"><img src="#if(loginAccount.avatar??) /upload/avatar/#(loginAccount.avatar??) #else /assets/img/touxiang.png #end" class="layui-nav-img">我</a>
    </li>
</ul>

<!-- 中部主体容器 -->
#@main()

<div class="clearfix"></div>
<!-- 底部容器 -->
<div class="jf-footer-box">
    <ul>
        <li><a href="/share/1" target="_blank">关于澳林匹亚</a></li>
        <li><a href="javascript:void(0);">友情链接</a></li>
        <li><a href="http://www.miibeian.gov.cn" rel="external nofollow" target="_blank">闽ICP备**********号</a></li>
    </ul>
</div>

<script type="application/javascript" src="/assets/layui/layui.js"></script>
<script type="application/javascript" src="/assets/layui/xm-select.js"></script>
<script type="text/javascript" src="/assets/anime/anime.min.js"></script>
<script type="text/javascript" src="/assets/math/math.min.js"></script>
<script type="text/javascript" src="/assets/js/os-v1.0.js?t=**************"></script>
<script type="text/javascript" src="/assets/js/qrcode.min.js"></script>
<script type="text/javascript" src="/assets/js/axios.min.js"></script>
### 模板函数安全调用，找到 js() 模板函数则调用，找不到则直接跳过
### 适合于部分页面在 layout.html 提供的 js 文件之外，还需额外添加 js 资源的应用场景
<script type="text/javascript">
    // 页面加载完成后检查通知
    $(document).ready(function () {
        // checkNeedNotice();
        checkNoticeList();
        checkNeedWriteDaily();
        // 每5分钟检查一次
        setInterval(checkNeedWriteDaily, 5 * 60 * 1000);
        
        // 处理所有宽表格，自动添加水平滚动支持
        makeAllTablesResponsive();
        
        // 窗口大小变化时重新检查表格
        $(window).resize(function() {
            makeAllTablesResponsive();
        });
    });
    
    // 处理所有表格，使其自动支持水平滚动
    function makeAllTablesResponsive() {
        // 处理所有表格，不仅限于移动设备
        $('table.layui-table').each(function() {
            const view = $(this).closest('.fixTable');
            if(view){
                const originId = view.prevObject.attr('id');
                if (originId && (originId.indexOf('fixTable') > -1)) {
                    return; // 使用 return 替代 continue，确保逻辑正确
                }
            }

            // 避免重复包装
            if (!$(this).parent().hasClass('table-responsive') && 
                !$(this).parent().hasClass('layui-table-view')) {
                
                // 检查表格是否宽于其容器
                var tableWidth = $(this)[0].scrollWidth;
                var containerWidth = $(this).parent().width();
                
                // 只为宽表格添加滚动支持
                if (tableWidth > containerWidth) {
                    // 包装表格
                    $(this).wrap('<div class="table-responsive"></div>');
                    
                    var $tableWrapper = $(this).parent();
                    var $table = $(this);
                    
                    // 添加滑动指示器
                    $tableWrapper.prepend('<div class="table-scroll-indicator"></div>');
                    
                    // 添加滑动提示
                    var $gestureHint = $('<div class="swipe-hint">左右滑动查看更多内容 <span>↔</span></div>');
                    $tableWrapper.after($gestureHint);
                    
                    var $indicator = $tableWrapper.find('.table-scroll-indicator');
                    $indicator.show();
                    
                    // 几秒后自动隐藏提示
                    setTimeout(function() {
                        $gestureHint.fadeOut(800);
                    }, 4000);
                    
                    // 监听滚动事件
                    $tableWrapper.on('scroll', function() {
                        // 滚动时隐藏提示
                        $gestureHint.fadeOut(300);
                        
                        var maxScroll = $table.width() - $tableWrapper.width();
                        var currentScroll = $tableWrapper.scrollLeft();
                        
                        // 滚动到最右侧时隐藏指示器
                        if (currentScroll >= maxScroll - 5) {
                            $indicator.fadeOut(300);
                        } else if ($indicator.is(':hidden') && currentScroll < maxScroll - 50) {
                            $indicator.fadeIn(300);
                        }
                    });
                }
            }
        });
    }

    // 检查是否有需要显示的通知
    function checkNeedNotice() {
        $.get('/my/c/needNotice', function (data) {
            if (data.notice) {
                showNotificationBox(data.notice);
                markNoticeAsShowed(data.notice.id);
            }
        });
    }

    // 显示通知弹框
    function showNotificationBox(notice) {
        const notificationBox = `
            <div class="notification-box" id="notificationBox">
                <div class="notification-box-header">
                    <span>通知</span>
                    <i class="layui-icon layui-icon-close" onclick="closeNotificationBox()"></i>
                </div>
                <div class="notification-box-content" id="notificationContent">
                    <div class="notification-text" style="cursor: pointer;" onclick="markNoticeAsRead(${notice.id})">${notice.content}</div>
                </div>
                <div class="notification-box-footer">
                    <button class="layui-btn layui-btn-sm ${notice.status > 0 ? 'layui-btn-disabled' : ''}"
                            onclick="${notice.status > 0 ? '' : `markNoticeAsRead(${notice.id})`}"
                            style="${notice.status > 0 ? 'background-color: #ccc; border-color: #ccc; color: #fff; cursor: not-allowed;' : ''}"
                            ${notice.status > 0 ? 'disabled' : ''}>
                        已读
                    </button>
                </div>
            </div>`;
        $('body').append(notificationBox);
        $('#notificationBox').fadeIn();
        
        // 添加事件委托，处理链接点击
        $('#notificationContent').on('click', 'a', function(e) {
            e.stopPropagation(); // 阻止事件冒泡
        });
    }

    // 关闭通知弹框
    function closeNotificationBox() {
        $('#notificationBox').fadeOut();
    }

    // 标记通知为已显示
    function markNoticeAsShowed(id) {
        $.post('/my/c/markNoticeAsShowed', { id }, function () {});
    }

    // 标记通知为已读
    function markNoticeAsRead(id) {
        $.post('/my/c/markNoticeAsRead', { id }, function () {
            // 关闭通知弹框
            closeNotificationBox();
            
            // 移除通知列表中对应行的字体加粗样式
            $(`.notice-item[data-id="${id}"]`).css('font-weight', 'normal');
            
            // 将已读按钮置为灰色并禁用
            $(`.notice-item[data-id="${id}"] button`).css({
                'background-color': '#ccc',
                'border-color': '#ccc',
                'color': '#fff',
                'cursor': 'not-allowed'
            }).prop('disabled', true);
            
            // 更新右下角未读数量
            updateUnreadCount();
        });
    }

    // 更新未读通知数量
    function updateUnreadCount() {
        $.get('/my/c/noticeList', function(data) {
            const unreadCount = data.noticeList.filter(notice => notice.status === 0).length;
            const notificationIcon = $('#notificationIcon');
            
            if (unreadCount > 0) {
                // 如果还有未读通知
                notificationIcon.text(unreadCount).removeClass('read').addClass('unread');
            } else {
                // 如果没有未读通知
                notificationIcon.text('0').removeClass('unread').addClass('read');
            }
        });
    }

    // 检查通知列表
    function checkNoticeList() {
        $.get('/my/c/noticeList', function (data) {
            if (data.noticeList && data.noticeList.length > 0) {
                showNotificationIcon();
            }
        });
    }

    // 显示通知图标
    function showNotificationIcon() {
        $.get('/my/c/noticeList', function(data) {
            const unreadCount = data.noticeList.filter(notice => notice.status === 0).length;
            const iconClass = unreadCount > 0 ? 'notification-icon unread' : 'notification-icon read';
            const icon = `<div class="${iconClass}" id="notificationIcon" onclick="showNoticeList()">${unreadCount || 0}</div>`;
            $('body').append(icon);
        });
    }

    // 显示通知详情
    function showNoticeDetail(content) {
        layer.open({
            type: 1,
            title: '通知详情',
            area: ['600px', 'auto'],
            content: `<div class="notice-detail-content" style="padding: 20px;">${content}</div>`
        });
    }

    // 展示通知列表
    function showNoticeList() {
        layer.open({
            type: 1,
            title: '通知列表',
            area: ['600px', '500px'],
            content: '<div style="padding: 10px;"><button class="layui-btn layui-btn-sm" onclick="markAllNoticeAsRead()">全部已读</button><button class="layui-btn layui-btn-sm layui-btn-normal" onclick="openSendNoticeModal()" style="margin-left: 10px;">发送通知</button></div><div id="noticeListContainer"></div><div id="noticeListPagination" style="text-align: center; margin-top: 20px;"></div>',
            success: function () {
                loadNoticeList();
            }
        });
    }

    // 标记所有通知为已读
    function markAllNoticeAsRead() {
        $.post('/my/c/allNoticeSetRead', function () {
            loadNoticeList();
        });
    }

    // 打开发送通知弹窗
    function openSendNoticeModal() {
        layer.open({
            type: 1,
            title: '发送通知',
            area: ['650px', '580px'],
            content: `
                <style>
                    .notice-form {
                        padding: 25px;
                        background: #fff;
                    }
                    .notice-form .layui-form-item {
                        margin-bottom: 18px;
                        display: flex;
                        align-items: flex-start;
                    }
                    .notice-form .layui-form-label {
                        width: 80px;
                        min-width: 80px;
                        text-align: right;
                        padding: 9px 12px 9px 0;
                        height: 38px;
                        line-height: 20px;
                        color: #333;
                        font-weight: 500;
                        flex-shrink: 0;
                        margin-right: 15px;
                    }
                    .notice-form .layui-input-block {
                        margin-left: 0;
                        flex: 1;
                    }
                    .notice-form .layui-input,
                    .notice-form .layui-textarea {
                        width: 100%;
                        height: 38px;
                        line-height: 38px;
                        padding: 0 12px;
                        border: 1px solid #e6e6e6;
                        border-radius: 2px;
                        transition: border-color 0.3s;
                    }
                    .notice-form .layui-input:focus,
                    .notice-form .layui-textarea:focus {
                        border-color: #1E9FFF;
                        outline: none;
                    }
                    .notice-form .layui-textarea {
                        height: 100px;
                        line-height: 1.5;
                        padding: 8px 12px;
                        resize: vertical;
                        min-height: 80px;
                    }
                    .notice-form .layui-form-text {
                        align-items: flex-start;
                    }
                    .notice-form .layui-form-text .layui-form-label {
                        padding-top: 8px;
                    }
                    .notice-form .form-buttons {
                        text-align: center;
                        margin-top: 25px;
                        padding-top: 20px;
                        border-top: 1px solid #e6e6e6;
                    }
                    .notice-form .form-buttons .layui-btn {
                        margin: 0 10px;
                        min-width: 100px;
                        height: 36px;
                        line-height: 36px;
                    }
                    /* 用户选择器样式调整 */
                    .notice-form #userSelect .xm-select {
                        height: 38px !important;
                        line-height: 36px !important;
                    }
                </style>
                <form class="layui-form notice-form">

                    <div class="layui-form-item layui-form-text">
                        <label class="layui-form-label">通知内容</label>
                        <div class="layui-input-block">
                            <textarea id="noticeContent" placeholder="请输入通知内容，支持HTML格式" class="layui-textarea" lay-verify="required"></textarea>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">通知对象</label>
                        <div class="layui-input-block">
                            <div id="userSelect"></div>
                        </div>
                    </div>

                    <div class="form-buttons">
                        <button type="button" class="layui-btn" onclick="sendNotice()">发送通知</button>
                        <button type="button" class="layui-btn layui-btn-primary" onclick="layer.closeAll()">取消</button>
                    </div>
                </form>
            `,
            success: function() {
                // 初始化用户选择器
                initUserSelector();
            }
        });
    }

    // 初始化用户选择器
    function initUserSelector() {
        // 获取用户列表
        $.get('/my/c/getEmployeeList', function(data) {
            if (data.state === 'ok') {
                const users = data.data.map(user => ({
                    name: user.xm + (user.bq ? ` (${user.bq})` : '澳林'),
                    value: user.id,
                    selected: false
                }));

                // 添加"所有用户"选项
                users.unshift({
                    name: '所有用户',
                    value: 'all',
                    selected: false
                });

                // 初始化xm-select
                window.userSelectInstance = xmSelect.render({
                    el: '#userSelect',
                    name: 'users',
                    placeholder: '请选择要通知的用户',
                    searchTips: '请输入用户姓名进行搜索',
                    theme: {
                        color: '#1E9FFF',
                    },
                    model: {
                        label: {
                            type: 'text'
                        }
                    },
                    height: '200px',
                    data: users,
                    toolbar: {
                        show: true,
                        list: ['ALL', 'CLEAR', 'REVERSE']
                    },
                    filterable: true,
                    on: function(data) {
                        // 处理"所有用户"选项
                        const allOption = data.arr.find(item => item.value === 'all');
                        if (allOption) {
                            // 如果选择了"所有用户"，则选择所有其他用户
                            const allUsers = users.filter(user => user.value !== 'all');
                            window.userSelectInstance.setValue(allUsers);
                        }
                    }
                });
            }
        });
    }

    // 发送通知
    function sendNotice() {
        const content = $('#noticeContent').val().trim();

        if (!content) {
            layer.msg('请输入通知内容', {icon: 2});
            return;
        }

        const selectedUsers = window.userSelectInstance ? window.userSelectInstance.getValue() : [];
        if (selectedUsers.length === 0) {
            layer.msg('请选择要通知的用户', {icon: 2});
            return;
        }

        const userIds = selectedUsers.map(user => user.value).filter(id => id !== 'all');

        // 显示加载提示
        const loadingIndex = layer.load(2, {shade: [0.3, '#000']});

        $.ajax({
            url: '/my/c/sendNotice',
            type: 'POST',
            data: {
                content: content,
                userIds: userIds.join(',')
            },
            success: function(ret) {
                layer.close(loadingIndex);
                if (ret.state === 'ok') {
                    layer.msg('通知发送成功！', {icon: 1});
                    layer.closeAll();
                    // 刷新通知列表
                    if (typeof loadNoticeList === 'function') {
                        loadNoticeList();
                    }
                } else {
                    layer.msg(ret.msg || '发送失败', {icon: 2});
                }
            },
            error: function() {
                layer.close(loadingIndex);
                layer.msg('网络错误，发送失败', {icon: 2});
            }
        });
    }

    // 加载通知列表
    function loadNoticeList(page = 1) {
        $.get('/my/c/noticeList', { page }, function (data) {
            let listHtml = '';
            data.noticeList.forEach(item => {
                const content = item.content;
                listHtml += `
                    <div style="padding: 10px; ${item.status === 0 ? 'font-weight: bold;' : ''}; display: flex; justify-content: space-between; align-items: center;" class="notice-item" data-id="${item.id}">
                        <div class="notice-content" style="cursor: pointer;" onclick="markNoticeAsRead(${item.id})">${content}</div>
                        <button class="layui-btn layui-btn-xs ${item.status > 0 ? 'layui-btn-disabled' : ''}"
                                onclick="${item.status > 0 ? '' : `markNoticeAsRead(${item.id})`}"
                                style="${item.status > 0 ? 'background-color: #ccc; border-color: #ccc; color: #fff; cursor: not-allowed;' : ''}"
                                ${item.status > 0 ? 'disabled' : ''}>
                            已读
                        </button>
                    </div>`;
            });
            $('#noticeListContainer').html(listHtml);

            // 渲染分页
            layui.laypage.render({
                elem: 'noticeListPagination',
                count: data.total || data.totalCount || (data.noticeList ? data.noticeList.length : 0),
                limit: data.pageSize || 10,
                curr: page,
                layout: ['count', 'prev', 'page', 'next'],
                jump: function(obj, first) {
                    if(!first) {
                        loadNoticeList(obj.curr);
                    }
                }
            });
        });
    }

    let secondLevelMap = JSON.parse('#(secondLevelMap??)');
    // console.log(secondLevelMap);
    var modal_menu_links = `
          .modal-menu-links {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-around;
            align-items: center;
            padding: 20px;
            position: relative;
          }
          .link-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: #333;
            margin: 10px;
            width: 100px;
            z-index: 1;
          }
          .link-item i {
            font-size: 24px;
            margin-bottom: 5px;
          }
          .link-item span {
            text-align: center;
            font-size: 12px;
          }
          .modal-menu-links::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 5%;
            right: 5%;
            height: 1px;
            background-color: #ddd;
            z-index: 0;
          }
          .modal-menu-links::after {
            content: '';
            position: absolute;
            left: 50%;
            top: 5%;
            bottom: 5%;
            width: 1px;
            background-color: #ddd;
            z-index: 0;
          }
        `;

    function openModuleModal(title) {
        const menuList = secondLevelMap[title] || [];
        // console.log(menuList);
        const menuLinks = menuList.map(menu => {
            const menuArray = menu.split(';');
            return `
                    <a href="${menuArray[1] || ''}" class="link-item">
                        <i class="${menuArray[2] || ''}"></i>
                        <span>${menuArray[0] || ''}</span>
                    </a>
                `;
        }).join('');

        layer.open({
            type: 1,
            title: title,
            shadeClose: true,
            shade: 0.8,
            area: ['90%', '90%'],
            content: `
                    <div class="modal-menu-links">
                        ${menuLinks}
                    </div>
                `,
            success: function (layero, index) {
                var style = document.createElement('style');
                style.textContent = modal_menu_links;
                layero.find('.layui-layer-content').append(style);
            }
        });
    }


    function openMyModuleModal() {
        layer.open({
            type: 1,
            title: '我的',
            shadeClose: true,
            shade: 0.8,
            area: ['90%', '90%'],
            content: `
                    <div class="modal-menu-links">
                        <a class="link-item" href="/my"><span>动态消息</span></a>
                        <a class="link-item" href="/my/share"><span>我的分享</span></a>
                        <a class="link-item" href="/my/feedback"><span>我的反馈</span></a>
                        <a class="link-item" href="/my/project"><span>我的项目</span></a>
                        <a class="link-item" href="/my/message"><span>我的私信</span></a>
                        <a class="link-item" href="/my/favorite"><span>我的收藏</span></a>
                        <a class="link-item" href="/my/tip"><span>我的提醒</span></a>
                        <a class="link-item" href="forget_password.html"><span>忘记密码</span></a>
                        <a class="link-item" href="retrieve_password.html"><span>更改密码</span></a>
                        <a class="link-item" href="javascript:void(0);" onclick="logout();"><span>退出</span></a>
                    </div>
                `,
            success: function (layero, index) {
                var style = document.createElement('style');
                style.textContent = modal_menu_links;
                layero.find('.layui-layer-content').append(style);
            }
        });
    }
    layui.use(['element'], function(){
        var element = layui.element;
        // 重新初始化 element 以应用新的样式
        element.render('nav');
    });

    // 检查是否需要填写日报
    function checkNeedWriteDaily() {
        // 检查当前页面是否是 rbNew 页面
        if (window.location.pathname.includes('/my/rb/rbNew')) {
            return;
        }

        $.get('/my/c/needWriteDaily', function(data) {
            if (data.need) {
                // 如果还没有显示提示，则添加提示元素
                if ($('.daily-report-reminder').length === 0) {
                    const reminder = $(`
                        <div class="daily-report-reminder">
                            <div class="reminder-content">
                                <i class="layui-icon layui-icon-form"></i>
                                <span>${data.today} 还没有提交早晚报哦，点击这里填写并提交, 谢谢</span>
                            </div>
                        </div>
                    `);
                
                    $('body').append(reminder);
                
                    // 添加点击事件
                    reminder.find('.reminder-content').on('click', function() {
                        window.open('/my/rb/rbNew?id='+data.id+"&today="+data.today, '_blank');
                    });

                    // 添加图标点击事件
                    reminder.on('click', function(e) {
                        if ($(this).hasClass('icon-mode')) {
                            e.stopPropagation();
                            $(this).removeClass('icon-mode');
                        }
                    });
                
                    // 延迟一帧后显示，避免闪烁
                    requestAnimationFrame(function() {
                        reminder.addClass('show');
                    });
                
                    // 完全重写的拖动功能，使用绝对定位
                    let isDragging = false;
                    let startX, startY;
                    
                    // 获取可视区域尺寸
                    function getViewportSize() {
                        return {
                            width: window.innerWidth || document.documentElement.clientWidth,
                            height: window.innerHeight || document.documentElement.clientHeight
                        };
                    }
                    
                    // 确保提示框完全在可视范围内
                    function ensureInViewport() {
                        const viewport = getViewportSize();
                        const left = parseInt(reminder.css('left')) || 0;
                        const bottom = parseInt(reminder.css('bottom')) || 0;
                        const width = reminder.outerWidth();
                        const height = reminder.outerHeight();
                        
                        // 计算有效范围
                        let newLeft = Math.max(10, Math.min(left, viewport.width - width - 10));
                        let newBottom = Math.max(10, Math.min(bottom, viewport.height - height - 10));
                        
                        // 应用位置
                        reminder.css({
                            'left': newLeft + 'px',
                            'bottom': newBottom + 'px'
                        });
                    }
                    
                    // 初始化确保在视口内
                    ensureInViewport();
                    
                    // 处理鼠标事件开始拖动
                    reminder.on('mousedown', function(e) {
                        if ($(e.target).closest('.reminder-content').length > 0) {
                            return;
                        }
                        
                        isDragging = true;
                        const rect = reminder[0].getBoundingClientRect();
                        startX = e.clientX - rect.left;
                        startY = e.clientY - rect.top;
                        
                        reminder.css('transition', 'none');
                        
                        e.preventDefault();
                    });
                    
                    // 处理触摸事件开始拖动
                    reminder.on('touchstart', function(e) {
                        if ($(e.target).closest('.reminder-content').length > 0) {
                            return;
                        }
                        
                        isDragging = true;
                        const touch = e.originalEvent.touches[0];
                        const rect = reminder[0].getBoundingClientRect();
                        startX = touch.clientX - rect.left;
                        startY = touch.clientY - rect.top;
                        
                        reminder.css('transition', 'none');
                        
                        e.preventDefault();
                    });
                    
                    // 处理鼠标移动
                    $(document).on('mousemove', function(e) {
                        if (!isDragging) return;
                        
                        const viewport = getViewportSize();
                        const width = reminder.outerWidth();
                        const height = reminder.outerHeight();
                        
                        let left = e.clientX - startX;
                        let top = e.clientY - startY;
                        
                        left = Math.max(0, Math.min(left, viewport.width - width));
                        top = Math.max(0, Math.min(top, viewport.height - height));
                        
                        reminder.css({
                            left: left + 'px',
                            bottom: (viewport.height - top - height) + 'px'
                        });
                    });
                    
                    // 处理触摸移动
                    $(document).on('touchmove', function(e) {
                        if (!isDragging) return;
                        
                        const touch = e.originalEvent.touches[0];
                        const viewport = getViewportSize();
                        const width = reminder.outerWidth();
                        const height = reminder.outerHeight();
                        
                        let left = touch.clientX - startX;
                        let top = touch.clientY - startY;
                        
                        left = Math.max(0, Math.min(left, viewport.width - width));
                        top = Math.max(0, Math.min(top, viewport.height - height));
                        
                        reminder.css({
                            left: left + 'px',
                            bottom: (viewport.height - top - height) + 'px'
                        });
                    });
                    
                    // 处理鼠标释放和触摸结束
                    $(document).on('mouseup touchend', function() {
                        if (!isDragging) return;
                        isDragging = false;
                        reminder.css('transition', '');
                        ensureInViewport();
                    });

                    // 监听窗口大小变化
                    $(window).on('resize', ensureInViewport);
                }
            } else {
                $('.daily-report-reminder').removeClass('show').on('transitionend', function() {
                    $(this).remove();
                });
            }
        });
    }
</script>
#@js?()
</body>
</html>
#end