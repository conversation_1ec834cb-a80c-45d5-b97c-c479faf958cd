#set(seoTitle="忘记密码")
#@layout()
#define main()
<!-- 内容容器 -->
<div class="jf-panel-box jf-pull-left">
	<!-- 注册 -->
	<div class="jf-panel jf-reg-box">
		<div id="jf-resend-activate-email">
			### <lable>注册邮箱:</lable>
			<input id="email" class="TEXT" type="text" placeholder="填写注册邮箱">
			<input id="submit_btn" class="BUTTON" type="button" value="找回密码">
		</div>
	</div>

</div>
#end

#define js()

	<script type="text/javascript">
		$(document).ready(function() {
			$("#submit_btn").bind("click", function() {
				$.ajax("/login/sendRetrievePasswordEmail", {
					type: "POST"
					, cache: false
					, dataType: "json"
					, data: { email:  $("#email").val() }
					, beforeSend: function() {
						$("#submit_btn").attr("disabled", true);
					}
					, success: function(ret) {
						layer.msg(ret.msg, {
									shift: ret.state == "ok" ? 0 : 6
									, shade: 0.3
									, time: 0
									, offset: "130px"
									, closeBtn: 1
									, shadeClose: false
									,maxWidth: "1000"
								}, function () {
									if (ret.state == "fail") {
										$("#submit_btn").attr("disabled", false);
									}
								}
						);
					}
				});
			})
		});
	</script>
#end