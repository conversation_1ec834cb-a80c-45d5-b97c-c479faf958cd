#set(seoTitle="WDS 账户增加")
#@layout()
#define main()
#include("/_view/my/common/_my_menu_bar.html")

#define content()
<div class="layui-panel">
	<div class="jfa-content" id="jfa-content">
		<form class="layui-form" id="myArticleForm" action="/my/account/save" method="post">

			<div class="layui-form-item">
				<label class="layui-form-label">头像</label>
				 <div class="layui-input-inline">
					<img src="/assets/img/touxiang.png" >
				</div>
			</div>

			<div class="layui-form-item">
				<label class="layui-form-label">昵称</label>
				 <div class="layui-input-inline">
					<input type="text" class="layui-input" name="account.nickName" value="">
				</div>
			</div>

			<div class="layui-form-item">
				<label class="layui-form-label">姓名</label>
				<div class="layui-input-inline">
					<input type="text" class="layui-input" name="account.xm" value="">
				</div>
			</div>


			<div class="layui-form-item">
				<label class="layui-form-label">手机</label>
				 <div class="layui-input-inline">
					<input type="text" class="layui-input" name="account.sj" value="">
				</div>
			</div>

			<div class="layui-form-item">
				<label class="layui-form-label">微信</label>
				 <div class="layui-input-inline">
					<input type="text" class="layui-input" name="account.wx" value="">
				</div>
			</div>

            <div class="layui-form-item">
                <label class="layui-form-label">密码</label>
                 <div class="layui-input-inline">
                    <input type="text" class="layui-input" name="account.password" value="">
                </div>
            </div>

			<div class="layui-form-item">
				<label class="layui-form-label">邮箱</label>
				 <div class="layui-input-inline">
					<input type="email" class="layui-input" name="account.userName" value="" placeholder="填写合法的 email">
				</div>
			</div>

			<div class="layui-form-item">
				<label class="layui-form-label">标签</label>
				<div class="layui-input-inline">
					<input type="text" class="layui-input" name="account.bq" value="WDS">
				</div>
			</div>

            <div class="layui-form-item">
                 <div class="layui-input-inline">
                    <input class="layui-btn layui-btn-sm" type="submit" value="提交" />
                </div>
            </div>
		</form>
	</div><!-- END OF jfa-content -->
</div><!-- END OF jfa-content-box -->
<script type="text/javascript">
	$(document).ready(function() {
		$("#myArticleForm").ajaxForm({
			dataType: "json"
			, beforeSubmit: function(formData, jqForm, options) {}
			, success: function(ret) {
				layer_alert_with_callback(ret.msg, ret.state, "/my/account/edit?id="+ret.id);
			}
			, error: function(ret) {layer_alert(ret.msg);}
			, complete: function(ret) {} 	      // 无论是 success 还是 error，最终都会被回调
		});
	});
</script>

#end
#end