#set(seoTitle="澳林匹亚管理系统")
#@layui_layout()
#define main()
<form action="/my/account/jxtj" method="post">
    <div class="layui-form-item">
        <div class="layui-inline">
            <label class="layui-form-label w50" for="ksrq">开始日期</label>
            <div class="layui-input-inline w150">
                <input type="text" class="layui-input" id="ksrq" name="ksrq" autocomplete="off" value="#(ksrq??)">
            </div>
            <script type="text/javascript">
                layui.use('laydate', function () {
                    var laydate = layui.laydate;
                    laydate.render({
                        elem: document.getElementById('ksrq')
                    });
                });
            </script>
        </div>
        <div class="layui-inline">
            <label class="layui-form-label w50" for="jsrq">结束日期</label>
            <div class="layui-input-inline w150">
                <input type="text" class="layui-input" id="jsrq" name="jsrq" autocomplete="off" value="#(jsrq??)">
            </div>
            <script type="text/javascript">
                layui.use('laydate', function () {
                    var laydate = layui.laydate;
                    laydate.render({
                        elem: document.getElementById('jsrq')
                    });
                });
            </script>
        </div>
        <div class="layui-inline">
            <label class="layui-form-label w50" for="lx">分组类型</label>
            <div class="layui-input-inline w100">
                #@ss_select("lx", "日,月,年", lx??)
            </div>
        </div>
        <div class="layui-inline">
            <label class="layui-form-label w50" for="tjlx">统计类型</label>
            <div class="layui-input-inline w100">
                #@ss_select("tjlx", "新增产品,新增询价,新增潜在客户,新增潜客沟通,新增订单维护,新增学习,新增订单金额,新增客户维护", tjlx??)
            </div>
        </div>
        <div class="layui-inline">
            <button type="submit" class="layui-btn layui-btn-sm">查看</button>
        </div>
    </div>
</form>
<hr>
<div style="height: 400px; margin: 0">
    <div id="container" style="height: 100%"></div>
</div>
#end
#define js()
<script type="application/javascript" src="/assets/echarts/echarts.min.js"></script>
<script type="application/javascript">
    var dom = document.getElementById('container');
    var myChart = echarts.init(dom, null, {
        renderer: 'canvas',
        useDirtyRect: false
    });
    var app = {};

    var option;

    option = {
        title: {
            text: '绩效统计'
        },
        tooltip: {
            trigger: 'axis'
        },
        legend: {
            data: #(rens??)
            // data: ['Email', 'Union Ads', 'Video Ads', 'Direct', 'Search Engine']
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        toolbox: {
            feature: {
                saveAsImage: {}
            }
        },
        xAxis: {
            type: 'category',
            boundaryGap: false,
            // data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
            data: #(fzs??)
        },
        yAxis: {
            type: 'value'
        },
        series: #(series??)
    };


    if (option && typeof option === 'object') {
        myChart.setOption(option);
    }

    window.addEventListener('resize', myChart.resize);
</script>
#end
