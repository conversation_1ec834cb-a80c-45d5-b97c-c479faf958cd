#set(seoTitle="WDS 账户管理")
#@layout()
#define main()
#include("/_view/my/common/_my_menu_bar.html")

#define content()
<div class="layui-panel">
	<div class="jfa-content" id="jfa-content">

		<div class="jfa-toolbar">
			<a data-pjax class="layui-btn layui-btn-sm" href="/my/account/add">
				<i class="fa fa-plus"></i>
				创建用户
			</a>
		</div>

		<div class="jfa-table-box margin-top-30">
			<table class="layui-table" lay-size="sm">
				<thead>
				<tr>
					<th>id</th>
					<th>昵称</th>
					<th>用户名</th>
					<th>微信</th>
					<th>手机</th>
					<th>标签</th>
					<th>锁定</th>
					<th style="width: 100px;">操作</th>
				</tr>
				</thead>
				<tbody>
				#for(x : wdsAccountList)
				<tr>
					<th scope="row">#(x.id)</th>
					<td>#(x.nickName)</td>
					<td>#(x.userName)</td>
					<td>#(x.wx)</td>
					<td>#(x.sj)</td>
					<td>#(x.bq??)</td>
					<td>
						<input data-id="#(x.id)" data-type="status"
							   #if(x.isStatusLockId()) checked #end
							   type="checkbox" class="mgc-switch mgc-tiny">
					</td>
					<td class="jfa-operation-button">
						<a data-pjax href="/my/account/edit?id=#(x.id)">
							<i class="fa fa-pencil" title="编辑"></i>
						</a>

						<a data-pjax href="/my/account/assignRoles?id=#(x.id)">
							<i class="fa fa-user" title="分配角色"></i>
						</a>
					</td>
				</tr>
				#end
				</tbody>
			</table>
		</div>

	</div>
</div>

<script type="text/javascript">
	$(document).ready(function() {
		initMagicInput(prepareAction);
	});

	function prepareAction($this, state) {
		return {
			url: state ? "/my/account/lock" : "/my/account/unlock",
			data : {
				id: $this.attr("data-id"),
				type: $this.attr("data-type")
			}
		}
	}
</script>
#end
#end
