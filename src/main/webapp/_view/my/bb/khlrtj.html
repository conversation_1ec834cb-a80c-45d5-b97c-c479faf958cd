#set(seoTitle="订单概况")
#@layout()
#define main()
#include("/_view/my/common/_my_menu_bar.html")
#define content()
<div>
    <div class="jf-breadcrumb-box">
        <ol class="jf-breadcrumb">
            <li><a href="/">首页</a></li>
            <li id="aolinReportModule" class="clickable-menu-item"><i class="layui-icon layui-icon-chart"></i><button class="layui-btn layui-btn-sm" onclick="openModuleModal('澳林报表')">澳林报表</button></li>
            <li class="active"><a href="/my/bb/khlrtj">客户利润统计</a></li>
        </ol>
        <div class="layui-row">
            <form class="layui-form" method="post" action="/my/bb/khlrtj">
                <div class="layui-form-item">
                    <div class="form-inline">
                        <div class="layui-input-inline w100">
                            <input class="layui-input" type="text" id="ksrq" name="ksrq" value="#(ksrq??)" autocomplete="off" placeholder="船期开始时间">
                        </div>
                        <div class="layui-input-inline w100">
                            <input class="layui-input" type="text" id="jsrq" name="jsrq" value="#(jsrq??)" autocomplete="off" placeholder="船期结束时间">
                        </div>
                        <div class="layui-input-inline">
                            <input class="layui-btn layui-btn-sm" type="submit" value="查找">
                        </div>
                        <div class="layui-input-inline">
                            <a href="#" class="layui-btn layui-btn-sm" onclick="doExport('#myTable01', {type: 'xlsx', htmlHyperlink: 'content'});">导出 <img src="/assets/img/xls.png" alt="XLSX" style="width:24px"></a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <br>
    #if(records??)
    <table id="myTable01" class="layui-table" lay-size="sm">
        <thead>
        <tr>
            <th>客户</th>
            <th>选定日期总计</th>
            <th>同比前一年总计</th>
            <th>前一自然年总计</th>
            <th>当年占比</th>
        </tr>
        </thead>
        <tbody>
        #for(record : records??)
        <tr>
            <td><a class="layui-btn layui-btn-sm" href="/my/dd?khid=#(record.khid??)" target="_blank">#(record.jc??)</a></td>
            <td style="text-align:right;"><code>#number(record.zml??0, "0.00")</code></td>
            <td style="text-align:right;"><code>#number(record.tbzml??0, "0.00")</code></td>
            <td style="text-align:right;"><code>#number(record.qnzml??0, "0.00")</code></td>
            <td style="text-align:right;"><code>#number(record.dnzb??0, "0.00")%</code></td>
        </tr>
        #end
        </tbody>
    </table>
    #else
    <span>请选择查询时间，点击查询</span>
    #end
</div>
#end
#end

#define js()
<script type="application/javascript" src="/assets/export/js-xlsx/xlsx.core.min.js"></script>
<script type="application/javascript" src="/assets/export/tableExport.min.js"></script>
<script type="text/javascript">
    layui.use('laydate', function () {
        var laydate = layui.laydate;

        laydate.render({
            elem: '#ksrq'
        });
        laydate.render({
            elem: '#jsrq'
        });
    });

    function doExport(selector, params) {
        var options = {
            fileName: '客户利润统计'
        };

        jQuery.extend(true, options, params);

        $(selector).tableExport(options);
    }
</script>
#end