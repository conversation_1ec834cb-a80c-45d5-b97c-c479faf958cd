#set(seoTitle="WDS订单概况")
#@layout()
#define main()
#include("/_view/my/common/_my_menu_bar.html")

#define content()
<div>
    <div class="jf-breadcrumb-box">
        <ol class="jf-breadcrumb">
            <li><a href="/">首页</a></li>
            <li id="yilinReportModule" class="clickable-menu-item"><i class="layui-icon layui-icon-chart-screen"></i><button class="layui-btn layui-btn-sm" onclick="openModuleModal('壹林报表')">壹林报表</button></li>
            <li class="active">WDS订单概况</li>
        </ol>
        <div class="layui-row">
             <div class="layui-input-inline">
                <form method="post" action="/my/bb/wdsddgk">
                    <input type="text" id="kssj" name="kssj" value="#(kssj??)" autocomplete="off" placeholder="请输入开始时间" size="20">
                    <input type="text" id="jssj" name="jssj" value="#(jssj??)" autocomplete="off" placeholder="请输入结束时间" size="20">
                    <input class="layui-btn layui-btn-sm" type="submit" value="查找">
                    <a href="#" onclick="doExport('#myTable01', {type: 'xlsx', htmlHyperlink: 'content'});"> <img src="/assets/img/xls.png" alt="XLSX" style="width:24px"></a>
                </form>
            </div>
        </div>
    </div>

    <br>
    #if(records??)
    <table id="myTable01"  class="layui-table" lay-size="sm">
        <thead>
        <tr>
            <th>订单</th>
            <th>船期</th>
            <th>订单金额</th>
            <th>订单金额(RMB)</th>
            <th>回款额</th>
            <th>回款额(RMB)</th>
            <th>索赔额</th>
            <th>销售回款差</th>
            <th>其他差额</th>
            <th>其他差额(RMB)</th>
            <th>运杂费</th>
            <th>材料成本</th>
            <th>外加工</th>
            <th>快递费</th>
            <th>业务相关费用</th>
            <th>毛利</th>
            <th>毛利率%</th>
        </tr>
        </thead>
        <tbody>
        #for(record : records??)
        <tr>
            <td>#(record.ddbh??)</td>
            <td>#(record.cq??)</td>
            <td style="text-align:right;">#(record.zje??)#(record.zjedw??)</td>
            <td style="text-align:right;">#(record.dkrmb??)</td>
            <td style="text-align:right;">#(record.hkje??)#(record.zjedw??)</td>
            <td style="text-align:right;">#(record.hkrmb??)</td>
            <td style="text-align:right;">#(record.spje??)</td>
            <td style="text-align:right;">#(record.cha??)</td>
            <td style="text-align:right;">#(record.qtce??)</td>
            <td style="text-align:right;">#(record.qtcermb??)</td>
            <td style="text-align:right;">#(record.hzyf??)</td>
            <td style="text-align:right;">#(record.clcb??)</td>
            <td style="text-align:right;">#(record.alje??)</td>
            <td style="text-align:right;">#(record.kdf??)</td>
            <td style="text-align:right;">#(record.pj??)</td>
            <td style="text-align:right;">#(record.ml??)</td>
            <td style="text-align:right;">#(record.mll??)</td>
        </tr>
        #end
        #for(record : totalRecords??)
        <tr>
            <td colspan="3">合计</td>
            <td style="text-align:right;">#(record.dkrmb??)</td>
            <td style="text-align:right;">-</td>
            <td style="text-align:right;">#(record.hkrmb??)</td>
            <td style="text-align:right;">#(record.spje??)</td>
            <td style="text-align:right;">#(record.cha??)</td>
            <td style="text-align:right;">#(record.qtce??)</td>
            <td style="text-align:right;">#(record.qtcermb??)</td>
            <td style="text-align:right;">#(record.hzyf??)</td>
            <td style="text-align:right;">#(record.clcb??)</td>
            <td style="text-align:right;">#(record.alje??)</td>
            <td style="text-align:right;">#(record.kdf??)</td>
            <td style="text-align:right;">#(record.pj??)</td>
            <td style="text-align:right;">#(record.ml??)</td>
            <td style="text-align:right;">#(record.mll??)</td>
        </tr>
        #end
        </tbody>
    </table>
    #else
    <span>请选择查询时间，点击查询</span>
    #end
</div>
#end
#end

#define js()
<script type="application/javascript" src="/assets/export/js-xlsx/xlsx.core.min.js"></script>
<script type="application/javascript" src="/assets/export/tableExport.min.js"></script>
<script type="text/javascript">
    layui.use('laydate', function() {
        var laydate = layui.laydate;

        laydate.render({
            elem: '#kssj'
        });
        laydate.render({
            elem: '#jssj'
        });
    });
    function doExport(selector, params) {
        var options = {
            fileName: '订单概况'
        };

        jQuery.extend(true, options, params);

        $(selector).tableExport(options);
    }
</script>
#end