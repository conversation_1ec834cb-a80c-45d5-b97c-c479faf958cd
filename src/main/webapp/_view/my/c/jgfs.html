#set(seoTitle="加工方式")
#@layout()
#define main()
#include("/_view/my/common/_my_menu_bar.html")

#define content()
<div class="jf-breadcrumb-box">
    <ol class="jf-breadcrumb">
        <li><a href="/">首页</a></li>
        <li id="resourceModule" class="clickable-menu-item"><i class="layui-icon layui-icon-app"></i><button class="layui-btn layui-btn-sm" onclick="openModuleModal('资源模块')">资源模块</button></li>
        <li class="active">加工方式</li>
    </ol>
</div>

<form class="layui-form" action="/my/c/jgfs" method="post">
    <div class="layui-form-item">
        <div class="layui-input-inline w400">
            <input type="text" class="layui-input" id="q" name="q" value="#(q??)" placeholder="请输入查询内容" autocomplete="off">
        </div>
        <div class="layui-input-inline w150">
            <button class="layui-btn layui-btn-sm" type="submit">查询</button>
        </div>
    </div>
</form>
<table class="layui-table" lay-size="sm">
    <thead>
    <tr>
        <th>图片</th>
        <th>名称</th>
        <th>英文名称</th>
        <th>编号</th>
        <th>备注</th>
    </tr>
    </thead>

    #for(x : list)
    <tr>
        <td>
            #if(x.wj??)
            #set(wjAttr=(x.wj).split(";"))
            #for(wj : wjAttr)
            #if(for.index == 0)
            <a id="a_#(x.id??)" class="fancybox" href="/upload/jgfs/#(x.id??)/mid_#(wj??)" data-fancybox-group="#(x.mc??)"
               title="#(x.mc??) #(x.ywmc??)&nbsp;<a class='layui-btn layui-btn-sm' href='/upload/jgfs/#(x.id??)/#(wj??)' target='_blank'>原图</a>&nbsp;<a class='layui-btn layui-btn-sm' data-info='#(x.id??)$$#(wj??)' onclick='deleteTp(this)'>删除</a>"><img style="height:80px; width: 80px;" src="/upload/jgfs/#(x.id??)/thumb_#(wj??)"/></a>
            #else
            <a class="fancybox" href="/upload/jgfs/#(x.id??)/mid_#(wj??)" data-fancybox-group="#(x.mc??)" title="#(x.mc??) #(x.ywmc??)&nbsp;<a class='layui-btn layui-btn-sm' href='/upload/jgfs/#(x.id??)/#(wj??)' target='_blank'>原图</a>&nbsp;<a class='layui-btn layui-btn-sm' data-info='#(x.id??)$$#(wj??)' onclick='deleteTp(this)'>删除</a>"></a>
            #end
            #end
            #end
        </td>
        <td><button class="layui-btn layui-btn-sm" style="width: 400px;" onclick='clickUp("#(x.id??)")'>#(x.mc??)</button></td>
        <td>#(x.ywmc??)</td>
        <td>#(x.bh??)</td>
        <td>#(x.bz??)</td>
    </tr>
    #end
</table>
<button id="uploadJgfstp" class="layui-btn layui-btn-sm" style="display: none">上传</button>
#end
#end
#define css()
<link type="text/css" rel="stylesheet" href="/assets/fancyBox/jquery.fancybox.css"/>
<link type="text/css" rel="stylesheet" href="/assets/fancyBox/helpers/jquery.fancybox-buttons.css"/>
<link type="text/css" rel="stylesheet" href="/assets/fancyBox/helpers/jquery.fancybox-thumbs.css"/>
<style type="text/css">
    .fancybox-custom .fancybox-skin {
        box-shadow: 0 0 50px #222;
    }
</style>
#end

#define js()
<script type="text/javascript" src="/assets/fancyBox/jquery.mousewheel-3.0.6.pack.js"></script>
<script type="text/javascript" src="/assets/fancyBox/jquery.fancybox.pack.js"></script>
<script type="text/javascript" src="/assets/fancyBox/helpers/jquery.fancybox-buttons.js"></script>
<script type="text/javascript" src="/assets/fancyBox/helpers/jquery.fancybox-media.js"></script>
<script type="text/javascript" src="/assets/fancyBox/helpers/jquery.fancybox-thumbs.js"></script>

<script type="text/javascript">
    function deleteTp(obj) {
        var info = obj.getAttribute("data-info");
        var infos = info.split("$$");
        confirmAjaxGet("确定要删除[" + infos[1] + "]？", "/my/c/delectJgfstp?id=" + infos[0] + "&wj=" + infos[1], {
            success: function (ret) {
                layer_alert(ret.msg, 3000);
            }
        });
    }

    var up = layui.upload.render({
        elem: '#uploadJgfstp'
        , timeout: 300000
        , url: '/my/c/uploadJgfstp'
        , accept: 'file' //普通文件
        , done: function (res) {
            layer.msg('上传成功');
            console.log(res);
        }
    });

    function clickUp(id) {
        up.reload({
            data: {id: id}
        });
        $("#uploadJgfstp").click();
    }


    $(document).ready(function () {
        $('.fancybox').fancybox();
    });

</script>
#end