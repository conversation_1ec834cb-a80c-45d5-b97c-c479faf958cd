<!DOCTYPE html>
<html>
<head>
    <title>电话系统</title>
    <script src="https://sdk.twilio.com/js/client/releases/1.13.0/twilio.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <div id="phone-app">
        <input type="text" id="phone-number" placeholder="输入电话号码">
        <input type="text" id="TWILIO_NUMBER" placeholder="输入拨打号码电话号码" value="+16088122693">
        <button onclick="makeCall()">拨打电话</button>
        <div id="call-status"></div>
    </div>

    <script>
        function makeCall() {
            const phoneNumber = $('#phone-number').val();
            const TWILIO_NUMBER = $('#TWILIO_NUMBER').val();

            $.ajax({
                url: '/my/c/makeCall',
                method: 'POST',
                data: {
                    toNumber: phoneNumber,
                    TWILIO_NUMBER: TWILIO_NUMBER
                },
                success: function(data) {
                    $('#call-status').text('通话已开始，CallSID: ' + data.callSid);
                    // 可以用callSid后续查询录音
                },
                error: function(err) {
                    $('#call-status').text('通话失败: ' + err.message);
                }
            });
        }
    </script>
</body>
</html>