#set(seoTitle="图纸审核映射磁盘")
#@layout()
#define main()
#include("/_view/my/common/_my_menu_bar.html")

#define content()
<div style="width: 80%; margin-left: 30px;">
    <div class="layui-card">
        <div class="layui-card-header" style="max-height: 80px;">
            <div class="layui-row"><h1>图纸审核映射磁盘说明(win10和win7) 映射路径:<font color="red">\\192.168.1.23\share\dd_sh\sht</font></h1></div>
        </div>
        <div class="layui-card-header" style="max-height: 80px;">
            <div class="layui-row"><h2>上传的时候创建的文件夹名字要和唛头一致，包括大小写，比如AB01，就只能是AB01，不能是ab01或者AB-01</h2></div>
        </div>
        <div class="layui-card-body">
            <div class="layui-card">
                <div class="layui-card-header">
                    <h3>Windows 10 设置方式 </h3>
                </div>
                <div class="layui-card-body">
                    <div class="layui-row"><video src="/assets/video/cpys.mp4" controls></video></div>
                    <div class="layui-row"><img src="/assets/img/win10/1.png"></div>
                    <div class="layui-row"><img src="/assets/img/win10/2.png"></div>
                    <div class="layui-row"><img src="/assets/img/win10/3.png"></div>
                    <div class="layui-row"><img src="/assets/img/win10/4.png"></div>
                    <div class="layui-row"><img src="/assets/img/win10/5.png"></div>
                </div>
            </div>
            <hr>
            <div class="layui-card">
                <div class="layui-card-header">
                    <h3>Windows 7 设置方式</h3>
                </div>
                <div class="layui-card-body">
                    <div class="layui-row"><img src="/assets/img/win10/1.png"></div>
                    <div class="layui-row"><img src="/assets/img/win10/2.png"></div>
                    <div class="layui-row"><img src="/assets/img/win10/3.png"></div>
                    <div class="layui-row"><img src="/assets/img/win10/4.png"></div>
                    <div class="layui-row"><img src="/assets/img/win10/5.png"></div>
                </div>
            </div>
        </div>
    </div>
</div>
#end
#end

#define js()
<script type="text/javascript">

</script>
#end
