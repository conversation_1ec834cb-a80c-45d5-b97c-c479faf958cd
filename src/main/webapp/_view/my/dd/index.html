#set(seoTitle="订单管理")
#@layout()
#define main()
#include("/_view/my/common/_my_menu_bar.html")

#define content()
<div>
    <div class="jf-breadcrumb-box">
        <ol class="jf-breadcrumb">
            <li><a href="/">首页</a></li>
            <li id="businessRelated" class="clickable-menu-item"><i class="layui-icon layui-icon-form"></i>
                <button class="layui-btn layui-btn-sm" onclick="openModuleModal('业务相关')">业务相关</button>
            </li>
            <li class="active">订单录入</li>
        </ol>
        <div class="jf-btn-box">
            #role("权限管理员", "超级管理员", "总经理")
            <a class="layui-btn layui-btn-sm" href="/my/cgd">采购单管理</a>
            #end
            <a class="layui-btn layui-btn-sm" href="/my/dd/add">创&nbsp;&nbsp;建</a>
        </div>
    </div>
    <hr>
    <form method="post" action="/my/dd" class="layui-form" id="searchForm">
        <div class="layui-form-item" style="margin-left: 50px;">
            <div class="layui-input-inline w100" title="搜指定订单编号，模糊搜索">
                <input type="text" id="query" name="query" class="layui-input" value="#(query??)" placeholder="订单编号模糊搜索">
            </div>
            <div class="layui-input-inline w100" title="搜指定工程编号，模糊搜索">
                <input type="text" id="queryGcdd" name="queryGcdd" class="layui-input" value="#(queryGcdd??)" placeholder="工程编号">
            </div>
            <div class="layui-input-inline w100" title="搜指定客户订单号，模糊搜索">
                <input type="text" id="queryKhdd" name="queryKhdd" class="layui-input" value="#(queryKhdd??)" placeholder="客户订单号">
            </div>
            <div class="layui-input-inline w100" title="搜指定石种，模糊搜索">
                <input type="text" id="querySz" name="querySz" class="layui-input" value="#(querySz??)" placeholder="石种">
            </div>
            <div class="layui-input-inline w100" title="搜指定品名，模糊搜索">
                <input type="text" id="queryPm" name="queryPm" class="layui-input" value="#(queryPm??)" placeholder="品名">
            </div>
            <div class="layui-input-inline w100" title="搜指定加工方式，模糊搜索">
                <input type="text" id="queryJgfs" name="queryJgfs" class="layui-input" value="#(queryJgfs??)" placeholder="加工方式">
            </div>
            <div class="layui-input-inline w100" title="搜指定货号/包装备注，模糊搜索">
                <input type="text" id="queryBzbz" name="queryBzbz" class="layui-input" value="#(queryBzbz??)" placeholder="货号/包装备注">
            </div>
            <div class="layui-input-inline w150" title="搜订单类型">
                #@ss_select("queryLx", ",正常订单,询价单,其他代垫", queryLx??)
            </div>
            <div class="layui-input-inline w150" title="搜指定客户订单">
                <select lay-verify="required" lay-search="" id="khid" name="khid" title="客户">
                    <option value="">请选择客户</option>
                    #for( kh : khList)
                    <option value="#(kh.id??)" #if(kh.id??==khid??) selected #end>#(kh.jc??)</option>
                    #end
                </select>
            </div>
            <div class="layui-input-inline w200" title="搜含零价格工程">
                #@ss_select("queryLjg", ",含零价格工程,含零价格工程有索赔,含零价格工程无索赔", queryLjg??)
            </div>
            <div class="layui-input-inline w100" title="搜>录入时间">
                <input class="layui-input" type="text" id="queryLrKsrq" name="queryLrKsrq" value="#(queryLrKsrq??)" autocomplete="off" placeholder="录入开始日期" size="12">
            </div>
            <div class="layui-input-inline w100" title="搜<录入时间>">
                <input class="layui-input" type="text" id="queryLrJsrq" name="queryLrJsrq" value="#(queryLrJsrq??)" autocomplete="off" placeholder="录入结束日期" size="12">
            </div>
            <div class="layui-input-inline w100" title="搜>船期">
                <input class="layui-input" type="text" id="queryCqKsrq" name="queryCqKsrq" value="#(queryCqKsrq??)" autocomplete="off" placeholder="船期开始日期" size="12">
            </div>
            <div class="layui-input-inline w100" title="搜<船期">
                <input class="layui-input" type="text" id="queryCqJsrq" name="queryCqJsrq" value="#(queryCqJsrq??)" autocomplete="off" placeholder="船期结束日期" size="12">
            </div>
            <div class="layui-input-inline w160" title="是否填货好时间">
                #@ss_select("queryHhsj", ",已填货好时间,未填货好时间", queryHhsj??)
            </div>
            <div class="layui-input-inline w160" title="是否填船期时间">
                #@ss_select("queryCq", ",已填船期,未填船期", queryCq??)
            </div>
            <div class="layui-input-inline w160" title="本厂/外厂">
                #@ss_select("queryBc", ",壹林,壹林+外厂,外厂", queryBc??)
            </div>
            <div class="layui-input-inline w160" title="单行订单明细含有,整个订单含有">
                #@ss_select("queryMx", "整个订单含有,单行订单明细含有", queryMx??)
            </div>
            <div class="layui-input-inline w00">
                <div class="layui-col-lg1"><button type="submit" class="layui-btn layui-btn-sm" lay-filter="component-form-element">查找</button></div>
            </div>
        </div>
    </form>
    <hr>
    <div class="layui-row"><a class=" layui-btn layui-btn-sm" href="dd/cpys" target="_blank">图纸审核快速访问方式</a></div>
    <table class="layui-table" lay-size="sm">
        <thead>
        <tr>
            <td style="width:100px;">订单编号</td>
            <td style="width:80px;">工程数</td>
            <td>总才数</td>
            <td>总重量</td>
            <td>总金额</td>
            <td>创建人</td>
            <td>录入时间</td>
            <td>船期</td>
            <td>操作</td>
            <td>下单审核</td>
            <td>出货图片审核</td>
        </tr>
        </thead>
        <tbody>
        #for( x: page.list )
        <tr>
            <td title="#(x.ddbh??)">
                <a href="/my/dd/edit?id=#(x.id??)" target="_blank">#(x.ddbh??)</a>
                <button name="showOrderProgressBtn" data-ddbh="#(x.ddbh??)"><i class="fa fa-list-ol"></i></button>
                <button name="emailBtn" class="email-btn" data-osclub-id="#(x.kh??)" title="邮件管理"><i class="fa fa-envelope"></i></button>
            </td>
            <td>#(x.count_gcdd??)</td>
            <td>#(x.zcs??)</td>
            <td>#(x.zzl??)</td>
            <td>#(x.zje??)#(x.hb??)</td>
            <td>#(x.lrr??)</td>
            <td>#(x.lrsj??)</td>
            <td>#(x.cq??)</td>
            <td>
                <div class="form-inline">
                    <div class="layui-form-item">
                        <div class="layui-input-inline w50">
                            <a class="layui-btn layui-btn-sm" href="/my/dd/edit?id=#(x.id??)" target="_blank">编辑</a>
                        </div>
                        <div class="layui-input-inline w50">
                            <button class="layui-btn layui-btn-sm layui-btn-normal show-ddmxhj-btn" data-ddbh="#(x.ddbh??)"><i class="layui-icon layui-icon-table"></i>查阅</button>
                        </div>
                        <div class="layui-input-inline w50">
                            <button class="layui-btn layui-btn-sm show-ddmx-btn" data-ddbh="#(x.ddbh??)" title="符合查询条件的该订单明细"><i class="layui-icon layui-icon-chart"></i></button>
                        </div>
                        <div class="layui-input-inline w60">
                            <div class="layui-btn-group">
                                <button type="button" class="layui-btn layui-btn-sm" id="moreBtn_#(x.ddbh??)" lay-dropdown>更多</button>
                            </div>
                            <div class="layui-dropdown-menu" id="moreMenu_#(x.ddbh??)" style="display:none;">
                                <div class="layui-dropdown-panel">
                                    <div class="dropdown-header">
                                        <span>&nbsp;&nbsp;#(x.ddbh??):</span>
                                        <i class="layui-icon layui-icon-close dropdown-close" onclick="hideDropdownMenu(this)"></i>
                                    </div>
                                    <a class="layui-dropdown-item" href="/my/order/ddKz?ddbh=#(x.ddbh??)" target="_blank">
                                        <i class="layui-icon layui-icon-form"></i> 订单状态
                                    </a>
                                    <a class="layui-dropdown-item" href="/my/dzhy/edit?ddbh=#(x.ddbh??)" target="_blank">
                                        <i class="layui-icon layui-icon-file"></i> 单证信息
                                    </a>
                                    <a class="layui-dropdown-item" href="/my/order/payment?khid=#(x.kh??)" target="_blank">
                                        <i class="layui-icon layui-icon-dollar"></i> Payment
                                    </a>
                                    <a class="layui-dropdown-item" href="/my/dd/zdcg?ddbh=#(x.ddbh??)" target="_blank">
                                        <i class="layui-icon layui-icon-cart"></i> 整单采购
                                    </a>
                                    <a class="layui-dropdown-item" href="javascript:void(0);" onclick="confirmAjax('确定发送 #escape(x.ddbh??) 现在的航行轨迹给客户？', '/my/dd/vesselfinder?ddbh=#(x.ddbh??)')">
                                        <i class="layui-icon layui-icon-location"></i> 发送船迹
                                    </a>
                                    <a class="layui-dropdown-item" href="javascript:void(0);" onclick="changeZxdID('#(x.ddbh??)');">
                                        <i class="layui-icon layui-icon-refresh"></i> 更新装箱单ID
                                    </a>
                                    <a class="layui-dropdown-item" href="javascript:void(0);" onclick="changeName('#(x.ddbh??)');">
                                        <i class="layui-icon layui-icon-edit"></i> 改订单编号
                                    </a>
                                    <a class="layui-dropdown-item" href="javascript:void(0);" onclick="showCzjl('#(x.ddbh??)');">
                                        <i class="layui-icon layui-icon-log"></i> 操作记录
                                    </a>
                                    #role("权限管理员", "超级管理员", "总经理")
                                    <hr class="layui-dropdown-line">
                                    <a class="layui-dropdown-item" href="javascript:void(0);" onclick="unlock('#(x.ddbh??)');">
                                        <i class="layui-icon layui-icon-unlock"></i> 编辑解锁
                                    </a>
                                    <a class="layui-dropdown-item" href="/my/bb/ddgk?ddbh=#(x.ddbh??)&kssj=2000-01-01&jssj=2099-12-31" target="_blank">
                                        <i class="layui-icon layui-icon-chart"></i> 订单概况
                                    </a>
                                    <a class="layui-dropdown-item" href="javascript:void(0);" onclick="confirmAjaxLayer('删除 #escape(x.ddbh??) 后无法恢复，确定要删除？', '/my/dd/delete?ddbh=#(x.ddbh??)', this);">
                                        <i class="layui-icon layui-icon-delete"></i> 删除
                                    </a>
                                    #end
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </td>
            <td>
                <div class="layui-btn-group">
                    <button class="layui-btn layui-btn-sm" onclick='clickUp1("#(x.ddbh??)")'>上传</button>
                    <a class="layui-btn #if(x.tzsh??'未审核'!='未审核') layui-btn-normal #end layui-btn-sm" href="/my/dd/editShtz?id=#(x.id??)&ddbh=#(x.ddbh??)&type=sht" target="_blank">[#(x.tzsh??"未审核")]查看</a>
                </div>
            </td>
            <td>
                <div class="layui-btn-group">
                    <button class="layui-btn layui-btn-sm" onclick='clickUp2("#(x.ddbh??)")'>上传</button>
                    <a class="layui-btn #if(x.cpsh??'未审核'!='未审核') layui-btn-normal #end layui-btn-sm" href="/my/dd/editShtz?id=#(x.id??)&ddbh=#(x.ddbh??)&type=cpt" target="_blank">[#(x.cpsh??"未审核")]查看</a>
                </div>
            </td>
        </tr>
        #end
        </tbody>
    </table>
    <button id="uploadCltp" class="layui-btn layui-btn-sm" style="display: none">上传</button>
    #@paginate(page.pageNumber, page.totalPage, "/my/dd?query=" + query + "&queryGcdd=" + queryGcdd + "&queryPm=" + queryPm + "&khid=" + khid + "&p=")
</div>
<div id="defaultModal" class="layui-form" style="display: none;">
    <input type="hidden" id="ddbh" name="ddbh">
    <div class="layui-form-item">
        <div class="form-inline">
            <label class="layui-form-label">新订单编号</label>
            <div class="layui-input-inline">
                <input type="text" class="layui-input" id="xddbh" name="xddbh">
            </div>
            <div class="layui-input-inline">
                <button class="layui-input layui-btn-sm" onclick="updateName()">更新</button>
            </div>
        </div>
    </div>
</div>


<div id="uploadModal" class="layui-form" style="display: none;">
    <form class="layui-form" style="height: 50px;" id="uploadModalForm" action="#(wl??)upload/index" method="post" enctype="multipart/form-data">
        <input type="hidden" id="upload_modal_ddbh" name="ddbh">
        <input type="hidden" id="upload_modal_type" name="type">
        <div class="layui-form-item">
            <div class="form-inline">
                <div class="layui-input-inline">
                    <input type="file" class="layui-input" name="files" multiple="multiple">
                </div>
                <div class="layui-input-inline">
                    <button onclick="updateRemoteSht()">上传</button>
                </div>
            </div>
        </div>
    </form>
</div>
#end
#end

#define js()
<script type="text/javascript">
    document.addEventListener('DOMContentLoaded', function () {
        document.getElementById('searchForm').addEventListener('keydown', function (event) {
            if (event.key === 'Enter') {
                event.preventDefault();
                this.submit();
            }
        });
    });
    function renderTemplate(data) {
        var html = '<table class="layui-table layui-table-compact">';
        html += '<thead><tr><th style="width: 50px;">序号</th><th>流程名称</th><th>选择项</th><th>更新时间</th><th>更新人</th><th>备注</th></tr></thead>';
        html += '<tbody>';

        data.forEach(function (item, index) {
            html += '<tr data-id="' + item.id + '" data-lcid="' + item.lcid + '">';
            html += '<td>' + (index + 1) + '</td>';
            html += '<td>' + item.lcmc + '</td>';
            html += '<td><div class="layui-btn-group layui-btn-group-compact">';

            let xzx = item.xzx;
            if (xzx && xzx.length > 0) {
                var options = xzx.split(',');
                options.forEach(function (option) {
                    let zhi = item.zhi;
                    if (zhi && zhi.length > 0) {
                        var zhis = zhi.split(',');
                        if (zhis && zhis.length > 0 && zhis.includes(option)) {
                            html += '<button type="button" class="layui-btn layui-btn-sm layui-btn-normal" data-xzx="' + option + '">' + option + '</button>';
                        } else {
                            html += '<button type="button" class="layui-btn layui-btn-sm layui-btn-primary" data-xzx="' + option + '">' + option + '</button>';
                        }
                    } else {
                        html += '<button type="button" class="layui-btn layui-btn-sm layui-btn-primary" data-xzx="' + option + '">' + option + '</button>';
                    }
                });
            }

            html += '</div></td>';
            html += '<td>' + item.gxsj + '</td>';
            html += '<td>' + item.gxr + '</td>';
            html += '<td><input type="text" class="layui-input layui-input-compact" name="remark" value="' + (item.bz || '') + '" placeholder="请输入备注"></td>';
            html += '</tr>';
        });

        html += '<tr></tr></tbody></table>';
        return html;
    }

    $(document).ready(function () {
        $('button[name="showOrderProgressBtn"]').on('click', function () {
            var ddbh = $(this).data('ddbh');

            $.ajax({
                url: '/my/dd/ddlc?ddbh=' + ddbh,
                type: 'GET',
                success: function (res) {
                    if (res.state === "ok") {
                        layui.use(['layer', 'laytpl'], function () {
                            var layer = layui.layer;

                            let data = res.data;
                            var dataJson = JSON.parse(data);
                            var html = renderTemplate(dataJson);
                            // 打开 Layui 弹窗
                            layer.open({
                                type: 1,
                                title: '订单进度 - ' + ddbh,
                                content: html,
                                area: ['80%', '90%'],
                                btn: ['保存', '取消'],
                                yes: function (index, layero) {
                                    // 收集所有的备注
                                    var updates = [];
                                    var m = 0;
                                    layero.find('tr').each(function (i, row) {
                                        let rowData = $(row);
                                        var id = $(rowData).data('id');
                                        var lcid = $(rowData).data('lcid');
                                        if (lcid) {
                                            var dataJsonElement = dataJson[m++];
                                            if (dataJsonElement && typeof dataJsonElement == "object" && "id" in dataJsonElement) {
                                                var remark = rowData.find('input[name="remark"]').val();
                                                var selectedOptions = [];
                                                rowData.find('.layui-btn-group-compact button').each(function () {
                                                    if (!$(this).hasClass('layui-btn-primary')) {
                                                        selectedOptions.push($(this).data('xzx'));
                                                    }
                                                });
                                                updates.push({
                                                    ddbh: ddbh,
                                                    id: id,
                                                    lcid: lcid,
                                                    bz: remark,
                                                    xzx: selectedOptions.join(',')
                                                });
                                            }
                                        }
                                    });

                                    // 发送更新请求到服务器
                                    $.ajax({
                                        url: '/my/dd/update_ddlc_remarks',
                                        type: 'POST',
                                        contentType: 'application/json',
                                        data: JSON.stringify(updates),
                                        success: function (updateRes) {
                                            if (updateRes.state === "ok") {
                                                layer.msg('保存成功');
                                                layer.close(index);
                                            } else {
                                                layer.msg('保存失败: ' + updateRes.msg);
                                            }
                                        },
                                        error: function () {
                                            layer.msg('保存失败，请稍后重试');
                                        }
                                    });
                                }
                            });

                            // 修改选择项按钮的点击事件处理
                            $('.layui-btn-group-compact button').on('click', function () {
                                $(this).toggleClass('layui-btn-primary layui-btn-normal');
                            });
                        });
                    } else {
                        layer.msg('获取数据失败: ' + res.msg);
                    }
                },
                error: function () {
                    layer.msg('获取数据失败，请稍后重试');
                }
            });
        });
    });

    layui.use('laydate', function () {
        var laydate = layui.laydate;

        laydate.render({
            elem: '#queryLrKsrq'
        });
        laydate.render({
            elem: '#queryLrJsrq'
        });
        laydate.render({
            elem: '#queryCqKsrq'
        });
        laydate.render({
            elem: '#queryCqJsrq'
        });
    });

    function updateRemoteSht() {
        $("#uploadModalForm").ajaxForm({
            jsonp: "callback",
            success: function (ret) {
                console.log("success:" + ret);
                layer_alert(ret.msg);
                document.getElementById("uploadModal").style.display = "none";
            }
            , error: function (jqXHR, textStatus, errorThrown) {
                layer_alert("上传成功!");
                console.dir(jqXHR);
                console.dir(textStatus);
                console.dir(errorThrown);
                document.getElementById("uploadModal").style.display = "none";
            }
        });
    }

    function clickUp1(ddbh) {
        document.getElementById("upload_modal_ddbh").value = ddbh;
        document.getElementById("upload_modal_type").value = "sht";
        document.getElementById("uploadModal").style.display = "";
        layer.open({
            type: 1,
            title: "上传审核图片[" + ddbh + "]",
            content: $('#uploadModal'),
            end: function () {
                document.getElementById("uploadModal").style.display = "none";
            }
        });
    }

    function clickUp2(ddbh) {
        document.getElementById("upload_modal_ddbh").value = ddbh;
        document.getElementById("upload_modal_type").value = "cpt";
        document.getElementById("uploadModal").style.display = "";
        layer.open({
            type: 1,
            title: "上传审核图片[" + ddbh + "]",
            content: $('#uploadModal'),
            end: function () {
                document.getElementById("uploadModal").style.display = "none";
            }
        });
    }

    function changeName(ddbh) {
        document.getElementById("ddbh").value = ddbh;
        layer.open({
            type: 1,
            title: "修改订单编号[" + ddbh + "]",
            content: $('#defaultModal'),
            end: function () {
                document.getElementById("defaultModal").style.display = "none";
            }
        });
    }

    function unlock(ddbh) {
        $.ajax({
            url: "/my/dd/unlock",
            type: "POST",
            cache: false,
            dataType: "json",
            data: {
                "ddbh": ddbh
            },
            success: function (ret) {
                layer.msg(ret.msg, {time: 2000, shade: 0.3});
            },
            fail: function (ret) {
                showReplyErrorMsg(ret.msg);
                return false;
            }
        });
    }

    function changeZxdID(ddbh) {
        $.ajax({
            url: "/my/dd/gxzxdId",
            type: "POST",
            cache: false,
            dataType: "json",
            data: {
                "ddbh": ddbh
            },
            success: function (ret) {
                showReplyErrorMsg(ret.msg);
                location.reload();
            },
            fail: function (ret) {
                showReplyErrorMsg(ret.msg);
                return false;
            }
        });
    }

    function updateName() {
        $.ajax({
            url: "/my/dd/xgddh",
            type: "POST",
            cache: false,
            dataType: "json",
            data: {
                "ddbh": document.getElementById("ddbh").value,
                "xddbh": document.getElementById("xddbh").value
            },
            success: function (ret) {
                showReplyErrorMsg(ret.msg);
                location.reload();
            },
            fail: function (ret) {
                showReplyErrorMsg(ret.msg);
                return false;
            }
        });
    }

    function showCzjl(ddbh) {
        layer.open({
            type: 2,
            title: false,
            area: ['96%', '80%'],
            fix: false,
            maxmin: false,
            content: '/my/dd/showCzjl?ddbh=' + ddbh
        });
    }

    // 下拉菜单功能
    layui.use(['dropdown', 'layer'], function(){
        var dropdown = layui.dropdown;
        var activeMenu = null;

        // 全局函数，用于关闭下拉菜单
        window.hideDropdownMenu = function(closeIcon) {
            var menu = $(closeIcon).closest('.layui-dropdown-menu');
            menu.hide();
            if (activeMenu && activeMenu.get(0) === menu.get(0)) {
                activeMenu = null;
            }
        };

        function hideActiveMenu() {
            if (activeMenu) {
                activeMenu.hide();
                activeMenu = null;
            }
        }

        // 为每个"更多"按钮绑定点击事件
        $('[lay-dropdown]').each(function() {
            var btn = $(this);
            var menuId = btn.attr('id').replace('moreBtn_', 'moreMenu_');
            var menu = $('#' + menuId);

            // 点击按钮时显示/隐藏菜单
            btn.on('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // 如果当前菜单已经显示，则隐藏它
                if (menu.is(':visible')) {
                    hideActiveMenu();
                    return;
                }

                // 隐藏其他菜单
                hideActiveMenu();

                // 获取最新的按钮位置和窗口信息
                var offset = btn.offset();
                var windowHeight = $(window).height();
                var scrollTop = $(window).scrollTop();
                var menuHeight = menu.outerHeight() || 300; // 预估高度
                var buttonHeight = btn.outerHeight();

                // 计算按钮在视口中的位置
                var buttonTopInViewport = offset.top - scrollTop;
                var buttonBottomInViewport = buttonTopInViewport + buttonHeight;

                // 计算可用空间
                var spaceBelow = windowHeight - buttonBottomInViewport;
                var spaceAbove = buttonTopInViewport;

                // 决定菜单显示在上方还是下方
                if (spaceBelow >= menuHeight || spaceBelow >= spaceAbove) {
                    // 显示在下方
                    menu.css({
                        position: 'fixed',
                        left: offset.left + 'px',
                        top: buttonBottomInViewport + 2 + 'px',
                        bottom: 'auto',
                        'z-index': 9999
                    }).show();
                } else {
                    // 显示在上方
                    menu.css({
                        position: 'fixed',
                        left: offset.left + 'px',
                        top: buttonTopInViewport - menuHeight - 2 + 'px',
                        bottom: 'auto',
                        'z-index': 9999
                    }).show();
                }

                activeMenu = menu;
            });

            // 为菜单中的每个项目添加点击处理
            menu.find('.layui-dropdown-item').on('click', function(e) {
                e.stopPropagation();
                hideActiveMenu();
            });
        });

        // 在document上绑定点击事件来处理点击外部
        $(document).on('click', function(e) {
            hideActiveMenu();
        });

        // 阻止菜单本身的点击事件冒泡
        $('.layui-dropdown-menu').on('click', function(e) {
            e.stopPropagation();
        });

        // 监听窗口滚动
        $(window).on('scroll', function() {
            hideActiveMenu();
        });

        // 监听窗口大小改变
        $(window).on('resize', function() {
            hideActiveMenu();
        });
    });

    layui.use(['laydate', 'form', 'element', 'table'], function () {
        var laydate = layui.laydate;
        var form = layui.form;
        var element = layui.element;
        var table = layui.table;

        laydate.render({
            elem: '#queryLrKsrq'
        });

        laydate.render({
            elem: '#queryLrJsrq'
        });

        laydate.render({
            elem: '#queryCqKsrq'
        });

        laydate.render({
            elem: '#queryCqJsrq'
        });

        $(document).on('click', '.show-ddmx-btn', function() {
            var ddbh = $(this).data('ddbh');
            showDdmxDialog(ddbh);
        });

        function showDdmxDialog(ddbh) {
            $.ajax({
                url: '/my/dd/showDdmx',
                type: 'GET',
                data: {
                    ddbh: ddbh,
                    query: $('#query').val(),
                    queryGcdd: $('#queryGcdd').val(),
                    queryKhdd: $('#queryKhdd').val(),
                    queryPm: $('#queryPm').val(),
                    querySz: $('#querySz').val(),
                    queryJgfs: $('#queryJgfs').val()
                },
                success: function(res) {
                    if (res.state === 'ok') {
                        var data = res.data;

                        // 构建表格HTML
                        var html = '<div class="layui-card"><div class="layui-card-body" style="overflow-x: auto;">';
                        html += '<table class="layui-table" lay-size="sm">';
                        html += '<thead><tr style="background-color: #f2f2f2;">';
                        html += '<th>工程</th><th>客户订单号</th><th>品名</th><th>石种</th><th>size a*b*c</th><th>单位</th><th>单价</th><th>才数</th><th>总金额</th>';
                        html += '</tr></thead><tbody>';

                        // 遍历数据添加行
                        $.each(data, function(i, record) {
                            html += '<tr>';
                            html += '<td>' + (record.gcdd || '') + '</td>';
                            html += '<td>' + (record.khddh || '') + '</td>';
                            html += '<td>' + (record.zwpm || '') + '</td>';
                            html += '<td>' + (record.zwszm || '') + '</td>';
                            html += '<td>' + (record.sizea || '') + '*' + (record.sizeb || '') + '*' + (record.sizec || '') + '</td>';
                            html += '<td>' + (record.dw || '') + '</td>';
                            html += '<td style="text-align:right;">' + (record.dj || '0.00') + '</td>';
                            html += '<td style="text-align:right;">' + (record.cs || '0.00') + '</td>';
                            html += '<td style="text-align:right;font-weight:bold;color:#009688;">' + (record.mhzj || '0.00') + '</td>';
                            html += '</tr>';
                        });

                        html += '</tbody></table></div></div>';
                        layer.open({
                            type: 1,
                            title: '订单明细 - ' + ddbh,
                            content: html,
                            area: ['95%', '80%'],
                            maxmin: true,
                            shadeClose: true,
                            btn: ['关闭'],
                            yes: function(index){
                                layer.close(index);
                            }
                        });
                    }
                }
            })
        }

        // 初始化查阅按钮点击事件
        $(document).on('click', '.show-ddmxhj-btn', function() {
            var ddbh = $(this).data('ddbh');
            showDdmxhjDialog(ddbh);
        });

        // 显示订单明细汇总弹窗
        function showDdmxhjDialog(ddbh) {
            $.ajax({
                url: '/my/dd/getDdmxhj',
                type: 'GET',
                data: { ddbh: ddbh },
                success: function(res) {
                    if (res.state === 'ok') {
                        var data = res.data;

                        // 创建表格HTML
                        var tableHtml = '<div class="layui-card"><div class="layui-card-header">总计信息 (工程数: ' + data.length + ')</div>' +
                            '<div class="layui-card-body" style="overflow-x: auto;">' +
                            '<table class="layui-table" lay-size="sm">' +
                            '<thead><tr>' +
                            '<th>工程名称</th>' +
                            '<th>客户订单号</th>' +
                            '<th>石种</th>' +
                            '<th>总数量</th>' +
                            '<th>总才数</th>' +
                            '<th>立方数</th>' +
                            '<th>总重量</th>' +
                            '<th>总重量KGS</th>' +
                            '<th>总重量LBS</th>' +
                            '<th>材料总金额</th>' +
                            '<th>附加费</th>' +
                            '<th>材料附加费</th>' +
                            '<th>刻字附加费</th>' +
                            '<th>合计总金额</th>' +
                            '</tr></thead><tbody>';

                        // 计算合计
                        var totalSl = 0, totalCs = 0, totalLfs = 0, totalZl = 0, totalZllbs = 0;
                        var totalMhzj = 0, totalFjf = 0, totalClfjf = 0, totalKzfjf = 0, totalClzje = 0;

                        // 添加数据行
                        $.each(data, function(i, item) {
                            tableHtml += '<tr>' +
                                '<td><strong>' + (item.gcdd || '') + '</strong></td>' +
                                '<td>' + (item.khddh || '') + '</td>' +
                                '<td>' + (item.zwszm || '') + '</td>' +
                                '<td style="text-align:right;">' + (item.sl || '0.00') + '</td>' +
                                '<td style="text-align:right;">' + (item.cs || '0.00') + '</td>' +
                                '<td style="text-align:right;">' + (item.lfs || '0.00') + '</td>' +
                                '<td style="text-align:right;">' + (item.zl || '0.00') + '</td>' +
                                '<td style="text-align:right;">' + (item.zl || '0.00') + '</td>' +
                                '<td style="text-align:right;">' + (item.zllbs || '0.00') + '</td>' +
                                '<td style="text-align:right;">' + (item.clzje || '0.00') + '</td>' +
                                '<td style="text-align:right;">' + (item.fjf || '0.00') + '</td>' +
                                '<td style="text-align:right;">' + (item.clfjf || '0.00') + '</td>' +
                                '<td style="text-align:right;">' + (item.kzfjf || '0.00') + '</td>' +
                                '<td style="text-align:right;color:#009688;font-weight:bold;">' + (item.mhzj || '0.00') + '</td>' +
                                '</tr>';

                            // 累加合计
                            totalSl += parseFloat(item.sl || 0);
                            totalCs += parseFloat(item.cs || 0);
                            totalLfs += parseFloat(item.lfs || 0);
                            totalZl += parseFloat(item.zl || 0);
                            totalZllbs += parseFloat(item.zllbs || 0);
                            totalMhzj += parseFloat(item.mhzj || 0);
                            totalFjf += parseFloat(item.fjf || 0);
                            totalClfjf += parseFloat(item.clfjf || 0);
                            totalKzfjf += parseFloat(item.kzfjf || 0);
                            totalClzje += parseFloat(item.clzje || 0);
                        });

                        // 添加合计行
                        tableHtml += '<tr style="background-color:#f2f2f2;">' +
                            '<td colspan="3" style="text-align:center;font-weight:bold;">订单总计</td>' +
                            '<td style="text-align:right;font-weight:bold;">' + totalSl.toFixed(2) + '</td>' +
                            '<td style="text-align:right;font-weight:bold;">' + totalCs.toFixed(2) + '</td>' +
                            '<td style="text-align:right;font-weight:bold;">' + totalLfs.toFixed(2) + '</td>' +
                            '<td style="text-align:right;font-weight:bold;">' + totalZl.toFixed(2) + '</td>' +
                            '<td style="text-align:right;font-weight:bold;">' + totalZl.toFixed(2) + '</td>' +
                            '<td style="text-align:right;font-weight:bold;">' + totalZllbs.toFixed(2) + '</td>' +
                            '<td style="text-align:right;font-weight:bold;">' + totalClzje.toFixed(2) + '</td>' +
                            '<td style="text-align:right;font-weight:bold;">' + totalFjf.toFixed(2) + '</td>' +
                            '<td style="text-align:right;font-weight:bold;">' + totalClfjf.toFixed(2) + '</td>' +
                            '<td style="text-align:right;font-weight:bold;">' + totalKzfjf.toFixed(2) + '</td>' +
                            '<td style="text-align:right;color:#009688;font-weight:bold;">' + totalMhzj.toFixed(2) + '</td>' +
                            '</tr>';

                        tableHtml += '</tbody></table></div></div>';

                        // 打开弹窗显示表格
                        layer.open({
                            type: 1,
                            title: '订单明细汇总 - ' + ddbh,
                            area: ['95%', '80%'],
                            content: tableHtml,
                            maxmin: true,
                            shadeClose: true,
                            btn: ['关闭'],
                            yes: function(index){
                                layer.close(index);
                            }
                        });
                    } else {
                        layer.msg('获取数据失败: ' + res.msg);
                    }
                },
                error: function() {
                    layer.msg('获取数据失败，请稍后重试');
                }
            });
        }
    });

</script>
#end

#define css()
<style>
    .layui-form-radio {
        margin: 0;
        padding: 3px;
    }

    .layui-form-radio:nth-child(1) {
        color: #FFB800;
    }

    .layui-form-radio:nth-child(2) {
        color: #1E9FFF;
    }

    .layui-form-radio:nth-child(3) {
        color: #009688;
    }

    .layui-form-radioed {
        font-weight: bold;
    }

    .layui-table td, .layui-table th {
        padding: 5px 10px;
    }

    .progress-container {
        display: flex;
        flex-wrap: wrap;
        gap: 5px;
        margin-bottom: 10px;
    }

    .progress-item {
        width: calc(16.666% - 5px);
        margin-bottom: 5px;
        position: relative;
    }

    .progress-label {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 12px;
        text-align: center;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        width: 90%;
        color: #000;
        text-shadow: -1px -1px 0 #fff,
        1px -1px 0 #fff,
        -1px 1px 0 #fff,
        1px 1px 0 #fff;
        font-weight: bold;
        pointer-events: none;
    }

    .layui-progress {
        margin-bottom: 0;
        height: 22px;
        border-radius: 11px;
        background-color: #e0e0e0;
    }

    .layui-progress-bar {
        border-radius: 11px;
    }

    .status-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
    }

    .status-item {
        width: 48%;
    }

    .remark-icon {
        cursor: pointer;
        margin-left: 5px;
        color: #ffffff; /* 默认白色 */
        text-shadow: 0 0 1px #000000; /* 添加轮廓以增加可见度 */
        transition: color 0.3s ease;
    }

    .remark-icon.has-remark {
        color: #000000; /* 有备注时变为黑色 */
    }

    .status-label {
        display: inline-block;
        width: 30px;
        text-align: center;
    }

    /* 添加以下响应式样式 */
    @media screen and (max-width: 768px) {
        .progress-item {
            width: calc(33.333% - 5px);
        }

        .status-row {
            flex-direction: column;
        }

        .status-item {
            width: 100%;
            margin-bottom: 10px;
        }
    }

    @media screen and (max-width: 480px) {
        .progress-item {
            width: calc(50% - 5px);
        }
    }

    /* 添加手风琴样式 */
    .accordion-container {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        margin-top: 20px;
    }

    .accordion-item {
        width: 49%; /* 略小于50%以留出一些间距 */
        margin-bottom: 10px;
        border: 1px solid #e6e6e6;
    }

    .accordion-header {
        padding: 10px;
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        align-items: center;
        transition: background-color 0.3s ease;
        background-color: #f2f2f2; /* 默认背景色，同时也是"未开始"的背景色 */
    }

    .accordion-header.active {
        background-color: #e6e6e6;
    }

    .accordion-content {
        display: none;
        padding: 10px;
    }

    /* 修改状态颜色 */
    .accordion-header.status-not-started {
        background-color: rgba(255, 184, 0, 0.1);
    }

    .accordion-header.status-in-progress {
        background-color: rgba(30, 159, 255, 0.1);
    }

    .accordion-header.status-completed {
        background-color: rgba(0, 150, 136, 0.1);
    }

    .accordion-header:not(.status-not-started):not(.status-in-progress):not(.status-completed) {
        background-color: #f2f2f2;
    }

    .accordion-header span {
        font-weight: bold;
    }

    /* 响应式样式调整 */
    @media screen and (max-width: 768px) {
        .progress-container {
            display: none; /* 在移动端隐藏进度条 */
        }

        .accordion-container {
            margin-top: 0;
        }
    }

    /* 修改流程名称的颜色样式 */
    .text-not-started, .accordion-header span {
        color: #333; /* 默认文字颜色，同时也是"未开始"的文字颜色 */
    }

    .text-in-progress {
        color: #1E9FFF;
    }

    .text-completed {
        color: #009688;
    }

    /* 添加以下媒体查询 */
    @media screen and (max-width: 768px) {
        .accordion-item {
            width: 100%;
        }
    }

    .layui-dropdown-menu {
        position: fixed;
        background: #fff;
        border: 1px solid #eee;
        border-radius: 2px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.12);
        padding: 5px 0;
        min-width: 120px;
        z-index: 9999;
    }

    .layui-dropdown-panel {
        max-height: 400px;
        overflow-y: auto;
        overflow-x: hidden;
    }

    .layui-dropdown-item {
        display: flex;
        align-items: center;
        padding: 8px 15px;
        color: #666;
        white-space: nowrap;
        transition: all .2s;
        text-decoration: none;
        cursor: pointer;
    }

    .layui-dropdown-item:hover {
        background-color: #f2f2f2;
        color: #009688;
    }

    .layui-dropdown-item i {
        margin-right: 8px;
        font-size: 14px;
        width: 14px;
        text-align: center;
    }

    .layui-dropdown-line {
        margin: 5px 0;
        border: none;
        border-top: 1px solid #eee;
    }

    .dropdown-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 15px;
        border-bottom: 1px solid #eee;
    }

    .dropdown-close {
        cursor: pointer;
        padding: 4px;
        font-size: 14px;
        color: #666;
        transition: color 0.2s;
    }

    .dropdown-close:hover {
        color: #333;
    }

    .layui-dropdown-menu {
        position: fixed;
        background: #fff;
        border: 1px solid #eee;
        border-radius: 2px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.12);
        padding: 0;
        min-width: 120px;
        z-index: 9999;
    }
</style>
#end
