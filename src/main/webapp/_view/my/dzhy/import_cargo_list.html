#include("/_view/common/_layout.html")
#@layout()

#define css()
<style>
    .upload-area {
        border: 2px dashed #e2e2e2;
        border-radius: 5px;
        padding: 20px;
        text-align: center;
        margin-bottom: 20px;
        cursor: pointer;
    }

    .upload-area:hover {
        border-color: #1E9FFF;
    }

    .upload-icon {
        font-size: 50px;
        color: #1E9FFF;
        margin-bottom: 10px;
    }

    .container-3d {
        width: 100%;
        height: 500px;
        border: 1px solid #e6e6e6;
        background-color: #f5f5f5;
        display: none;
    }

    .result-panel {
        display: none;
    }

    .loading-info {
        padding: 10px 0;
    }

    .items-list {
        max-height: 400px;
        overflow-y: auto;
    }

    .item-card {
        padding: 8px;
        margin-bottom: 8px;
        border-radius: 2px;
        cursor: pointer;
        transition: all 0.3s;
        display: flex;
        align-items: center;
    }

    .item-card:hover {
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }

    .item-color {
        width: 20px;
        height: 20px;
        border-radius: 3px;
        margin-right: 10px;
        border: 1px solid #ddd;
    }

    .item-number {
        display: inline-block;
        width: 24px;
        height: 24px;
        line-height: 24px;
        text-align: center;
        border-radius: 50%;
        background-color: #1E9FFF;
        color: white;
        font-weight: bold;
        margin-right: 8px;
    }

    .highlighted {
        box-shadow: 0 0 8px rgba(0,0,0,0.2);
        transform: translateY(-2px);
    }

    .item-label {
        position: absolute;
        color: #fff;
        font-size: 16px;
        font-weight: bold;
        background-color: rgba(30, 159, 255, 0.8);
        padding: 4px 8px;
        border-radius: 50%;
        pointer-events: none;
        text-align: center;
        min-width: 24px;
        min-height: 24px;
        line-height: 24px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        z-index: 1000;
        transform: translate(-50%, -50%);
    }

    .template-link {
        margin-top: 10px;
        display: block;
    }

    .cargo-edit-btn {
        margin-right: 5px;
    }

    .cargo-row-highlight {
        background-color: #f8f8f8;
    }

    .unplaced-item-card {
        background-color: #fff2f0;
        border-left: 3px solid #FF5722;
    }

    /* 控制面板容器 */
    .control-panel-container {
        position: absolute;
        top: 10px;
        right: 10px;
        z-index: 100;
    }

    /* 视图控制按钮 */
    .view-controls {
        display: flex;
        flex-direction: column;
        gap: 5px;
    }

    .control-group {
        display: flex;
        gap: 5px;
        justify-content: flex-end;
    }

    .control-btn {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        background-color: rgba(255, 255, 255, 0.9);
        border: none;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s ease;
        color: #1E9FFF;
    }

    .control-btn:hover {
        background-color: #1E9FFF;
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    }

    .control-btn:active {
        transform: translateY(0);
        box-shadow: 0 2px 3px rgba(0, 0, 0, 0.2);
    }

    .control-btn i {
        font-size: 18px;
    }

    /* 动画控制面板 */
    .animation-panel {
        margin-top: 15px;
        background-color: rgba(255, 255, 255, 0.9);
        border-radius: 8px;
        padding: 12px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        display: flex;
        flex-direction: column;
        gap: 12px;
    }

    .animate-btn {
        background-color: #1E9FFF;
        color: white;
        border: none;
        border-radius: 4px;
        padding: 8px 12px;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 5px;
    }

    .animate-btn:hover {
        background-color: #0d8aee;
        box-shadow: 0 4px 8px rgba(30, 159, 255, 0.3);
    }

    .animate-btn i {
        font-size: 16px;
    }

    /* 速度控制 */
    .speed-control {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .speed-label {
        color: #666;
        font-size: 12px;
    }

    .slider-container {
        position: relative;
        flex-grow: 1;
        height: 20px;
        display: flex;
        align-items: center;
    }

    .slider {
        -webkit-appearance: none;
        width: 100%;
        height: 4px;
        background: #e0e0e0;
        outline: none;
        border-radius: 2px;
        z-index: 2;
    }

    .slider::-webkit-slider-thumb {
        -webkit-appearance: none;
        appearance: none;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background: #1E9FFF;
        cursor: pointer;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
        transition: all 0.2s ease;
    }

    .slider::-moz-range-thumb {
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background: #1E9FFF;
        cursor: pointer;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
        transition: all 0.2s ease;
        border: none;
    }

    .slider::-webkit-slider-thumb:hover {
        background: #0d8aee;
        transform: scale(1.2);
    }

    .slider::-moz-range-thumb:hover {
        background: #0d8aee;
        transform: scale(1.2);
    }

    .slider-track {
        position: absolute;
        top: 50%;
        left: 0;
        height: 4px;
        background: #1E9FFF;
        border-radius: 2px;
        transform: translateY(-50%);
        pointer-events: none;
        z-index: 1;
    }

    /* 设置面板样式 */
    .settings-panel {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 350px;
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        z-index: 1000;
    }

    .settings-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 20px;
        border-bottom: 1px solid #eee;
    }

    .settings-header h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 500;
        color: #333;
    }

    .settings-close-btn {
        background: none;
        border: none;
        color: #999;
        cursor: pointer;
        font-size: 16px;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        transition: all 0.2s;
    }

    .settings-close-btn:hover {
        background-color: #f5f5f5;
        color: #666;
    }

    .settings-content {
        padding: 20px;
    }

    .settings-item {
        margin-bottom: 20px;
    }

    .settings-item label {
        display: block;
        margin-bottom: 8px;
        font-size: 14px;
        color: #333;
    }

    .settings-control {
        display: flex;
        align-items: center;
    }

    .size-options {
        display: flex;
        gap: 8px;
        width: 100%;
    }

    .size-option {
        flex: 1;
        padding: 8px 0;
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        background-color: white;
        color: #666;
        cursor: pointer;
        transition: all 0.2s;
        text-align: center;
        font-size: 14px;
    }

    .size-option:hover {
        border-color: #1E9FFF;
        color: #1E9FFF;
    }

    .size-option.active {
        background-color: #1E9FFF;
        border-color: #1E9FFF;
        color: white;
    }

    .zoom-value {
        margin-left: 10px;
        font-size: 14px;
        color: #666;
        min-width: 45px;
        text-align: right;
    }

    .settings-footer {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
        margin-top: 25px;
    }

    .settings-btn {
        padding: 8px 16px;
        border-radius: 4px;
        border: none;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.2s;
    }

    .settings-btn {
        background-color: #1E9FFF;
        color: white;
    }

    .settings-btn:hover {
        background-color: #0d8aee;
        box-shadow: 0 2px 5px rgba(30, 159, 255, 0.3);
    }

    .settings-btn-secondary {
        background-color: #f5f5f5;
        color: #666;
    }

    .settings-btn-secondary:hover {
        background-color: #e0e0e0;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }
</style>
#end

#define main()
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-header">导入货物列表</div>
        <div class="layui-card-body">
            <div class="layui-tab layui-tab-brief" lay-filter="cargoTab">
                <ul class="layui-tab-title">
                    <li class="layui-this">手动编辑货物</li>
                    <li>上传货物文件</li>
                </ul>
                <div class="layui-tab-content">
                    <!-- 手动编辑货物选项卡 -->
                    <div class="layui-tab-item layui-show">
                        <div class="layui-form" id="cargoForm">
                            <div class="layui-form-item">
                                <label class="layui-form-label">集装箱类型</label>
                                <div class="layui-input-inline">
                                    <select name="containerType" lay-filter="containerType">
                                        #for(containerType : containerTypes)
                                        <option value="#(containerType.type)">
                                            #(containerType.type) (#(containerType.length)×#(containerType.width)×#(containerType.height) mm)
                                        </option>
                                        #end
                                    </select>
                                </div>

                                <label class="layui-form-label">间隙(mm)</label>
                                <div class="layui-input-inline" style="width: 100px;">
                                    <input type="number" name="gap" value="50" min="0" max="500" class="layui-input">
                                </div>
                            </div>

                            <div class="layui-form-item">
                                <label class="layui-form-label">装箱算法</label>
                                <div class="layui-input-inline">
                                    <select name="algorithm" lay-filter="algorithm">
                                        #for(algorithm : algorithms)
                                        <option value="#(algorithm.value)">#(algorithm.name)</option>
                                        #end
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- 货物列表编辑区 -->
                        <div class="layui-card">
                            <div class="layui-card-header">
                                货物列表
                                <button type="button" class="layui-btn layui-btn-xs layui-btn-normal" id="addCargoBtn">
                                    <i class="layui-icon layui-icon-add-1"></i> 添加货物
                                </button>
                            </div>
                            <div class="layui-card-body">
                                <table class="layui-table" id="cargoTable">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>名称</th>
                                            <th>长(mm)</th>
                                            <th>宽(mm)</th>
                                            <th>高(mm)</th>
                                            <th>重量(kg)</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- 货物列表将通过JavaScript动态生成 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <div class="layui-form-item" style="margin-top: 20px;">
                            <div class="layui-input-block">
                                <button type="button" class="layui-btn" id="simulateManualBtn">开始模拟</button>
                                <button type="button" class="layui-btn layui-btn-primary" id="clearCargoBtn">清空货物</button>
                                <button type="button" class="layui-btn layui-btn-primary" id="exportCargoBtn">导出货物</button>
                            </div>
                        </div>
                    </div>

                    <!-- 上传货物文件选项卡 -->
                    <div class="layui-tab-item">
                        <div class="layui-form" id="uploadForm">
                            <div class="layui-form-item">
                                <label class="layui-form-label">集装箱类型</label>
                                <div class="layui-input-inline">
                                    <select name="containerType" lay-filter="containerType">
                                        #for(containerType : containerTypes)
                                        <option value="#(containerType.type)">
                                            #(containerType.type) (#(containerType.length)×#(containerType.width)×#(containerType.height) mm)
                                        </option>
                                        #end
                                    </select>
                                </div>

                                <label class="layui-form-label">间隙(mm)</label>
                                <div class="layui-input-inline" style="width: 100px;">
                                    <input type="number" name="gap" value="50" min="0" max="500" class="layui-input">
                                </div>
                            </div>

                            <div class="layui-form-item">
                                <label class="layui-form-label">装箱算法</label>
                                <div class="layui-input-inline">
                                    <select name="algorithm" lay-filter="algorithm">
                                        #for(algorithm : algorithms)
                                        <option value="#(algorithm.value)">#(algorithm.name)</option>
                                        #end
                                    </select>
                                </div>
                            </div>

                            <div class="layui-form-item">
                                <div class="layui-input-block">
                                    <div class="upload-area" id="uploadArea">
                                        <div class="upload-icon"><i class="layui-icon layui-icon-upload"></i></div>
                                        <div>点击或拖拽文件到此处上传</div>
                                        <div class="layui-text" style="color: #999; font-size: 12px;">支持 CSV 格式，文件大小不超过 5MB</div>
                                        <a href="javascript:;" class="template-link" id="downloadTemplate">下载模板</a>
                                    </div>
                                    <input type="file" id="fileInput" name="file" style="display: none;" accept=".csv">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="result-panel" id="resultPanel">
                <div class="layui-row">
                    <div class="layui-col-md8">
                        <!-- 3D 容器 -->
                        <div id="container3d" class="container-3d"></div>

                        <!-- 控制面板容器 -->
                        <div class="control-panel-container">
                            <!-- 视图控制按钮 -->
                            <div class="view-controls">
                                <div class="control-group">
                                    <button type="button" class="control-btn" id="zoomInBtn" title="放大">
                                        <i class="layui-icon layui-icon-add-1"></i>
                                    </button>
                                    <button type="button" class="control-btn" id="zoomOutBtn" title="缩小">
                                        <i class="layui-icon layui-icon-reduce-circle"></i>
                                    </button>
                                </div>
                                <div class="control-group">
                                    <button type="button" class="control-btn" id="rotateLeftBtn" title="左旋">
                                        <i class="layui-icon layui-icon-left"></i>
                                    </button>
                                    <button type="button" class="control-btn" id="rotateRightBtn" title="右旋">
                                        <i class="layui-icon layui-icon-right"></i>
                                    </button>
                                </div>
                                <div class="control-group">
                                    <button type="button" class="control-btn" id="resetViewBtn" title="重置视图">
                                        <i class="layui-icon layui-icon-refresh"></i>
                                    </button>
                                    <button type="button" class="control-btn" id="settingsBtn" title="设置">
                                        <i class="layui-icon layui-icon-set"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 动画控制面板 -->
                        <div class="animation-panel">
                            <button type="button" class="animate-btn" id="animateBtn">
                                <i class="layui-icon layui-icon-play"></i> 模拟装柜动画
                            </button>
                            <div class="speed-control">
                                <span class="speed-label">慢</span>
                                <div class="slider-container">
                                    <input type="range" id="animationSpeed" min="1" max="10" value="5" class="slider">
                                    <div class="slider-track"></div>
                                </div>
                                <span class="speed-label">快</span>
                            </div>
                        </div>

                        <div class="layui-btn-group" style="margin-top: 10px;">
                            <button type="button" class="layui-btn layui-btn-sm layui-btn-normal" id="toggleWireframeBtn">切换线框模式</button>
                            <button type="button" class="layui-btn layui-btn-sm layui-btn-primary" id="printBtn">打印视图</button>
                        </div>

                        <!-- 设置面板 -->
                        <div class="settings-panel" id="settingsPanel" style="display: none;">
                            <div class="settings-header">
                                <h3>显示设置</h3>
                                <button type="button" class="settings-close-btn" id="closeSettingsBtn">
                                    <i class="layui-icon layui-icon-close"></i>
                                </button>
                            </div>
                            <div class="settings-content">
                                <div class="settings-item">
                                    <label>初始图画大小</label>
                                    <div class="settings-control">
                                        <div class="size-options">
                                            <button type="button" class="size-option" data-size="small">小</button>
                                            <button type="button" class="size-option" data-size="medium">中</button>
                                            <button type="button" class="size-option active" data-size="large">大</button>
                                            <button type="button" class="size-option" data-size="xlarge">超大</button>
                                        </div>
                                    </div>
                                </div>
                                <div class="settings-item">
                                    <label>自定义缩放比例</label>
                                    <div class="settings-control">
                                        <div class="slider-container">
                                            <input type="range" id="zoomRatio" min="0.5" max="2.5" step="0.1" value="1.0" class="slider">
                                            <div class="slider-track"></div>
                                        </div>
                                        <span class="zoom-value">100%</span>
                                    </div>
                                </div>
                                <div class="settings-footer">
                                    <button type="button" class="settings-btn" id="applySettingsBtn">应用</button>
                                    <button type="button" class="settings-btn settings-btn-secondary" id="resetSettingsBtn">重置</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md4">
                        <!-- 装载信息 -->
                        <div class="layui-card">
                            <div class="layui-card-header">装载信息</div>
                            <div class="layui-card-body">
                                <div id="loadingInfo" class="loading-info"></div>
                            </div>
                        </div>

                        <!-- 货物列表 -->
                        <div class="layui-card" style="margin-top: 15px;">
                            <div class="layui-card-header">货物列表</div>
                            <div class="layui-card-body">
                                <div id="itemsList" class="items-list"></div>
                            </div>
                        </div>

                        <!-- 货物列表区域 -->
                        <div class="layui-row" style="margin-top: 15px;">
                            <!-- 已装载货物列表 -->
                            <div class="layui-col-md6">
                                <div class="layui-card">
                                    <div class="layui-card-header">已装载货物</div>
                                    <div class="layui-card-body">
                                        <div id="placedItemsList" class="items-list"></div>
                                    </div>
                                </div>
                            </div>

                            <!-- 未装载货物列表 -->
                            <div class="layui-col-md6" id="unplacedItemsPanel" style="display: none;">
                                <div class="layui-card">
                                    <div class="layui-card-header">未装载货物</div>
                                    <div class="layui-card-body">
                                        <div id="unplacedItemsList" class="items-list"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
#end

#define js()
<!-- 引入Three.js库 -->
<script src="https://cdn.jsdelivr.net/npm/three@0.132.2/build/three.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/three@0.132.2/examples/js/controls/OrbitControls.js"></script>
<!-- 引入TWEEN.js库，用于动画效果 -->
<script src="https://cdn.jsdelivr.net/npm/@tweenjs/tween.js@18.6.4/dist/tween.umd.js"></script>

<script>
    layui.use(['form', 'layer', 'upload', 'element'], function() {
        var form = layui.form;
        var layer = layui.layer;
        var upload = layui.upload;
        var element = layui.element;
        var $ = layui.jquery;

        // 货物列表数据
        var cargoItems = [];
        var nextCargoId = 1;

        // 全局变量
        var isAnimating = false;
        var animationInterval = null;
        var originalItemMeshes = [];
        var animationQueue = [];
        var currentAnimationIndex = 0;
        var simulationData = null;

        // 上传区域点击事件
        $('#uploadArea').on('click', function() {
            $('#fileInput').click();
        });

        // 文件选择事件
        $('#fileInput').on('change', function() {
            var file = this.files[0];
            if (file) {
                uploadFile(file);
            }
        });

        // 添加货物按钮点击事件
        $('#addCargoBtn').on('click', function() {
            showCargoEditDialog();
        });

        // 清空货物按钮点击事件
        $('#clearCargoBtn').on('click', function() {
            layer.confirm('确定要清空所有货物吗？', function(index) {
                cargoItems = [];
                renderCargoTable();
                layer.close(index);
            });
        });

        // 导出货物按钮点击事件
        $('#exportCargoBtn').on('click', function() {
            if (cargoItems.length === 0) {
                layer.msg('没有货物可导出', {icon: 2});
                return;
            }

            // 创建 CSV 数据
            var csvContent = "ID,Name,Length,Width,Height,Weight\n";
            cargoItems.forEach(function(item) {
                csvContent += item.id + "," +
                             item.name + "," +
                             item.length + "," +
                             item.width + "," +
                             item.height + "," +
                             item.weight + "\n";
            });

            // 创建下载链接
            var blob = new Blob([csvContent], { type: 'text/csv' });
            var url = URL.createObjectURL(blob);
            var a = document.createElement('a');
            a.href = url;
            a.download = 'cargo_list_export.csv';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        });

        // 手动模拟按钮点击事件
        $('#simulateManualBtn').on('click', function() {
            if (cargoItems.length === 0) {
                layer.msg('请先添加货物', {icon: 2});
                return;
            }

            simulateWithCargoItems();
        });

        // 下载模板
        $('#downloadTemplate').on('click', function(e) {
            e.stopPropagation();

            // 创建模板内容
            var templateContent = "ID,Name,Length,Width,Height,Weight\n" +
                                 "1,Box 1,1000,800,600,50\n" +
                                 "2,Box 2,1200,1000,800,80\n" +
                                 "3,Box 3,900,700,500,40\n";

            // 创建下载链接
            var blob = new Blob([templateContent], { type: 'text/csv' });
            var url = URL.createObjectURL(blob);
            var a = document.createElement('a');
            a.href = url;
            a.download = 'cargo_list_template.csv';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        });

        // 显示货物编辑对话框
        function showCargoEditDialog(cargoItem) {
            var isEdit = !!cargoItem;
            var title = isEdit ? '编辑货物' : '添加货物';

            layer.open({
                type: 1,
                title: title,
                area: ['500px', '400px'],
                content: '<div class="layui-form" style="padding: 20px;">' +
                         '<div class="layui-form-item">' +
                         '<label class="layui-form-label">名称</label>' +
                         '<div class="layui-input-block">' +
                         '<input type="text" id="cargoName" class="layui-input" value="' + (isEdit ? cargoItem.name : '') + '">' +
                         '</div>' +
                         '</div>' +
                         '<div class="layui-form-item">' +
                         '<label class="layui-form-label">长(mm)</label>' +
                         '<div class="layui-input-block">' +
                         '<input type="number" id="cargoLength" class="layui-input" value="' + (isEdit ? cargoItem.length : '1000') + '">' +
                         '</div>' +
                         '</div>' +
                         '<div class="layui-form-item">' +
                         '<label class="layui-form-label">宽(mm)</label>' +
                         '<div class="layui-input-block">' +
                         '<input type="number" id="cargoWidth" class="layui-input" value="' + (isEdit ? cargoItem.width : '800') + '">' +
                         '</div>' +
                         '</div>' +
                         '<div class="layui-form-item">' +
                         '<label class="layui-form-label">高(mm)</label>' +
                         '<div class="layui-input-block">' +
                         '<input type="number" id="cargoHeight" class="layui-input" value="' + (isEdit ? cargoItem.height : '600') + '">' +
                         '</div>' +
                         '</div>' +
                         '<div class="layui-form-item">' +
                         '<label class="layui-form-label">重量(kg)</label>' +
                         '<div class="layui-input-block">' +
                         '<input type="number" id="cargoWeight" class="layui-input" value="' + (isEdit ? cargoItem.weight : '50') + '">' +
                         '</div>' +
                         '</div>' +
                         '</div>',
                btn: ['确定', '取消'],
                yes: function(index) {
                    // 获取表单数据
                    var name = $('#cargoName').val() || '货物-' + nextCargoId;
                    var length = parseFloat($('#cargoLength').val());
                    var width = parseFloat($('#cargoWidth').val());
                    var height = parseFloat($('#cargoHeight').val());
                    var weight = parseFloat($('#cargoWeight').val()) || 0;

                    // 验证数据
                    if (isNaN(length) || length <= 0 || isNaN(width) || width <= 0 || isNaN(height) || height <= 0) {
                        layer.msg('请输入有效的尺寸', {icon: 2});
                        return;
                    }

                    if (isEdit) {
                        // 编辑现有货物
                        cargoItem.name = name;
                        cargoItem.length = length;
                        cargoItem.width = width;
                        cargoItem.height = height;
                        cargoItem.weight = weight;
                    } else {
                        // 添加新货物
                        cargoItems.push({
                            id: nextCargoId++,
                            name: name,
                            length: length,
                            width: width,
                            height: height,
                            weight: weight
                        });
                    }

                    // 重新渲染货物表
                    renderCargoTable();

                    layer.close(index);
                }
            });
        }

        // 渲染货物表
        function renderCargoTable() {
            var html = '';

            if (cargoItems.length === 0) {
                html = '<tr><td colspan="7" style="text-align: center;">暂无货物，请添加</td></tr>';
            } else {
                cargoItems.forEach(function(item) {
                    html += '<tr>';
                    html += '<td>' + item.id + '</td>';
                    html += '<td>' + item.name + '</td>';
                    html += '<td>' + item.length + '</td>';
                    html += '<td>' + item.width + '</td>';
                    html += '<td>' + item.height + '</td>';
                    html += '<td>' + item.weight + '</td>';
                    html += '<td>';
                    html += '<button type="button" class="layui-btn layui-btn-xs cargo-edit-btn" data-id="' + item.id + '">';
                    html += '<i class="layui-icon layui-icon-edit"></i> 编辑';
                    html += '</button>';
                    html += '<button type="button" class="layui-btn layui-btn-xs layui-btn-danger cargo-delete-btn" data-id="' + item.id + '">';
                    html += '<i class="layui-icon layui-icon-delete"></i> 删除';
                    html += '</button>';
                    html += '</td>';
                    html += '</tr>';
                });
            }

            $('#cargoTable tbody').html(html);

            // 绑定编辑按钮事件
            $('.cargo-edit-btn').on('click', function() {
                var id = $(this).data('id');
                var cargoItem = cargoItems.find(function(item) {
                    return item.id == id;
                });

                if (cargoItem) {
                    showCargoEditDialog(cargoItem);
                }
            });

            // 绑定删除按钮事件
            $('.cargo-delete-btn').on('click', function() {
                var id = $(this).data('id');

                layer.confirm('确定要删除该货物吗？', function(index) {
                    cargoItems = cargoItems.filter(function(item) {
                        return item.id != id;
                    });

                    renderCargoTable();
                    layer.close(index);
                });
            });

            // 绑定行高亮事件
            $('#cargoTable tbody tr').hover(
                function() {
                    $(this).addClass('cargo-row-highlight');
                },
                function() {
                    $(this).removeClass('cargo-row-highlight');
                }
            );
        }

        // 初始化货物表
        renderCargoTable();

        // 使用手动编辑的货物列表模拟装柜
        function simulateWithCargoItems() {
            var containerType = $('#cargoForm select[name=containerType]').val();
            var gap = parseFloat($('#cargoForm input[name=gap]').val());
            var algorithm = $('#cargoForm select[name=algorithm]').val();

            // 创建请求数据
            var requestData = {
                containerType: containerType,
                gap: gap,
                algorithm: algorithm,
                cargoItems: cargoItems
            };

            layer.load(2);
            $.ajax({
                url: '/my/dzhy/simulateWithCargoItems',
                type: 'post',
                contentType: 'application/json',
                data: JSON.stringify(requestData),
                dataType: 'json',
                success: function(res) {
                    layer.closeAll('loading');
                    if (res.state === 'ok') {
                        // 显示结果
                        showResult(res.data);
                    } else {
                        layer.msg(res.msg || '模拟失败', {icon: 2});
                    }
                },
                error: function() {
                    layer.closeAll('loading');
                    layer.msg('请求失败，请稍后重试', {icon: 2});
                }
            });
        }

        // 上传文件
        function uploadFile(file) {
            if (file.size > 5 * 1024 * 1024) {
                layer.msg('文件大小不能超过5MB', {icon: 2});
                return;
            }

            var formData = new FormData();
            formData.append('file', file);
            formData.append('containerType', $('select[name=containerType]').val());
            formData.append('gap', $('input[name=gap]').val());
            formData.append('algorithm', $('select[name=algorithm]').val());

            layer.load(2);
            $.ajax({
                url: '/my/dzhy/importCargoList',
                type: 'post',
                data: formData,
                processData: false,
                contentType: false,
                dataType: 'json',
                success: function(res) {
                    layer.closeAll('loading');
                    if (res.state === 'ok') {
                        // 显示结果
                        showResult(res.data);
                    } else {
                        layer.msg(res.msg || '导入失败', {icon: 2});
                    }
                },
                error: function() {
                    layer.closeAll('loading');
                    layer.msg('请求失败，请稍后重试', {icon: 2});
                }
            });
        }

        // 显示结果
        function showResult(data) {
            $('#resultPanel').show();
            $('#container3d').show();

            // 初始化3D场景
            initScene();

            // 渲染集装箱和货物
            renderContainer(data);

            // 显示装载信息
            showLoadingInfo(data);

            // 显示已装载货物列表
            showPlacedItems(data.items);

            // 显示未装载货物列表
            showUnplacedItems(data.unplacedItems);
        }

        /**
         * 显示已装载货物列表
         */
        function showPlacedItems(placedItems) {
            if (!placedItems || placedItems.length === 0) {
                return;
            }

            var html = '';

            // 按名称排序
            var sortedItems = placedItems.slice().sort(function(a, b) {
                // 如果name是数字形式，则按数字大小排序
                var aNum = parseInt(a.name);
                var bNum = parseInt(b.name);

                if (!isNaN(aNum) && !isNaN(bNum)) {
                    return aNum - bNum;
                }

                // 否则按字符串排序
                return a.name.localeCompare(b.name);
            });

            sortedItems.forEach(function(item, index) {
                html += '<div class="item-card" data-id="' + item.id + '">';
                html += '<div class="item-color" style="background-color:' + item.color + '"></div>';
                html += '<div class="item-info">';
                html += '<div><b>' + item.name + '</b></div>';
                html += '<div>尺寸: ' + item.length + ' × ' + item.width + ' × ' + item.height + ' mm</div>';
                html += '<div>位置: (' + item.x + ', ' + item.y + ', ' + item.z + ')</div>';
                html += '</div>';
                html += '</div>';
            });

            $('#placedItemsList').html(html);

            // 添加点击事件
            $('#placedItemsList .item-card').on('click', function() {
                var id = $(this).data('id');
                highlightItem(id);
            });
        }

        /**
         * 显示未装载货物列表
         */
        function showUnplacedItems(unplacedItems) {
            if (!unplacedItems || unplacedItems.length === 0) {
                $('#unplacedItemsPanel').hide();
                return;
            }

            $('#unplacedItemsPanel').show();

            var html = '';
            unplacedItems.forEach(function(item, index) {
                html += '<div class="item-card unplaced-item-card">';
                html += '<div class="item-color" style="background-color:' + item.color + '"></div>';
                html += '<div class="item-info">';
                html += '<div><b>' + item.name + '</b></div>';
                html += '<div>尺寸: ' + item.length + ' × ' + item.width + ' × ' + item.height + ' mm</div>';
                html += '</div>';
                html += '</div>';
            });

            $('#unplacedItemsList').html(html);
        }

        // 初始化3D场景
        var scene, camera, renderer, controls;
        var container = document.getElementById('container3d');
        var itemMeshes = {}; // 存储货物网格对象
        var itemLabels = []; // 存储标签
        var wireframeMode = false;

        function initScene() {
            // 创建场景
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0xf5f5f5);

            // 创建相机
            camera = new THREE.PerspectiveCamera(60, container.clientWidth / container.clientHeight, 1, 50000);
            camera.position.set(10000, 10000, 10000);

            // 创建渲染器
            renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(container.clientWidth, container.clientHeight);
            renderer.shadowMap.enabled = true;
            container.innerHTML = '';
            container.appendChild(renderer.domElement);

            // 添加控制器
            controls = new THREE.OrbitControls(camera, renderer.domElement);
            controls.enableDamping = true;
            controls.dampingFactor = 0.25;
            controls.screenSpacePanning = false;
            controls.maxPolarAngle = Math.PI / 1.5;

            // 添加环境光
            var ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
            scene.add(ambientLight);

            // 添加方向光
            var directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(5000, 10000, 7500);
            directionalLight.castShadow = true;
            scene.add(directionalLight);

            // 添加坐标轴辅助
            var axesHelper = new THREE.AxesHelper(1000);
            scene.add(axesHelper);

            // 渲染循环
            function animate() {
                requestAnimationFrame(animate);
                controls.update();
                renderer.render(scene, camera);
            }

            animate();

            // 窗口大小变化时调整渲染器大小
            window.addEventListener('resize', function() {
                camera.aspect = container.clientWidth / container.clientHeight;
                camera.updateProjectionMatrix();
                renderer.setSize(container.clientWidth, container.clientHeight);
            });
        }

        // 清除场景中的所有物体
        function clearScene() {
            while(scene.children.length > 0) {
                scene.remove(scene.children[0]);
            }

            // 重新添加光源和坐标轴
            var ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
            scene.add(ambientLight);

            var directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(5000, 10000, 7500);
            directionalLight.castShadow = true;
            scene.add(directionalLight);

            var axesHelper = new THREE.AxesHelper(1000);
            scene.add(axesHelper);

            // 清空货物网格对象
            itemMeshes = {};

            // 移除标签
            removeLabels();
        }

        // 移除所有标签
        function removeLabels() {
            itemLabels.forEach(function(label) {
                if (label.element && label.element.parentNode) {
                    label.element.parentNode.removeChild(label.element);
                }
            });
            itemLabels = [];
        }

        // 渲染集装箱和货物
        function renderContainer(data) {
            clearScene();

            // 保存模拟数据，供动画使用
            simulationData = data;

            var container = data.container;
            var items = data.items;

            // 创建集装箱
            var containerGeometry = new THREE.BoxGeometry(container.length, container.height, container.width);
            var containerMaterial = new THREE.MeshBasicMaterial({
                color: 0xffffff,
                transparent: true,
                opacity: 0.1,
                side: THREE.BackSide
            });
            var containerMesh = new THREE.Mesh(containerGeometry, containerMaterial);

            // 创建集装箱线框
            var containerEdges = new THREE.EdgesGeometry(containerGeometry);
            var containerLine = new THREE.LineSegments(
                containerEdges,
                new THREE.LineBasicMaterial({ color: 0x000000 })
            );

            // 调整集装箱位置，使其中心在原点
            containerMesh.position.set(container.length / 2, container.height / 2, container.width / 2);
            containerLine.position.set(container.length / 2, container.height / 2, container.width / 2);

            scene.add(containerMesh);
            scene.add(containerLine);

            // 创建地面网格
            var groundGeometry = new THREE.PlaneGeometry(container.length, container.width);
            var groundMaterial = new THREE.MeshBasicMaterial({
                color: 0xcccccc,
                side: THREE.DoubleSide,
                transparent: true,
                opacity: 0.5
            });
            var ground = new THREE.Mesh(groundGeometry, groundMaterial);
            ground.rotation.x = Math.PI / 2;
            ground.position.set(container.length / 2, 0, container.width / 2);
            scene.add(ground);

            // 创建货物
            items.forEach(function(item, index) {
                var itemGeometry = new THREE.BoxGeometry(item.length, item.height, item.width);
                var itemMaterial = new THREE.MeshLambertMaterial({
                    color: item.color,
                    transparent: true,
                    opacity: 0.8,
                    wireframe: wireframeMode
                });
                var itemMesh = new THREE.Mesh(itemGeometry, itemMaterial);

                // 调整货物位置
                var posX = item.x + item.length / 2;
                var posY = item.z + item.height / 2;
                var posZ = item.y + item.width / 2;

                itemMesh.position.set(posX, posY, posZ);

                // 添加边框
                var itemEdges = new THREE.EdgesGeometry(itemGeometry);
                var itemLine = new THREE.LineSegments(
                    itemEdges,
                    new THREE.LineBasicMaterial({ color: 0x000000 })
                );
                itemMesh.add(itemLine);

                // 存储货物网格对象
                itemMeshes[item.id] = {
                    mesh: itemMesh,
                    originalColor: item.color,
                    item: item,
                    index: index + 1
                };

                scene.add(itemMesh);

                // 创建序号标签
                createLabel(index + 1, posX, posY, posZ, item.length, item.height, item.width, item.id);
            });

            // 调整相机位置
            var maxDim = Math.max(container.length, container.width, container.height);
            var distance = maxDim * (1.5 / currentZoomRatio);
            camera.position.set(distance, distance, distance);
            controls.target.set(container.length / 2, container.height / 2, container.width / 2);
            controls.update();
        }

        // 创建3D文本标签
        function createLabel(number, x, y, z, length, height, width, itemId) {
            // 创建 HTML 元素作为标签
            var labelDiv = document.createElement('div');
            labelDiv.className = 'item-label';
            labelDiv.textContent = number;
            labelDiv.style.display = 'none'; // 初始隐藏，等渲染器更新后显示
            document.body.appendChild(labelDiv);

            // 计算标签应该显示的位置（货物正面中心）
            var labelPosition = new THREE.Vector3(
                x + length * 0.5, // 中心
                y + height * 0.5, // 中心
                z + width * 0.01  // 稍微偏移，使标签显示在货物表面
            );

            // 存储标签信息
            itemLabels.push({
                element: labelDiv,
                position: labelPosition,
                dimensions: { length: length, height: height, width: width },
                itemId: itemId // 存储货物ID，便于后续更新标签内容
            });

            // 添加渲染器更新后的回调，更新标签位置
            if (itemLabels.length === 1) { // 只需要添加一次事件监听
                renderer.domElement.addEventListener('mouseup', updateLabelsPosition);
                renderer.domElement.addEventListener('mousemove', updateLabelsPosition);
                window.addEventListener('resize', updateLabelsPosition);
            }

            // 初始更新标签位置
            setTimeout(updateLabelsPosition, 100);
        }

        // 更新标签位置
        function updateLabelsPosition() {
            itemLabels.forEach(function(label) {
                // 将 3D 位置转换为屏幕坐标
                var position = label.position.clone();
                position.project(camera);

                // 转换为像素坐标
                var x = (position.x * 0.5 + 0.5) * renderer.domElement.clientWidth;
                var y = (-position.y * 0.5 + 0.5) * renderer.domElement.clientHeight;

                // 检查是否在视野内
                if (position.z < 1 && x >= 0 && x <= renderer.domElement.clientWidth &&
                    y >= 0 && y <= renderer.domElement.clientHeight) {
                    label.element.style.display = 'block';
                    label.element.style.left = x + 'px';
                    label.element.style.top = y + 'px';
                } else {
                    label.element.style.display = 'none';
                }
            });
        }

        // 显示装载信息
        function showLoadingInfo(data) {
            var html = '';

            // 添加装载结果消息
            html += '<div class="info-item layui-bg-' + (data.canFit ? 'green' : 'orange') + '" style="padding: 10px;">';
            html += data.message;
            html += '</div>';

            // 添加算法信息
            if (data.algorithm) {
                html += '<div class="info-item"><b>使用算法:</b> ' + data.algorithm + '</div>';
            }

            // 添加集装箱信息
            html += '<div class="info-item"><b>集装箱尺寸:</b> ' +
                   data.container.length + ' × ' +
                   data.container.width + ' × ' +
                   data.container.height + ' mm</div>';

            // 添加货物数量
            html += '<div class="info-item"><b>货物数量:</b> ' + data.items.length + '</div>';

            // 添加空间利用率
            html += '<div class="info-item"><b>空间利用率:</b> ' + data.utilization.toFixed(2) + '%</div>';

            $('#loadingInfo').html(html);
        }

        // 显示货物列表
        function showItemsList(items) {
            var html = '';

            // 创建一个新数组并按name排序
            var sortedItems = items.slice().sort(function(a, b) {
                // 如果name是数字形式，则按数字大小排序
                var aNum = parseInt(a.name);
                var bNum = parseInt(b.name);

                if (!isNaN(aNum) && !isNaN(bNum)) {
                    return aNum - bNum;
                }

                // 否则按字符串排序
                return a.name.localeCompare(b.name);
            });

            // 创建一个映射，存储原始索引和排序后的索引关系
            var indexMap = {};
            sortedItems.forEach(function(item, index) {
                indexMap[item.id] = index + 1;
            });

            sortedItems.forEach(function(item, index) {
                var displayIndex = indexMap[item.id];
                html += '<div id="item-' + item.id + '" class="item-card" data-id="' + item.id + '">';
                html += '<div class="item-number">' + displayIndex + '</div>';
                html += '<div class="item-color" style="background-color:' + item.color + '"></div>';
                html += '<div class="item-info">';
                html += '<div><b>' + item.name + '</b></div>';
                html += '<div>尺寸: ' + item.length + ' × ' + item.width + ' × ' + item.height + ' mm</div>';
                html += '</div>';
                html += '</div>';
            });

            $('#itemsList').html(html);

            // 添加点击事件
            $('.item-card').on('click', function() {
                var id = $(this).data('id');
                highlightItem(id);
            });

            // 更新标签索引
            updateLabelIndices(indexMap);
        }

        // 更新标签索引
        function updateLabelIndices(indexMap) {
            // 更新每个标签的文本
            itemLabels.forEach(function(label) {
                if (label.itemId && indexMap[label.itemId]) {
                    label.element.textContent = indexMap[label.itemId];
                }
            });
        }

        // 高亮显示货物
        function highlightItem(id) {
            // 重置所有货物颜色
            Object.values(itemMeshes).forEach(function(item) {
                item.mesh.material.color.set(item.originalColor);
                item.mesh.material.opacity = 0.8;
            });

            // 高亮选中的货物
            if (id && itemMeshes[id]) {
                itemMeshes[id].mesh.material.color.set(0xffff00); // 黄色高亮
                itemMeshes[id].mesh.material.opacity = 1.0;

                // 移除其他货物的高亮样式
                $('.item-card').removeClass('highlighted');
                // 添加当前货物的高亮样式
                $('#item-' + id).addClass('highlighted');
            } else {
                // 移除所有货物的高亮样式
                $('.item-card').removeClass('highlighted');
            }
        }

        // 重置视图按钮点击事件
        $('#resetViewBtn').on('click', function() {
            if (scene && camera && controls) {
                var containerData = scene.userData.containerData;
                if (containerData) {
                    var maxDim = Math.max(containerData.length, containerData.width, containerData.height);
                    camera.position.set(maxDim * 1.5, maxDim * 1.5, maxDim * 1.5);
                    controls.target.set(containerData.length / 2, containerData.height / 2, containerData.width / 2);
                    controls.update();
                }

                // 重置所有货物高亮
                highlightItem(null);
            }
        });

        // 切换线框模式按钮点击事件
        $('#toggleWireframeBtn').on('click', function() {
            wireframeMode = !wireframeMode;

            Object.values(itemMeshes).forEach(function(item) {
                item.mesh.material.wireframe = wireframeMode;
            });
        });

        // 打印视图按钮点击事件
        $('#printBtn').on('click', function() {
            // 创建打印样式
            var printCSS = '<style>' +
                '@media print {' +
                '  body * { visibility: hidden; }' +
                '  #container3d, #container3d * { visibility: visible; }' +
                '  #container3d { position: absolute; left: 0; top: 0; width: 100%; height: 100%; }' +
                '}' +
                '</style>';

            // 创建打印框架
            var printFrame = $('<iframe name="printFrame" style="display:none;"></iframe>');
            $('body').append(printFrame);

            var frameDoc = printFrame[0].contentWindow.document;
            frameDoc.write('<html><head>' + printCSS + '</head><body>');

            // 捕获当前视图
            var dataURL = renderer.domElement.toDataURL('image/png');
            var img = new Image();
            img.src = dataURL;

            // 添加标题和信息
            frameDoc.write('<h2>集装箱装载模拟</h2>');
            frameDoc.write('<div>' + $('#loadingInfo').html() + '</div>');
            frameDoc.write('<div style="margin-top:20px;"><img src="' + dataURL + '" style="max-width:100%;"></div>');

            frameDoc.write('</body></html>');
            frameDoc.close();

            // 打印
            printFrame[0].contentWindow.focus();
            printFrame[0].contentWindow.print();

            // 移除打印框架
            setTimeout(function() {
                printFrame.remove();
            }, 1000);
        });

        // 视图控制按钮事件

        // 放大按钮
        $('#zoomInBtn').on('click', function() {
            camera.position.multiplyScalar(0.9); // 将相机向前移动
            controls.update();
        });

        // 缩小按钮
        $('#zoomOutBtn').on('click', function() {
            camera.position.multiplyScalar(1.1); // 将相机向后移动
            controls.update();
        });

        // 左旋按钮
        $('#rotateLeftBtn').on('click', function() {
            // 围绕y轴旋转相机位置
            var rotationAngle = Math.PI / 12; // 15度
            var x = camera.position.x;
            var z = camera.position.z;
            camera.position.x = x * Math.cos(rotationAngle) - z * Math.sin(rotationAngle);
            camera.position.z = x * Math.sin(rotationAngle) + z * Math.cos(rotationAngle);
            controls.update();
        });

        // 右旋按钮
        $('#rotateRightBtn').on('click', function() {
            // 围绕y轴旋转相机位置
            var rotationAngle = -Math.PI / 12; // -15度
            var x = camera.position.x;
            var z = camera.position.z;
            camera.position.x = x * Math.cos(rotationAngle) - z * Math.sin(rotationAngle);
            camera.position.z = x * Math.sin(rotationAngle) + z * Math.cos(rotationAngle);
            controls.update();
        });

        // 重置视图按钮
        $('#resetViewBtn').on('click', function() {
            if (simulationData) {
                var container = simulationData.container;
                var maxDim = Math.max(container.length, container.width, container.height);
                var distance = maxDim * (1.5 / currentZoomRatio);
                camera.position.set(distance, distance, distance);
                controls.target.set(container.length / 2, container.height / 2, container.width / 2);
                controls.update();

                // 重置所有货物高亮
                highlightItem(null);
            }
        });

        // 更新滑动条的视觉效果
        function updateSliderTrack() {
            var slider = $('#animationSpeed');
            var value = slider.val();
            var min = slider.attr('min');
            var max = slider.attr('max');
            var percentage = ((value - min) / (max - min)) * 100;
            $('.slider-track').css('width', percentage + '%');
        }

        // 初始化滑动条
        updateSliderTrack();

        // 监听滑动条变化
        $('#animationSpeed').on('input', function() {
            updateSliderTrack();
        });

        // 设置面板相关代码
        var currentZoomRatio = 1.0;
        var currentSizeOption = 'large';
        var zoomFactors = {
            'small': 0.7,
            'medium': 0.9,
            'large': 1.2,
            'xlarge': 1.5
        };

        // 打开设置面板
        $('#settingsBtn').on('click', function() {
            $('#settingsPanel').fadeIn(200);
        });

        // 关闭设置面板
        $('#closeSettingsBtn').on('click', function() {
            $('#settingsPanel').fadeOut(200);
        });

        // 尺寸选项点击
        $('.size-option').on('click', function() {
            $('.size-option').removeClass('active');
            $(this).addClass('active');
            currentSizeOption = $(this).data('size');

            // 更新缩放比例滑动条
            $('#zoomRatio').val(zoomFactors[currentSizeOption]);
            updateZoomSliderTrack();
            updateZoomValueDisplay();
        });

        // 更新缩放比例滑动条
        function updateZoomSliderTrack() {
            var slider = $('#zoomRatio');
            var value = slider.val();
            var min = slider.attr('min');
            var max = slider.attr('max');
            var percentage = ((value - min) / (max - min)) * 100;
            $('#zoomRatio').siblings('.slider-track').css('width', percentage + '%');
        }

        // 更新缩放比例显示
        function updateZoomValueDisplay() {
            var value = $('#zoomRatio').val();
            $('.zoom-value').text(Math.round(value * 100) + '%');
        }

        // 监听缩放比例滑动条变化
        $('#zoomRatio').on('input', function() {
            updateZoomSliderTrack();
            updateZoomValueDisplay();

            // 根据滑动条值自动选择最接近的尺寸选项
            var value = parseFloat($(this).val());
            var closestSize = 'medium';
            var minDiff = Number.MAX_VALUE;

            for (var size in zoomFactors) {
                var diff = Math.abs(value - zoomFactors[size]);
                if (diff < minDiff) {
                    minDiff = diff;
                    closestSize = size;
                }
            }

            if (minDiff < 0.05) { // 如果非常接近某个预设值，自动选中对应选项
                $('.size-option').removeClass('active');
                $('.size-option[data-size="' + closestSize + '"]').addClass('active');
                currentSizeOption = closestSize;
            } else {
                $('.size-option').removeClass('active');
            }
        });

        // 应用设置
        $('#applySettingsBtn').on('click', function() {
            var newZoomRatio = parseFloat($('#zoomRatio').val());
            currentZoomRatio = newZoomRatio;

            // 保存设置到本地存储
            localStorage.setItem('containerViewZoomRatio', newZoomRatio);
            localStorage.setItem('containerViewSizeOption', currentSizeOption);

            // 如果有模拟数据，重新渲染并应用新的缩放比例
            if (simulationData) {
                applyZoomRatio();
            }

            $('#settingsPanel').fadeOut(200);
            layer.msg('设置已应用', {icon: 1});
        });

        // 重置设置
        $('#resetSettingsBtn').on('click', function() {
            // 重置为默认值
            currentZoomRatio = 1.0;
            currentSizeOption = 'large';

            // 更新UI
            $('.size-option').removeClass('active');
            $('.size-option[data-size="large"]').addClass('active');
            $('#zoomRatio').val(1.0);
            updateZoomSliderTrack();
            updateZoomValueDisplay();

            // 清除本地存储
            localStorage.removeItem('containerViewZoomRatio');
            localStorage.removeItem('containerViewSizeOption');
        });

        // 应用缩放比例
        function applyZoomRatio() {
            if (!simulationData) return;

            var container = simulationData.container;
            var maxDim = Math.max(container.length, container.width, container.height);

            // 调整相机位置以应用缩放比例
            var distance = maxDim * (1.5 / currentZoomRatio);
            var direction = camera.position.clone().normalize();
            camera.position.copy(direction.multiplyScalar(distance));

            controls.update();
        }

        // 加载保存的设置
        function loadSavedSettings() {
            var savedZoomRatio = localStorage.getItem('containerViewZoomRatio');
            var savedSizeOption = localStorage.getItem('containerViewSizeOption');

            if (savedZoomRatio) {
                currentZoomRatio = parseFloat(savedZoomRatio);
                $('#zoomRatio').val(currentZoomRatio);
                updateZoomSliderTrack();
                updateZoomValueDisplay();
            }

            if (savedSizeOption) {
                currentSizeOption = savedSizeOption;
                $('.size-option').removeClass('active');
                $('.size-option[data-size="' + savedSizeOption + '"]').addClass('active');
            }
        }

        // 初始化设置
        updateZoomSliderTrack();
        updateZoomValueDisplay();
        loadSavedSettings();

        // 模拟装柜动画按钮点击事件
        $('#animateBtn').on('click', function() {
            if (isAnimating) {
                // 如果正在动画，停止动画
                stopAnimation();
                $(this).html('<i class="layui-icon layui-icon-play"></i> 模拟装柜动画');
            } else {
                // 如果没有动画，开始动画
                startAnimation();
                $(this).html('<i class="layui-icon layui-icon-pause"></i> 停止动画');
            }
        });

        // 开始装柜动画
        function startAnimation() {
            if (!simulationData || !simulationData.items || simulationData.items.length === 0) {
                layer.msg('没有可用的装柜数据', {icon: 2});
                return;
            }

            // 如果正在动画，先停止
            if (isAnimating) {
                stopAnimation();
            }

            // 清除场景，只保留集装箱和地面
            clearScene();

            // 创建集装箱
            var container = simulationData.container;
            var containerGeometry = new THREE.BoxGeometry(container.length, container.height, container.width);
            var containerMaterial = new THREE.MeshBasicMaterial({
                color: 0xffffff,
                transparent: true,
                opacity: 0.1,
                side: THREE.BackSide
            });
            var containerMesh = new THREE.Mesh(containerGeometry, containerMaterial);

            // 创建集装箱线框
            var containerEdges = new THREE.EdgesGeometry(containerGeometry);
            var containerLine = new THREE.LineSegments(
                containerEdges,
                new THREE.LineBasicMaterial({ color: 0x000000 })
            );

            // 调整集装箱位置，使其中心在原点
            containerMesh.position.set(container.length / 2, container.height / 2, container.width / 2);
            containerLine.position.set(container.length / 2, container.height / 2, container.width / 2);

            scene.add(containerMesh);
            scene.add(containerLine);

            // 创建地面网格
            var groundGeometry = new THREE.PlaneGeometry(container.length, container.width);
            var groundMaterial = new THREE.MeshBasicMaterial({
                color: 0xcccccc,
                side: THREE.DoubleSide,
                transparent: true,
                opacity: 0.5
            });
            var ground = new THREE.Mesh(groundGeometry, groundMaterial);
            ground.rotation.x = Math.PI / 2;
            ground.position.set(container.length / 2, 0, container.width / 2);
            scene.add(ground);

            // 清空货物网格对象和标签
            itemMeshes = {};
            removeLabels();

            // 准备动画队列
            animationQueue = [];
            originalItemMeshes = [];

            // 按照装载顺序排序货物
            var sortedItems = simulationData.items.slice().sort(function(a, b) {
                // 先按z坐标排序（从下到上）
                if (a.z !== b.z) {
                    return a.z - b.z;
                }
                // 然后按y坐标排序（从前到后）
                if (a.y !== b.y) {
                    return a.y - b.y;
                }
                // 最后按x坐标排序（从左到右）
                return a.x - b.x;
            });

            // 创建货物网格，但初始位置在集装箱上方
            sortedItems.forEach(function(item, index) {
                var itemGeometry = new THREE.BoxGeometry(item.length, item.height, item.width);
                var itemMaterial = new THREE.MeshLambertMaterial({
                    color: item.color,
                    transparent: true,
                    opacity: 0.8
                });
                var itemMesh = new THREE.Mesh(itemGeometry, itemMaterial);

                // 计算最终位置
                var finalPosX = item.x + item.length / 2;
                var finalPosY = item.z + item.height / 2;
                var finalPosZ = item.y + item.width / 2;

                // 初始位置在集装箱上方
                var startPosX = finalPosX;
                var startPosY = container.height * 2; // 在集装箱上方
                var startPosZ = finalPosZ;

                itemMesh.position.set(startPosX, startPosY, startPosZ);

                // 添加边框
                var itemEdges = new THREE.EdgesGeometry(itemGeometry);
                var itemLine = new THREE.LineSegments(
                    itemEdges,
                    new THREE.LineBasicMaterial({ color: 0x000000 })
                );
                itemMesh.add(itemLine);

                // 存储货物网格对象
                itemMeshes[item.id] = {
                    mesh: itemMesh,
                    originalColor: item.color,
                    item: item,
                    index: index + 1
                };

                // 添加到场景
                scene.add(itemMesh);

                // 添加到动画队列
                animationQueue.push({
                    mesh: itemMesh,
                    startPos: { x: startPosX, y: startPosY, z: startPosZ },
                    finalPos: { x: finalPosX, y: finalPosY, z: finalPosZ },
                    item: item
                });

                // 保存原始网格对象
                originalItemMeshes.push({
                    id: item.id,
                    mesh: itemMesh,
                    position: { x: finalPosX, y: finalPosY, z: finalPosZ },
                    item: item
                });
            });

            // 调整相机位置
            var maxDim = Math.max(container.length, container.width, container.height);
            camera.position.set(maxDim * 1.5, maxDim * 1.5, maxDim * 1.5);
            controls.target.set(container.length / 2, container.height / 2, container.width / 2);
            controls.update();

            // 开始动画
            isAnimating = true;
            currentAnimationIndex = 0;

            // 获取动画速度
            var speed = parseInt($('#animationSpeed').val()) || 5;
            var interval = 1100 - speed * 100; // 速度范围：100ms - 1000ms

            animationInterval = setInterval(function() {
                if (currentAnimationIndex >= animationQueue.length) {
                    // 动画完成
                    stopAnimation();
                    $('#animateBtn').html('<i class="layui-icon layui-icon-play"></i> 模拟装柜动画');
                    return;
                }

                // 获取当前要动画的货物
                var animation = animationQueue[currentAnimationIndex];

                // 创建动画
                var startY = animation.mesh.position.y;
                var finalY = animation.finalPos.y;
                var duration = 1000; // 1秒
                var startTime = Date.now();

                function animateItem() {
                    var elapsed = Date.now() - startTime;
                    var progress = Math.min(elapsed / duration, 1);

                    // 使用缓动函数使动画更自然
                    var easedProgress = easeOutBounce(progress);

                    // 更新位置
                    animation.mesh.position.y = startY + (finalY - startY) * easedProgress;

                    if (progress < 1) {
                        requestAnimationFrame(animateItem);
                    } else {
                        // 动画完成，创建标签
                        createLabel(
                            currentAnimationIndex + 1,
                            animation.finalPos.x,
                            animation.finalPos.y,
                            animation.finalPos.z,
                            animation.item.length,
                            animation.item.height,
                            animation.item.width,
                            animation.item.id
                        );

                        // 移动到下一个货物
                        currentAnimationIndex++;
                    }
                }

                animateItem();

            }, interval);
        }

        // 缓动函数 - 弹跳效果
        function easeOutBounce(t) {
            var n1 = 7.5625;
            var d1 = 2.75;

            if (t < 1 / d1) {
                return n1 * t * t;
            } else if (t < 2 / d1) {
                return n1 * (t -= 1.5 / d1) * t + 0.75;
            } else if (t < 2.5 / d1) {
                return n1 * (t -= 2.25 / d1) * t + 0.9375;
            } else {
                return n1 * (t -= 2.625 / d1) * t + 0.984375;
            }
        }

        // 停止装柜动画
        function stopAnimation() {
            if (animationInterval) {
                clearInterval(animationInterval);
                animationInterval = null;
            }

            isAnimating = false;

            // 如果动画没有完成，将所有货物移动到最终位置
            if (currentAnimationIndex < animationQueue.length) {
                for (var i = currentAnimationIndex; i < animationQueue.length; i++) {
                    var animation = animationQueue[i];
                    animation.mesh.position.set(
                        animation.finalPos.x,
                        animation.finalPos.y,
                        animation.finalPos.z
                    );

                    // 创建序号标签
                    createLabel(
                        i + 1,
                        animation.finalPos.x,
                        animation.finalPos.y,
                        animation.finalPos.z,
                        animation.item.length,
                        animation.item.height,
                        animation.item.width,
                        animation.item.id
                    );
                }
            }
        }
    });
</script>
#end
