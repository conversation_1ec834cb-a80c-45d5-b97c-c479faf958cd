#set(seoTitle="创建反馈")
#@layout()
#define main()
<!-- 个人空间左侧菜单栏 -->
#include("/_view/my/common/_my_menu_bar.html")

<!-- 内容容器 -->
    #define content()
	<!-- 项目 -->
	<div>
		<div class="jf-breadcrumb-box">
			<ol class="jf-breadcrumb">
				<li><a href="/">首页</a></li>
				<li id="managementModule" class="clickable-menu-item"><i class="layui-icon layui-icon-set"></i><button class="layui-btn layui-btn-sm" onclick="openModuleModal('管理相关')">管理相关</button></li>
				<li><a href="/my/feedback">反馈管理</a></li>
				<li class="active">创建反馈</li>
			</ol>
		</div>

		<form id="myArticleForm" action="/my/feedback/save" method="post">
			<div class="jf-my-article-add">
				<span class="label">标题</span>
				<div class="title-box">
					<input class="title" type="text" name="feedback.title" />
				</div>
				<span class="label">关联项目</span>
				<div class="related-project-box">
					<select lay-verify="required" lay-search="" name="feedback.projectId" class="related-project-name">
						<option >请选择</option>
						#for(x : projectList)
							<option value="#(x.id)">#(x.name)</option>
						#end
					</select>
				</div>
				<span class="label">正文</span>
				<div class="content-box"  style="line-height: 20px;" >
					<script id="container" name="feedback.content" style="line-height: 20px;"  type="text/plain"></script>
				</div>
				<div class="submit-box">
					<input class="submit-btn layui-btn" type="submit" value="提&nbsp;交" />
					<!--<a class="submit-btn layui-btn" onclick="$('#myArticleForm').submit();">提&nbsp;交</a>-->
				</div>
			</div>
		</form>

	</div>
	#end
#end

#define js()

	#include("/_view/my/common/_my_space_article_form_js.html")
	<script type="text/javascript">
		// 上传时在 url 中用问号挂参的方式添加额外的参数 uploadType，用于分类管理图片
		ue.ready(function() {
			ue.execCommand('serverparam', {
				'uploadType': 'feedback'
			});
		});

		// 选中左侧菜单项
		$(document).ready(function() {
			setCurrentMyMenu();
		});

		// 也用 ajax 提交比较好，这样有利于在出异常的时候信息不会丢失
		$(document).ready(function() {
			$("#myArticleForm").ajaxForm({
				dataType: "json"
				, beforeSubmit: function(formData, jqForm, options) {
					ue.sync();  // 同步一下 ueditor 中的数据到表单域
				}
				, success: function(ret) {
					layer_alert_with_callback(ret.msg, ret.state, "/my/feedback");
				}
				, error: function(ret) {
					layer_alert(ret);
				}
				, complete: function(ret) { }       // 无论是 success 还是 error，最终都会被回调
			});
		});



	</script>
#end