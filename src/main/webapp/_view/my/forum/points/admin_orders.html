#set(seoTitle="订单管理 - 积分商城")
#@layout()
#define main()
#include("/_view/my/common/_my_menu_bar.html")

#define content()
<div class="layui-container">
    <div class="jf-breadcrumb-box">
        <ol class="jf-breadcrumb">
            <li><a href="/">首页</a></li>
            <li><a href="/my/forum">论坛</a></li>
            <li><a href="/my/forum/points">积分商城</a></li>
            <li class="active">订单管理</li>
        </ol>
    </div>
    
    <!-- 订单列表 -->
    <div class="layui-card">
        <div class="layui-card-header">
            <h2>订单管理</h2>
        </div>
        <div class="layui-card-body">
            <!-- 搜索栏 -->
            <form class="layui-form search-box" action="/my/forum/points/adminOrders" method="get">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">订单号</label>
                        <div class="layui-input-inline">
                            <input type="text" name="orderNo" placeholder="请输入订单号" 
                                autocomplete="off" class="layui-input" value="#(para.orderNo)">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">用户昵称</label>
                        <div class="layui-input-inline">
                            <input type="text" name="nickname" placeholder="请输入用户昵称" 
                                autocomplete="off" class="layui-input" value="#(para.nickname)">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">订单状态</label>
                        <div class="layui-input-inline">
                            <select name="status">
                                <option value="">全部</option>
                                <option value="0" #if(para.status == "0")selected#end>待发货</option>
                                <option value="1" #if(para.status == "1")selected#end>已发货</option>
                                <option value="2" #if(para.status == "2")selected#end>已完成</option>
                                <option value="3" #if(para.status == "3")selected#end>已取消</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn" lay-submit>查询</button>
                        <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                    </div>
                </div>
            </form>
            
            <!-- 订单表格 -->
            <table class="layui-table">
                <thead>
                    <tr>
                        <th>订单号</th>
                        <th>用户昵称</th>
                        <th>商品名称</th>
                        <th>商品类型</th>
                        <th>消耗积分</th>
                        <th>订单状态</th>
                        <th>下单时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    #for(order : orderPage.list)
                    <tr>
                        <td>#(order.orderNo)</td>
                        <td>#(order.user_nickname)</td>
                        <td>#(order.productName)</td>
                        <td>
                            #if(order.product_type == "VIRTUAL")
                            <span class="product-type virtual">虚拟商品</span>
                            #else
                            <span class="product-type physical">实物商品</span>
                            #end
                        </td>
                        <td class="points-num">#(order.points)</td>
                        <td>
                            <span class="order-status status-#(order.status)">#(order.statusText)</span>
                        </td>
                        <td>#date(order.createdTime, "yyyy-MM-dd HH:mm:ss")</td>
                        <td>
                            <div class="layui-btn-group">
                                <button class="layui-btn layui-btn-xs view-btn" 
                                    data-order-no="#(order.orderNo)">查看</button>
                                #if(order.status == 0)
                                <button class="layui-btn layui-btn-normal layui-btn-xs complete-btn" 
                                    data-order-no="#(order.orderNo)">完成</button>
                                #if(order.product_type == "PHYSICAL")
                                <button class="layui-btn layui-btn-warm layui-btn-xs ship-btn"
                                    data-order-no="#(order.orderNo)">发货</button>
                                #end
                                #end
                            </div>
                        </td>
                    </tr>
                    #end
                </tbody>
            </table>
            
            <!-- 分页 -->
            #if(orderPage.totalPage > 1)
            <div id="order-pagination"></div>
            #end
        </div>
    </div>
</div>

<!-- 订单详情弹窗 -->
<script type="text/html" id="orderDetailTpl">
    <div class="order-detail-dialog">
        <div class="detail-section">
            <h3>基本信息</h3>
            <table class="layui-table">
                <tr>
                    <td width="100">订单号</td>
                    <td>{{ d.orderNo }}</td>
                    <td width="100">下单时间</td>
                    <td>{{ d.createdTime }}</td>
                </tr>
                <tr>
                    <td>订单状态</td>
                    <td>{{ d.statusText }}</td>
                    <td>消耗积分</td>
                    <td>{{ d.points }}</td>
                </tr>
            </table>
        </div>
        
        <div class="detail-section">
            <h3>商品信息</h3>
            <table class="layui-table">
                <tr>
                    <td width="100">商品名称</td>
                    <td colspan="3">{{ d.productName }}</td>
                </tr>
            </table>
        </div>
        
        <div class="detail-section">
            <h3>收货信息</h3>
            <table class="layui-table">
                <tr>
                    <td width="100">收货人</td>
                    <td>{{ d.receiverName }}</td>
                    <td width="100">手机号</td>
                    <td>{{ d.receiverPhone }}</td>
                </tr>
                <tr>
                    <td>收货地址</td>
                    <td colspan="3">{{ d.receiverAddress }}</td>
                </tr>
                {{# if(d.remark){ }}
                <tr>
                    <td>备注</td>
                    <td colspan="3">{{ d.remark }}</td>
                </tr>
                {{# } }}
            </table>
        </div>
    </div>
</script>

<!-- 发货弹窗 -->
<script type="text/html" id="shipDialogTpl">
    <div class="ship-dialog">
        <form class="layui-form" lay-filter="shipForm" style="padding: 20px;">
            <div class="layui-form-item">
                <label class="layui-form-label">物流公司</label>
                <div class="layui-input-block">
                    <input type="text" name="shipping_company" required lay-verify="required" 
                        placeholder="请输入物流公司" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">物流单号</label>
                <div class="layui-input-block">
                    <input type="text" name="shipping_no" required lay-verify="required" 
                        placeholder="请输入物流单号" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">备注</label>
                <div class="layui-input-block">
                    <textarea name="remark" placeholder="请输入备注信息" class="layui-textarea"></textarea>
                </div>
            </div>
            <div class="layui-form-item" style="text-align: center;">
                <button class="layui-btn" lay-submit lay-filter="shipSubmit">确认发货</button>
            </div>
        </form>
    </div>
</script>

#end

#define css()
<style>
/* 搜索栏样式 */
.search-box {
    margin-bottom: 20px;
    padding: 20px;
    background: #f8f8f8;
    border-radius: 4px;
}

/* 表格样式 */
.points-num {
    color: #FF5722;
    font-weight: bold;
}

.order-status {
    padding: 2px 8px;
    border-radius: 2px;
    font-size: 12px;
}

.status-0 {
    background: #1E9FFF;
    color: #fff;
}

.status-1 {
    background: #5FB878;
    color: #fff;
}

.status-2 {
    background: #009688;
    color: #fff;
}

.status-3 {
    background: #FF5722;
    color: #fff;
}

/* 订单详情弹窗样式 */
.order-detail-dialog {
    padding: 20px;
}

.detail-section {
    margin-bottom: 20px;
}

.detail-section:last-child {
    margin-bottom: 0;
}

.detail-section h3 {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin: 0 0 15px;
    padding-left: 10px;
    border-left: 3px solid #1E9FFF;
}

/* 分页样式 */
#order-pagination {
    text-align: center;
    margin-top: 20px;
}

/* 响应式布局 */
@media screen and (max-width: 768px) {
    .layui-form-item .layui-inline {
        margin-bottom: 10px;
    }
    
    .layui-table-box {
        overflow-x: auto;
    }
    
    .order-detail-dialog {
        padding: 15px;
    }
}

/* 商品类型样式 */
.product-type {
    padding: 2px 8px;
    border-radius: 2px;
    font-size: 12px;
}

.product-type.virtual {
    background: #009688;
    color: #fff;
}

.product-type.physical {
    background: #1E9FFF;
    color: #fff;
}
</style>
#end

#define js()
<script>
layui.use(['form', 'layer', 'laypage', 'laytpl'], function(){
    var form = layui.form,
        layer = layui.layer,
        laypage = layui.laypage,
        laytpl = layui.laytpl;
    
    // 初始化分页
    #if(orderPage.totalPage > 1)
    laypage.render({
        elem: 'order-pagination',
        count: #(orderPage.totalRow),
        limit: #(orderPage.pageSize),
        curr: #(orderPage.pageNumber),
        layout: ['prev', 'page', 'next', 'count', 'skip'],
        jump: function(obj, first){
            if(!first){
                // 构建URL
                var url = new URL(location.href);
                url.searchParams.set('page', obj.curr);
                location.href = url.toString();
            }
        }
    });
    #end
    
    // 查看订单详情
    $('.view-btn').click(function(){
        var orderNo = $(this).data('order-no');
        
        $.get('/my/forum/points/orderDetail/' + orderNo, function(order){
            var getTpl = document.getElementById('orderDetailTpl').innerHTML;
            laytpl(getTpl).render(order, function(html){
                layer.open({
                    type: 1,
                    title: '订单详情',
                    area: ['800px', 'auto'],
                    content: html
                });
            });
        });
    });
    
    // 完成订单
    $('.complete-btn').click(function(){
        var btn = $(this);
        var orderNo = btn.data('order-no');
        
        layer.confirm('确认完成这个订单吗？', {
            title: '完成确认',
            btn: ['确认完成','取消']
        }, function(){
            var loadingIndex = layer.load(2);
            
            $.post('/my/forum/points/complete', {
                orderNo: orderNo
            }, function(res){
                layer.close(loadingIndex);
                
                if(res === true){
                    layer.msg('订单已完成', {
                        icon: 1,
                        time: 1500
                    }, function(){
                        location.reload();
                    });
                } else {
                    layer.msg('操作失败，请重试', {icon: 2});
                }
            }).fail(function(){
                layer.close(loadingIndex);
                layer.msg('网络错误，请重试', {icon: 2});
            });
        });
    });
    
    // 发货操作
    $('.ship-btn').click(function(){
        var orderNo = $(this).data('order-no');
        
        layer.open({
            type: 1,
            title: '订单发货',
            area: ['500px', 'auto'],
            content: $('#shipDialogTpl').html(),
            success: function(){
                form.render();
                
                // 监听发货表单提交
                form.on('submit(shipSubmit)', function(data){
                    var loadingIndex = layer.load(2);
                    
                    $.post('/my/forum/points/ship', {
                        orderNo: orderNo,
                        shippingCompany: data.field.shipping_company,
                        shippingNo: data.field.shipping_no,
                        remark: data.field.remark
                    }, function(res){
                        layer.close(loadingIndex);
                        
                        if(res === true){
                            layer.msg('发货成功', {
                                icon: 1,
                                time: 1500
                            }, function(){
                                location.reload();
                            });
                        } else {
                            layer.msg('发货失败，请重试', {icon: 2});
                        }
                    }).fail(function(){
                        layer.close(loadingIndex);
                        layer.msg('网络错误，请重试', {icon: 2});
                    });
                    
                    return false;
                });
            }
        });
    });
});
</script>
#end 