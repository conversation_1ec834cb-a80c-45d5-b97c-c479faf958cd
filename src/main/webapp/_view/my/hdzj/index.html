#set(seoTitle="活动资金管理")
#@layout()
#define main()
#include("/_view/my/common/_my_menu_bar.html")

#define content()
<div class="jf-breadcrumb-box">
    <ol class="jf-breadcrumb">
        <li><a href="/">首页</a></li>
        <li id="financeModule" class="clickable-menu-item"><i class="layui-icon layui-icon-rmb"></i><button class="layui-btn layui-btn-sm" onclick="openModuleModal('财务模块')">财务模块</button></li>
        <li class="active">活动资金</li>
    </ol>
    <div class="jf-btn-box">
        <a class="layui-btn layui-btn-sm" href="/my/hdzj/add">创&nbsp;&nbsp;建</a>
    </div>
</div>
<br>
<form class="layui-form" method="post" action="/my/hdzj">
    <div class="layui-form-item">
        <div class="form-inline">
            <div class="layui-input-inline">
                <input class="layui-input" type="text" name="query" id="query" value="#(query??)" placeholder="输入内容查询">
            </div>
            <div class="layui-input-inline">
                <input type="text" class="layui-input" name="queryKsrq" id="queryKsrq" value="#(queryKsrq??)" autocomplete="off" placeholder="yyyy-MM-dd">
            </div>
            <div class="layui-input-inline">
                <input type="text" class="layui-input" name="queryJsrq" id="queryJsrq" value="#(queryJsrq??)" autocomplete="off" placeholder="yyyy-MM-dd">
            </div>
            <div class="layui-input-inline">
                <select name="queryPx" lay-search>
                    #for(x : pxList)
                    <option value="#(x.value??)" #if(queryPx??==x.value) selected #end>#(x.name??)</option>
                    #end
                </select>
            </div>
            <div class="layui-input-inline">
                #@fy_select()
            </div>
            <div class="layui-input-inline" style="width: 400px;">
                <button class="layui-btn layui-btn-sm" lay-submit lay-filter="component-form-element">查询</button>
                <button type="button" class="layui-btn layui-btn-sm" id="uploadFile"><i class="layui-icon"></i>导入</button>
                <a class="layui-btn layui-btn-sm" href="/download/活动资金模版.xls">模版下载</a>
                <a class="layui-btn layui-btn-sm" href="/my/hdzj/export" target="_blank">导出</a>
            </div>
        </div>
    </div>
</form>
<table class="layui-table" lay-size="sm">
    <thead>
    <tr>
        <th>日期</th>
        <th>数额</th>
        <th>事项说明</th>
        <th>备注</th>
        <th style="width: 160px;">操作</th>
    </tr>
    </thead>
    <tbody>
    #for(x : page.list)
    <tr>
        <td>#(x.rq??)</td>
        <td>#(x.sz??)</td>
        <td>#(x.sxsm??)</td>
        <td>#(x.bz??)</td>
        <td>
            <div class="layui-btn-group">
                #role("权限管理员", "超级管理员", "总经理")
                <a class="layui-btn layui-btn-sm" href="/my/hdzj/edit?id=#(x.id)">编辑</a>&nbsp;&nbsp;
                <button class="layui-btn layui-btn-sm" onclick="confirmAjaxLayer('删除 #escape(x.sxsm??) 后无法恢复，确定要删除？', '/my/hdzj/delete?id=#(x.id)', this);">删除</button>
                #end
            </div>
        </td>
    </tr>
    #end
    </tbody>
</table>
#@paginate(page.pageNumber, page.totalPage, "/my/hdzj?query=" + query + "&queryKsrq=" + queryKsrq + "&queryJsrq=" + queryJsrq + "&queryPx=" + queryPx + "&pageSize" + pageSize + "&p=")
#end
#end


#define js()
<script type="application/javascript">
    layui.laydate.render({
        elem: '#queryKsrq'
    });
    layui.laydate.render({
        elem: '#queryJsrq'
    });
    layui.upload.render({
        elem: '#uploadFile'
        , timeout: 300000
        , url: '/my/hdzj/uploadFile'
        , accept: 'file'
        , done: function (res) {
            location.reload();
        }
    });
</script>
#end