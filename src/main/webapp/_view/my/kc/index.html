#set(seoTitle="库存单管理")
#@layout()
#define main()
#include("/_view/my/common/_my_menu_bar.html")

#define content()
<div>
    <div class="jf-breadcrumb-box">
        <ol class="jf-breadcrumb">
            <li><a href="/">首页</a></li>
            <li id="businessRelated" class="clickable-menu-item"><i class="layui-icon layui-icon-form"></i><button class="layui-btn layui-btn-sm" onclick="openModuleModal('业务相关')">业务相关</button></li>
            <li class="active">库存管理</li>
        </ol>
        <div class="jf-btn-box">
            <a class="layui-btn layui-btn-sm" href="/my/kc/add">创&nbsp;&nbsp;建</a>
        </div>
    </div>
    <hr>
    <form method="post" action="/my/kc" class="layui-form">
        <div class="layui-form-item" style="margin-left: 50px;">
            <div class="layui-input-inline w100" title="搜指定库存单号，模糊搜索">
                <input type="text" name="query" class="layui-input" value="#(query??)" placeholder="库存单号模糊搜索">
            </div>
            <div class="layui-input-inline w100" title="搜指定工程编号，模糊搜索">
                <input type="text" name="queryGcdd" class="layui-input" value="#(queryGcdd??)" placeholder="工程编号">
            </div>
            <div class="layui-input-inline w100" title="搜指定客户订单号，模糊搜索">
                <input type="text" name="queryKhdd" class="layui-input" value="#(queryKhdd??)" placeholder="客户订单号">
            </div>
            <div class="layui-input-inline w100" title="搜指定石种，模糊搜索">
                <input type="text" name="querySz" class="layui-input" value="#(querySz??)" placeholder="石种">
            </div>
            <div class="layui-input-inline w100" title="搜指定品名，模糊搜索">
                <input type="text" name="queryPm" class="layui-input" value="#(queryPm??)" placeholder="品名">
            </div>
            <div class="layui-input-inline w100" title="搜指定加工方式，模糊搜索">
                <input type="text" name="queryJgfs" class="layui-input" value="#(queryJgfs??)" placeholder="加工方式">
            </div>
            <div class="layui-input-inline w100" title="搜指定货号/包装备注，模糊搜索">
                <input type="text" name="queryBzbz" class="layui-input" value="#(queryBzbz??)" placeholder="货号/包装备注">
            </div>
            <div class="layui-input-inline w150" title="搜订单类型">
                #@ss_select("queryLx", ",正常订单,询价单,其他代垫", queryLx??)
            </div>
            <div class="layui-input-inline w150" title="搜指定客户订单">
                <select lay-verify="required" lay-search="" id="khid" name="khid" title="客户">
                    <option value="">请选择客户</option>
                    #for( kh : khList)
                    <option value="#(kh.id??)" #if(kh.id??==khid??) selected #end>#(kh.jc??)</option>
                    #end
                </select>
            </div>
            <div class="layui-input-inline w200" title="搜含零价格工程">
                #@ss_select("queryLjg", ",含零价格工程,含零价格工程有索赔,含零价格工程无索赔", queryLjg??)
            </div>
            <div class="layui-input-inline w100" title="搜>录入时间">
                <input class="layui-input" type="text" id="queryLrKsrq" name="queryLrKsrq" value="#(queryLrKsrq??)" autocomplete="off" placeholder="录入开始日期" size="12">
            </div>
            <div class="layui-input-inline w100" title="搜<录入时间>">
                <input class="layui-input" type="text" id="queryLrJsrq" name="queryLrJsrq" value="#(queryLrJsrq??)" autocomplete="off" placeholder="录入结束日期" size="12">
            </div>
            <div class="layui-input-inline w100">
                <div class="layui-col-lg1"><input type="submit" class="layui-btn layui-btn-sm" value="查找"></div>
            </div>
        </div>
    </form>
    <hr>
    <table class="layui-table" lay-size="sm">
        <thead>
        <tr>
            <td style="width:100px;">库存单号</td>
            <td style="width:100px;">采购单号</td>
            <td style="width:100px;">订单编号</td>
            <td style="width:80px;">工程数</td>
            <td>总才数</td>
            <td>总重量</td>
            <td>总金额</td>
            <td>创建人</td>
            <td>创建时间</td>
            <td>操作</td>
        </tr>
        </thead>
        <tbody>
        #for( x: page.list )
        <tr>
            <td title="#(x.ckbh??)"><a href="/my/kc/edit?id=#(x.id??)" target="_blank">#(x.ckbh??)</a></td>
            <td title="#(x.cgdh??)"><a href="/my/cgd/edit?id=#(x.id??)" target="_blank">#(x.cgdh??)</a></td>
            <td title="#(x.ddbh??)"><a href="/my/dd/edit?id=#(x.id??)" target="_blank">#(x.ddbh??)</a></td>
            <td>#(x.count_gcdd??)</td>
            <td>#(x.zcs??)</td>
            <td>#(x.zzl??)</td>
            <td>#(x.zje??)#(x.hb??)</td>
            <td>#(x.lrr??)</td>
            <td>#(x.lrsj??)</td>
            <td>
                <div class="form-inline">
                    <div class="layui-form-item">
                        <div class="layui-input-inline w50">
                            <a class="layui-btn layui-btn-sm" href="/my/kc/edit?id=#(x.id??)" target="_blank">编辑</a>
                        </div>
                        <div class="layui-input-inline w60">
                            <ul class="layui-nav layui-bg-green w55" style="z-index:#(999-for.index??0);position:relative;">
                                <li class="layui-nav-item">
                                    <a class="layui-btn layui-btn-sm">更多</a>
                                    <dl class="layui-nav-child"> <!-- 二级菜单 -->
                                        <dd><a class="layui-btn layui-btn-normal layui-btn-sm" onclick="unlock('#(x.ddbh??)');">入仓</a></dd>
                                        <dd><a class="layui-btn layui-btn-normal layui-btn-sm" onclick="confirmAjaxLayer('删除 #escape(x.ckbh??) 后无法恢复，确定要删除？', '/my/kc/delete?id=#(x.id??)', this);">删除</a></dd>
                                    </dl>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </td>
        </tr>
        #end
        </tbody>
    </table>
    #@paginate(page.pageNumber, page.totalPage, "/my/kc?query=" + query + "&queryGcdd=" + queryGcdd + "&queryPm=" + queryPm + "&khid=" + khid + "&p=")
</div>

#end
#end

#define js()
<script type="text/javascript">

    layui.use('laydate', function () {
        var laydate = layui.laydate;

        laydate.render({
            elem: '#queryLrKsrq'
        });
        laydate.render({
            elem: '#queryLrJsrq'
        });
        laydate.render({
            elem: '#queryCqKsrq'
        });
        laydate.render({
            elem: '#queryCqJsrq'
        });
    });


    function unlock(ddbh) {
        $.ajax({
            url: "/my/kc/unlock",
            type: "POST",
            cache: false,
            dataType: "json",
            data: {
                "ddbh": ddbh
            },
            success: function (ret) {
                layer.msg(ret.msg, {time: 2000, shade: 0.3});
            },
            fail: function (ret) {
                showReplyErrorMsg(ret.msg);
                return false;
            }
        });
    }
</script>
#end
