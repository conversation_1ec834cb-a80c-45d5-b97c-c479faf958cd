#set(seoTitle="客户信息管理")
#@layout()
#define main()
#include("/_view/my/common/_my_menu_bar.html")

#define content()
<div>
    <div class="jf-breadcrumb-box">
        <ol class="jf-breadcrumb">
            <li><a href="/">首页</a></li>
            <li id="businessRelated" class="clickable-menu-item"><i class="layui-icon layui-icon-form"></i><button class="layui-btn layui-btn-sm" onclick="openModuleModal('业务相关')">业务相关</button></li>
            <li class="active">客户信息</li>
        </ol>
        <div class="jf-btn-box">
            <a class="layui-btn layui-btn-sm" href="/my/kh/add">创&nbsp;&nbsp;建</a>
        </div>
        <div class="layui-col8">
            <form class="layui-form" method="post" action="/my/kh">
                <div class="layui-row layui-col-space10 layui-form-item">
                    <div class="layui-col-lg2"><input class="layui-input" type="text" name="query" value="#(query??)" placeholder="简称/清关抬头/办公室名称查询"></div>

                    <div class="layui-col-lg1">
                        <button class="layui-btn layui-btn-sm" lay-submit lay-filter="component-form-element">查询</button>
                    </div>
                </div>
                <div class="layui-row layui-col-space10" id="errorResult">

                </div>
            </form>
        </div>
    </div>
    <br>
    <input type="hidden" id="id" name="id">
    <table class="layui-table" lay-size="sm">
        <thead>
        <tr>
            <th style="width: 120px;">简称</th>
            <th>联系人</th>
            <th style="width: 200px;">电话</th>
            <th style="width: 200px;">DDU港口驾驶距离</th>
            <th style="width: 80px;">贸易条款</th>
            <th style="width: 100px;">后台产品价格</th>
            <th style="width: 80px;">美金系数</th>
            <th style="width: 80px">货币</th>
            <th style="width: 150px">付款方式</th>
            <th style="width: 200px">目的港</th>
            <th style="width: 300px;">操作</th>
        </tr>
        </thead>
        <tbody>
        #for(x : page.list)
        <tr>
            <td><a href="/my/kh/edit?id=#(x.id)" target="_blank">#(x.jc??)</a></td>
            <td>#(x.lxr??)</td>
            <td>#(x.dh??)</td>
            <td>#(x.ddujsjl??)</td>
            <td>#(x.mytk??)</td>
            <td>#(x.sjmytk??)</td>
            <td>#(x.sbmjxs??)</td>
            <td>#(x.hb??)</td>
            <td>#(x.fkfs??)</td>
            <td>#(x.mdg??)</td>
            <td>
                <div class="layui-btn-group">
                    <a class="layui-btn layui-btn-sm" href="/my/kh/edit?id=#(x.id)" target="_blank">编辑</a>&nbsp;&nbsp;
                    <a class="layui-btn layui-btn-sm" href="/my/kh/exportPrice?id=#(x.id??)&jc=#(x.jc??)">导出价格模版</a>&nbsp;&nbsp;
                    <button type="button" class="layui-btn layui-btn-sm" onclick='clickUp("#(x.id??)")'><i class="layui-icon"></i>上传价格</button>
                    #role("权限管理员", "超级管理员", "总经理")
                    <span class="layui-btn layui-btn-sm" onclick="confirmAjaxLayer('删除 #escape(x.jc??) 后无法恢复，确定要删除？', '/my/kh/delete?id=#(x.id)', this);"><i class="layui-icon"></i></span>
                    #end
                </div>
            </td>
        </tr>
        #end
        </tbody>
    </table>
    #@paginate(page.pageNumber, page.totalPage, "/my/kh?query=" + query + "&p=")
    <button id="uploadKhjgFile" class="layui-btn layui-btn-sm" style="display: none">上传</button>
</div>
#end
#end

#define js()
<script type="text/javascript">
    var up = layui.upload.render({
        elem: '#uploadKhjgFile'
        , url: '/my/kh/uploadKhjgFile'
        ,accept: 'file' //普通文件
        , timeout: 300000
        ,done: function(res){
            layer.msg('上传成功');
            console.log(res);
        }
    });
    function clickUp(id){
        up.reload({
            data:{id:id}
        });
        $("#uploadKhjgFile").click();
    }
</script>
#end