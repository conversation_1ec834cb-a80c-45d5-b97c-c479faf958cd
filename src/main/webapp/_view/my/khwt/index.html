#set(seoTitle="客户沟通问题管理")
#@layout()
#define main()
#include("/_view/my/common/_my_menu_bar.html")

#define content()
<div>
    <div class="jf-breadcrumb-box">
        <ol class="jf-breadcrumb">
            <li><a href="/">首页</a></li>
            <li id="managementModule" class="clickable-menu-item"><i class="layui-icon layui-icon-set"></i><button class="layui-btn layui-btn-sm" onclick="openModuleModal('管理相关')">管理相关</button></li>
            <li class="active">营销客户问题设计</li>
        </ol>
        <div class="jf-btn-box">
            <a class="layui-btn layui-btn-sm" href="/my/khwt/add">创&nbsp;&nbsp;建</a>
        </div>
        <div class="layui-col8">
            <form class="layui-form" method="post" action="/my/khwt">
                <div class="layui-row layui-col-space10 layui-form-item">
                    <div class="layui-col-lg1"><input class="layui-input" type="text" name="query" placeholder="输入内容查询"></div>
                    <div class="layui-col-lg6">
                        <button class="layui-btn layui-btn-sm" lay-submit lay-filter="component-form-element">查询</button>
                        <button type="button" class="layui-btn layui-btn-sm" id="uploadFile"><i class="layui-icon"></i>导入</button>
                        <a class="layui-btn layui-btn-sm" href="/download/客户沟通问题模版.xls">模版下载</a>
                        <a class="layui-btn layui-btn-sm" href="/my/khwt/export" target="_blank">导出</a>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <br>
    <div class="layui-fluid">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <table class="layui-table" lay-size="sm">
                            <thead>
                            <tr>
                                <th>简要描述</th>
                                <th>问题范例</th>
                                <th>答案选项</th>
                                <th>重要性</th>
                                <th>排序</th>
                                <th style="width: 100px;">操作</th>
                            </tr>
                            </thead>
                            <tbody>
                            #for(x : page.list)
                            <tr>
                                <td>#(x.jyms??)</td>
                                <td>#(x.wtfl??)</td>
                                <td>#(x.daxx??)</td>
                                <td>#(x.zyx??)</td>
                                <td>#(x.px??)</td>

                                <td>
                                    <div class="layui-btn-group">
                                        <a class="layui-btn layui-btn-sm" href="/my/khwt/edit?id=#(x.id)" target="_blank">编辑</a>&nbsp;&nbsp;
                                        #role("权限管理员", "超级管理员", "总经理")
                                        <span class="layui-btn layui-btn-sm" onclick="confirmAjaxLayer('删除 #escape(jsString(x.jyms??)) 后无法恢复，确定要删除？', '/my/khwt/delete?id=#(x.id)', this);">删除</span>
                                        #end
                                    </div>
                                </td>
                            </tr>
                            #end
                            <tr>
                                <td><input type="text" id="khwt.jyms" name="khwt.jyms"></td>
                                <td><textarea class="layui-textarea" id="khwt.wtfl" rows="3" cols="100%"></textarea></td>
                                <td><input type="text" id="khwt.daxx" name="khwt.daxx" placeholder="多个选项用##分隔"></td>
                                <td><input type="text" id="khwt.zyx" name="khwt.zyx" value="5"></td>
                                <td><input type="text" id="khwt.px" name="khwt.px" value="100"></td>
                                <td>
                                    <a href="#" onclick="add()">添加</a>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    #@paginate(page.pageNumber, page.totalPage, "/my/khwt?query=" + query + "&queryKsrq=" + queryKsrq + "&queryJsrq=" + queryJsrq + "&queryPx=" + queryPx + "&p=")
</div>
#end
#end



#define js()
<script type="application/javascript">
    function add() {
        $.ajax({
            url: '/my/khwt/save',
            type: 'POST',
            data: {
                "khwt.jyms": $("#khwt\\.jyms").val(),
                "khwt.wtfl": $("#khwt\\.wtfl").val(),
                "khwt.daxx": $("#khwt\\.daxx").val(),
                "khwt.zyx": $("#khwt\\.zyx").val(),
                "khwt.px": $("#khwt\\.px").val()
            },
            cache: false,
            dataType: 'json',
            success: function (ret) {
                layer_alert(ret.msg);
                if(ret.state==="ok"){
                    location.href = "/my/khwt/edit?id="+ret.id;
                }
            },
            error: function (data) {
                layer_alert(data.msg);
            }
        });
    }
</script>
#end