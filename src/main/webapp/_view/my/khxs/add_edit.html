#set(seoTitle="客户线索管理 " + (isAdd?"创建":"编辑"))
#@layout()
#define main()
#include("/_view/my/common/_my_menu_bar.html")

#define content()
<div>
    <div class="jf-breadcrumb-box">
        <ol class="jf-breadcrumb">
            <li><a href="/">首页</a></li>
            <li id="businessRelated" class="clickable-menu-item"><i class="layui-icon layui-icon-form"></i><button class="layui-btn layui-btn-sm" onclick="openModuleModal('业务相关')">业务相关</button></li>
            <li><a href="/my/khxs">客户线索</a></li>
            <li class="active">#(isAdd ? "创建客户线索" : "编辑客户线索")</li>
        </ol>
    </div>
    <br>
    <br>

    <div class="layui-row">
        <label class="layui-form-lable"></label>
         <div class="layui-input-inline">
            <textarea id="znsb" cols="60" rows="6" class="layui-textarea"></textarea>
        </div>
         <div class="layui-input-inline">&nbsp;<button class="layui-btn btn-large" onclick="znsb()">智能识别</button>
        </div>
    </div>
<br>
    <form class="layui-form" action="/my/khxs/#(isAdd ? 'save' : 'update')" method="post">

        <input type="hidden" name="khxs.id" value="#(khxs.id??)">

        <div class="layui-form-item">
            <label class="layui-form-label">客户名称</label>
             <div class="layui-input-inline">
                <input type="text" class="layui-input" id="khxs.mc" name="khxs.mc" value="#(khxs.mc??)">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">客户性质</label>
             <div class="layui-input-inline">
                <select id="khxs.xz" name="khxs.xz">
                    <option></option>
                    <option value="批发商" #if(khxs.xz??=="批发商") selected #end>批发商</option>
                    <option value="零售商" #if(khxs.xz??=="零售商") selected #end>零售商</option>
                </select>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">采购源地</label>
             <div class="layui-input-inline">
                <input type="text" class="layui-input" id="khxs.cgyd" name="khxs.cgyd" list="cgyds" value="#(khxs.cgyd??)">
                <datalist id="cgyds" style="display:none;">
                    <option>中国</option>
                    <option>印度</option>
                    <option>本地</option>
                    <option>中国/印度</option>
                    <option>中国/本地</option>
                    <option>印度/本地</option>
                    <option>中国/印度/本地</option>
                </datalist>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">国别</label>
             <div class="layui-input-inline">
                <input type="text" class="layui-input" id="khxs.gb" name="khxs.gb" list="gbs" value="#(khxs.gb??)">
                <datalist id="gbs" style="display:none;">
                    <option>美国</option>
                    <option>德国</option>
                    <option>澳大利亚</option>
                    <option>英国</option>
                    <option>法国</option>
                    <option>加拿大</option>
                    <option>新西兰</option>
                    <option>俄罗斯</option>
                    <option>荷兰</option>
                    <option>爱尔兰</option>
                    <option>中国</option>
                </datalist>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">州</label>
             <div class="layui-input-inline">
                <input type="text" class="layui-input" id="khxs.zhou" name="khxs.zhou" value="#(khxs.zhou??)">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">城市</label>
             <div class="layui-input-inline">
                <input type="text" class="layui-input" id="khxs.cs" name="khxs.cs" value="#(khxs.cs??)">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">地址</label>
             <div class="layui-input-inline">
                <input type="text" class="layui-input" id="khxs.dz" name="khxs.dz" value="#(khxs.dz??)">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">决策者</label>
             <div class="layui-input-inline">
                <input type="text" class="layui-input" id="khxs.jcz" name="khxs.jcz" value="#(khxs.jcz??)">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">决策者性别</label>
             <div class="layui-input-inline">
                <select id="khxs.jczxb" name="khxs.jczxb">
                    <option></option>
                    <option value="男" #if(khxs.jczxb??=="男") selected #end>男</option>
                    <option value="女" #if(khxs.jczxb??=="女") selected #end>女</option>
                </select>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">决策者邮箱</label>
             <div class="layui-input-inline">
                <input type="text" class="layui-input" id="khxs.jczyx" name="khxs.jczyx" value="#(khxs.jczyx??)">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">决策者手机</label>
             <div class="layui-input-inline">
                <input type="text" class="layui-input" id="khxs.jczsj" name="khxs.jczsj" value="#(khxs.jczsj??)">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">主要供应商</label>
             <div class="layui-input-inline">
                <input type="text" class="layui-input" id="khxs.zygys" name="khxs.zygys" value="#(khxs.zygys??)">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">比价产品</label>
             <div class="layui-input-inline">
                <input type="text" class="layui-input" id="khxs.bjcp" name="khxs.bjcp" value="#(khxs.bjcp??)">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">网站</label>
             <div class="layui-input-inline">
                <input type="text" class="layui-input" id="khxs.wz" name="khxs.wz" value="#(khxs.wz??)">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-lable">域名</label>
            <label class="layui-col-sm4 control-label">#(khxs.ym??)</label>
        </div>


        <div class="layui-form-item">
            <label class="layui-form-label">备注</label>
             <div class="layui-input-inline">
                <input type="text" class="layui-input" id="khxs.bz" name="khxs.bz" value="#(khxs.bz??)">
            </div>
        </div>

        <div class="layui-form-item">
            <div class="layui-col-smoffset-2 col-sm-3">
                <input class="layui-btn layui-btn-sm" type="submit" value="提交"/>
            </div>
        </div>
    </form>
</div>
#end
#end

#define js()

<script type="text/javascript">
    function znsb(){
        var a = document.getElementById('znsb').value;
        var aa = a.split(/[\t;]/);
        document.getElementById('khxs.mc').value=aa[0];
        for (const argument of aa) {
            if(isEmail(argument)){
                document.getElementById('khxs.jczyx').value=argument;
            }else if(isMayBeTelCode(argument)){
                document.getElementById('khxs.jczsj').value=argument;
            }else if(isURL(argument)){
                document.getElementById('khxs.wz').value=argument;
            }
        }
    }

    $(document).ready(function () {
        $("form").ajaxForm({
            dataType: "json"
            , success: function (ret) {
                layer_alert_with_callback(ret.msg, ret.state, "/my/khxs/edit?id="+ret.id);
            }
            , error: function (data) {
                layer_alert("网络操作失败，请咨询老柳! QQ75272683");
            }
        });
    });
</script>
#end
