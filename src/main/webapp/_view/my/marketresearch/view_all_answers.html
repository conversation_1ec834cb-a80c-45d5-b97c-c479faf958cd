#set(seoTitle="调研所有答卷")
#@layout()
#define main()
#include("/_view/my/common/_my_menu_bar.html")

#define content()
<div class="answer-container">
    <div class="jf-breadcrumb-box">
        <ol class="jf-breadcrumb">
            <li><a href="/">首页</a></li>
            <li><a href="/my/marketresearch/statistics">调研统计</a></li>
            <li class="active">查看所有调研 <span class="admin-badge">管理员</span></li>
        </ol>
    </div>

    <div class="customer-info">
        <h3>客户信息</h3>
        <div class="layui-row">
            <div class="layui-col-md6">
                <p><strong>公司名称：</strong>#(customer.gsmc??)</p>
                <p><strong>决策者：</strong>#(customer.jcz??)</p>
            </div>
            <div class="layui-col-md6">
                <p><strong>国别：</strong>#(customer.gb??)</p>
                <p><strong>邮箱：</strong>#(customer.jczyx??)</p>
            </div>
        </div>
    </div>

    <div class="layui-card">
        <div class="layui-card-header">
            <h2>所有调研问答记录</h2>
            <p style="color: #666; margin-top: 5px;">显示该客户的所有调研员填写的答案</p>
        </div>
        <div class="layui-card-body">
            #if(answers.size() > 0)
                #for(answer : answers)
                <div class="answer-item">
                    <div class="question-header">
                        <div class="question-code">#(answer.question_code)</div>
                        <div class="question-content">
                            <div class="question-cn">#(answer.question_cn)</div>
                            #if(answer.question_en)
                            <div class="question-en">#(answer.question_en)</div>
                            #end
                        </div>
                    </div>

                    #if(answer.answer_content)
                    <div class="answer-content">
                        #(answer.answer_content)
                    </div>
                    <div class="survey-info">
                        调研员：#(answer.surveyor_nick_name??) (#(answer.surveyor_username??)) |
                        调研时间：#date(answer.survey_date, "yyyy-MM-dd HH:mm")
                    </div>
                    #else
                    <div class="no-answer">
                        暂无答案
                    </div>
                    #end
                </div>
                #end
            #else
            <div class="no-answer">
                该客户尚未进行调研
            </div>
            #end
        </div>
    </div>

    <div style="text-align: center; margin-top: 20px;">
        <a class="layui-btn layui-btn-primary" href="/my/marketresearch/statistics">返回统计</a>
        <a class="layui-btn layui-btn-normal" href="/my/marketresearch/survey?customerId=#(customer.id)">继续调研</a>
    </div>
</div>
#end
#end


#define css()
<style>
    .answer-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }
    .customer-info {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 20px;
    }
    .answer-item {
        background: #fff;
        border: 1px solid #e6e6e6;
        border-radius: 5px;
        padding: 20px;
        margin-bottom: 15px;
    }
    .question-header {
        display: flex;
        align-items: flex-start;
        margin-bottom: 15px;
    }
    .question-code {
        background: #1890ff;
        color: white;
        padding: 5px 10px;
        border-radius: 3px;
        font-weight: bold;
        margin-right: 15px;
        min-width: 40px;
        text-align: center;
    }
    .question-content {
        flex: 1;
    }
    .question-cn {
        font-size: 16px;
        font-weight: 500;
        color: #333;
        margin-bottom: 8px;
    }
    .question-en {
        font-size: 14px;
        color: #666;
        font-style: italic;
        margin-bottom: 15px;
    }
    .answer-content {
        background: #f9f9f9;
        padding: 15px;
        border-radius: 4px;
        border-left: 4px solid #52c41a;
        font-size: 14px;
        line-height: 1.6;
        white-space: pre-wrap;
        margin-bottom: 10px;
    }
    .multiple-answers {
        border-left: 4px solid #faad14;
    }
    .no-answer {
        color: #999;
        font-style: italic;
        text-align: center;
        padding: 20px;
    }
    .survey-info {
        font-size: 12px;
        color: #666;
        text-align: right;
        border-top: 1px solid #f0f0f0;
        padding-top: 8px;
        margin-top: 8px;
    }
    .admin-badge {
        background: #ff4d4f;
        color: white;
        padding: 2px 8px;
        border-radius: 10px;
        font-size: 12px;
        margin-left: 10px;
    }
</style>
#end

#define js()
<script>
    // 可以添加打印功能等
    function printAnswers() {
        window.print();
    }
    
    // 可以添加导出功能
    function exportAnswers() {
        window.open('/my/marketresearch/export?customerId=#(customer.id)');
    }
</script>
#end
