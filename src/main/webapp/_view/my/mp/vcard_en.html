<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Visiting Card #(mp.xm??)</title>
    <link rel="stylesheet" href="https://cdn.staticfile.org/layui/2.5.6/css/layui.min.css">
    <script src="https://cdn.staticfile.org/layui/2.5.6/layui.min.js"></script>
    <style>
        body {
            background-color: #f2f2f2;
        }

        .layui-card-header {
            font-size: 18px;
            color: #333;
            padding-top: 20px;
            padding-bottom: 20px;
            text-align: center;
            background-color: #fff;
            border-bottom: none;
        }

        .layui-card-body {
            padding-top: 30px;
            padding-bottom: 30px;
            background-color: #fff;
            border-top: none;
        }

        .layui-card-body p {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
            line-height: 24px;
        }

        .layui-card-body .layui-row {
            margin-top: 20px;
        }

        .layui-card-body .layui-col-md6 {
            text-align: center;
            padding-top: 10px;
            padding-bottom: 10px;
            border-right: 1px solid #f2f2f2;
        }

        .layui-card-body .layui-col-md6:last-child {
            border-right: none;
        }

        .layui-card-body .layui-icon {
            font-size: 24px;
            color: #666;
            margin-bottom: 10px;
            display: block;
        }

        .zp {
            /*min-height: 150px;*/
            max-height: 200px;
            /*min-width: 150px;*/
            /*max-width: 200px;*/
        }
    </style>
</head>
<body>
<div class="layui-container">
    <div class="layui-card">
        <div class="layui-card-header" style="height: 202px"><img title="#(mp.xm??)" src="/upload/mp/#(mp.zp??'default.png')" class="zp"></div>
        <div class="layui-card-body">
            <p>Name：#(mp.xm??"Bill")</p>
            <p>Position：#(mp.zw??)</p>
            <p>Company：#(mp.gs??)</p>
            <p>Address：#(mp.dz??)</p>
            <p>Mobile Phone：<a href="tel:#(mp.sj??)">#(mp.sj??)</a></p>
            <p>Email：<a href="mailto:#(mp.yx??)">#(mp.yx??)</a></p>
            <div class="layui-row">
                <div class="layui-col-md6">
                    <i class="layui-icon layui-icon-login-wechat"></i>
                    Wechat：<span id="wxSpan">#(mp.wx??)</span>
                </div>
                <div class="layui-col-md6">
                    <i class="layui-icon layui-icon-link"></i>
                    Website：<a href="#(mp.wz??)" target="_blank">#(mp.wz??)</a>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    // 获取span元素
    const span = document.querySelector("#wxSpan");
    // 给span元素添加一个title属性
    span.setAttribute("title", "#(mp.wx??)");
    // 给span元素添加一个点击事件监听器
    span.addEventListener("click", function () {
        // 把span元素的内容复制到剪贴板
        navigator.clipboard.writeText(span.textContent)
            .then(() => {
                // 显示一个提示信息
                layer_alert("复制成功!");
            })
            .catch((error) => {
                // 处理错误情况
                console.error(error);
            });
    });
</script>
</body>
</html>
