#set(seoTitle="订单状态管理 " + (isAdd?"创建":"编辑"))
#set(isAdd = xmjkmxMap == null ? true : false, isEdit = !isAdd)
#@layout()
#define main()
<!-- 个人空间左侧菜单栏 -->
#include("/_view/my/common/_my_menu_bar.html")

<!-- 内容容器 -->
#define content()
<!-- 项目 -->
<div>
    <div class="jf-breadcrumb-box">
        <ol class="jf-breadcrumb">
            <li><a href="/">首页</a></li>
            <li id="businessRelated" class="clickable-menu-item"><i class="layui-icon layui-icon-form"></i><button class="layui-btn layui-btn-sm" onclick="openModuleModal('业务相关')">业务相关</button></li>
            <li><a href="/my/order">订单状态</a></li>
            <li class="active">#(isAdd ? "创建订单" : "编辑订单")</li>
        </ol>
    </div>

    <form id="myArticleForm" class="layui-form" style="margin: 30px;"
          action="/my/order/finUpdate" method="post" onsubmit="return submitForm();">

        <div class="layui-form-item">
            <div class="layui-input-inline">
                <button type="submit" class="layui-btn layui-btn-sm">保存</button>
            </div>
            <div class="layui-input-inline">
                <label class="layui-form-label">订单编号 #(jkid??)</label>
                <input type="hidden" name="jkid" value="#(jkid??)">
            </div>
        </div>

        <div class="layui-tab layui-tab-brief layui-bg-gray" style="height: 800px;">
            <ul class="layui-tab-title">
                <li class="layui-this">回款相关</li>
                <li>港杂海运费</li>
                <li>其他项</li>
            </ul>
            <div class="layui-tab-content">
                <div class="layui-tab-item layui-show">
                    <div class="layui-form-item">
                        <div class="form-inline">
                            <label class="layui-form-label">回款</label>
                            <div class="layui-input-inline">
                                #set( hkje = xmjkmxMap==null ? null : xmjkmxMap.get("hkje") )
                                <input type="hidden" name="<EMAIL>" value="#(hkje.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="hkje">
                                <input data-number type="text" class="layui-input" name="<EMAIL>" value="#(hkje.sz??)" placeholder="回款金额">
                            </div>
                            <div class="layui-input-inline">
                                #set( hksj = xmjkmxMap==null ? null : xmjkmxMap.get("hksj") )
                                <input type="hidden" name="<EMAIL>" value="#(hksj.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="hksj">
                                <input type="text" class="layui-input" name="<EMAIL>" value="#(hksj.sz??)" autocomplete="off" placeholder="回款时间">
                            </div>
                            <div class="layui-input-inline">
                                #set( sjjkhl = xmjkmxMap==null ? null : xmjkmxMap.get("sjjkhl") )
                                <input type="hidden" name="<EMAIL>" value="#(sjjkhl.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="sjjkhl">
                                <input type="text" class="layui-input" name="<EMAIL>" value="#(sjjkhl.sz??)" placeholder="回款汇率">
                            </div>
                            <div class="layui-input-inline">
                                #set( hkfs = xmjkmxMap==null ? null : xmjkmxMap.get("hkfs") )
                                <input type="hidden" name="<EMAIL>" value="#(hkfs.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="hkfs">
                                <input type="text" class="layui-input" name="<EMAIL>" list="fsList" value="#(hkfs.sz??)" placeholder="回款方式">
                            </div>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <div class="form-inline">
                            <label class="layui-form-label"></label>
                            <div class="layui-input-inline">
                                #set( hkje1 = xmjkmxMap==null ? null : xmjkmxMap.get("hkje1") )
                                <input type="hidden" name="<EMAIL>" value="#(hkje1.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="hkje1">
                                <input type="text" data-number class="layui-input" name="<EMAIL>" value="#(hkje1.sz??)" placeholder="回款金额">
                            </div>
                            <div class="layui-input-inline">
                                #set( hksj1 = xmjkmxMap==null ? null : xmjkmxMap.get("hksj1") )
                                <input type="hidden" name="<EMAIL>" value="#(hksj1.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="hksj1">
                                <input type="text" class="layui-input" name="<EMAIL>" value="#(hksj1.sz??)" autocomplete="off" placeholder="回款时间">
                            </div>
                            <div class="layui-input-inline">
                                #set( sjjkhl1 = xmjkmxMap==null ? null : xmjkmxMap.get("sjjkhl1") )
                                <input type="hidden" name="<EMAIL>" value="#(sjjkhl1.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="sjjkhl1">
                                <input type="text" class="layui-input" name="<EMAIL>" value="#(sjjkhl1.sz??)" placeholder="回款汇率">
                            </div>
                            <div class="layui-input-inline">
                                #set( hkfs1 = xmjkmxMap==null ? null : xmjkmxMap.get("hkfs1") )
                                <input type="hidden" name="<EMAIL>" value="#(hkfs1.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="hkfs1">
                                <input type="text" class="layui-input" name="<EMAIL>" list="fsList" value="#(hkfs1.sz??)" placeholder="回款方式">
                            </div>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <div class="form-inline">
                            <label class="layui-form-label"></label>
                            <div class="layui-input-inline">
                                #set( hkje2 = xmjkmxMap==null ? null : xmjkmxMap.get("hkje2") )
                                <input type="hidden" name="<EMAIL>" value="#(hkje2.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="hkje2">
                                <input type="text" data-number class="layui-input" name="<EMAIL>" value="#(hkje2.sz??)" placeholder="回款金额">
                            </div>
                            <div class="layui-input-inline">
                                #set( hksj2 = xmjkmxMap==null ? null : xmjkmxMap.get("hksj2") )
                                <input type="hidden" name="<EMAIL>" value="#(hksj2.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="hksj2">
                                <input type="text" class="layui-input" name="<EMAIL>" value="#(hksj2.sz??)" autocomplete="off" placeholder="回款时间">
                            </div>
                            <div class="layui-input-inline">
                                #set( sjjkhl2 = xmjkmxMap==null ? null : xmjkmxMap.get("sjjkhl2") )
                                <input type="hidden" name="<EMAIL>" value="#(sjjkhl2.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="sjjkhl2">
                                <input type="text" class="layui-input" name="<EMAIL>" value="#(sjjkhl2.sz??)" placeholder="回款汇率">
                            </div>
                            <div class="layui-input-inline">
                                #set( hkfs2 = xmjkmxMap==null ? null : xmjkmxMap.get("hkfs2") )
                                <input type="hidden" name="<EMAIL>" value="#(hkfs2.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="hkfs2">
                                <input type="text" class="layui-input" name="<EMAIL>" list="fsList" value="#(hkfs2.sz??)" placeholder="回款方式">
                            </div>
                        </div>
                    </div>


                    <div class="layui-form-item">
                        <div class="form-inline">
                            <label class="layui-form-label"></label>
                            <div class="layui-input-inline">
                                #set( hkje3 = xmjkmxMap==null ? null : xmjkmxMap.get("hkje3") )
                                <input type="hidden" name="<EMAIL>" value="#(hkje3.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="hkje3">
                                <input type="text" data-number class="layui-input" name="<EMAIL>" value="#(hkje3.sz??)" placeholder="回款金额">
                            </div>
                            <div class="layui-input-inline">
                                #set( hksj3 = xmjkmxMap==null ? null : xmjkmxMap.get("hksj3") )
                                <input type="hidden" name="<EMAIL>" value="#(hksj3.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="hksj3">
                                <input type="text" class="layui-input" name="<EMAIL>" value="#(hksj3.sz??)" autocomplete="off" placeholder="回款时间">
                            </div>
                            <div class="layui-input-inline">
                                #set( sjjkhl3 = xmjkmxMap==null ? null : xmjkmxMap.get("sjjkhl3") )
                                <input type="hidden" name="<EMAIL>" value="#(sjjkhl3.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="sjjkhl3">
                                <input type="text" class="layui-input" name="<EMAIL>" value="#(sjjkhl3.sz??)" placeholder="回款汇率">
                            </div>
                            <div class="layui-input-inline">
                                #set( hkfs3 = xmjkmxMap==null ? null : xmjkmxMap.get("hkfs3") )
                                <input type="hidden" name="<EMAIL>" value="#(hkfs3.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="hkfs3">
                                <input type="text" class="layui-input" name="<EMAIL>" list="fsList" value="#(hkfs3.sz??)" placeholder="回款方式">
                            </div>
                        </div>
                    </div>


                    <div class="layui-form-item">
                        <div class="form-inline">
                            <label class="layui-form-label"></label>
                            <div class="layui-input-inline">
                                #set( hkje4 = xmjkmxMap==null ? null : xmjkmxMap.get("hkje4") )
                                <input type="hidden" name="<EMAIL>" value="#(hkje4.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="hkje4">
                                <input type="text" data-number class="layui-input" name="<EMAIL>" value="#(hkje4.sz??)" placeholder="回款金额">
                            </div>
                            <div class="layui-input-inline">
                                #set( hksj4 = xmjkmxMap==null ? null : xmjkmxMap.get("hksj4") )
                                <input type="hidden" name="<EMAIL>" value="#(hksj4.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="hksj4">
                                <input type="text" class="layui-input" name="<EMAIL>" value="#(hksj4.sz??)" autocomplete="off" placeholder="回款时间">
                            </div>
                            <div class="layui-input-inline">
                                #set( sjjkhl4 = xmjkmxMap==null ? null : xmjkmxMap.get("sjjkhl4") )
                                <input type="hidden" name="<EMAIL>" value="#(sjjkhl4.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="sjjkhl4">
                                <input type="text" class="layui-input" name="<EMAIL>" value="#(sjjkhl4.sz??)" placeholder="回款汇率">
                            </div>
                            <div class="layui-input-inline">
                                #set( hkfs4 = xmjkmxMap==null ? null : xmjkmxMap.get("hkfs4") )
                                <input type="hidden" name="<EMAIL>" value="#(hkfs4.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="hkfs4">
                                <input type="text" class="layui-input" name="<EMAIL>" list="fsList" value="#(hkfs4.sz??)" placeholder="回款方式">
                            </div>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <div class="form-inline">
                            <label class="layui-form-label"></label>
                            <div class="layui-input-inline">
                                #set( hkje5 = xmjkmxMap==null ? null : xmjkmxMap.get("hkje5") )
                                <input type="hidden" name="<EMAIL>" value="#(hkje5.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="hkje5">
                                <input type="text" data-number class="layui-input" name="<EMAIL>" value="#(hkje5.sz??)" placeholder="回款金额">
                            </div>
                            <div class="layui-input-inline">
                                #set( hksj5 = xmjkmxMap==null ? null : xmjkmxMap.get("hksj5") )
                                <input type="hidden" name="<EMAIL>" value="#(hksj5.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="hksj5">
                                <input type="text" class="layui-input" name="<EMAIL>" value="#(hksj5.sz??)" autocomplete="off" placeholder="回款时间">
                            </div>
                            <div class="layui-input-inline">
                                #set( sjjkhl5 = xmjkmxMap==null ? null : xmjkmxMap.get("sjjkhl5") )
                                <input type="hidden" name="<EMAIL>" value="#(sjjkhl5.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="sjjkhl5">
                                <input type="text" class="layui-input" name="<EMAIL>" value="#(sjjkhl5.sz??)" placeholder="回款汇率">
                            </div>
                            <div class="layui-input-inline">
                                #set( hkfs5 = xmjkmxMap==null ? null : xmjkmxMap.get("hkfs5") )
                                <input type="hidden" name="<EMAIL>" value="#(hkfs5.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="hkfs5">
                                <input type="text" class="layui-input" name="<EMAIL>" list="fsList" value="#(hkfs5.sz??)" placeholder="回款方式">
                            </div>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <div class="form-inline">
                            <label class="layui-form-label">其他差额</label>
                            <div class="layui-input-inline">
                                #set( qtce = xmjkmxMap==null ? null : xmjkmxMap.get("qtce") )
                                <input type="hidden" name="<EMAIL>" value="#(qtce.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="qtce">
                                <input type="text" class="layui-input" name="<EMAIL>" value="#(qtce.sz??)" placeholder="其他差额">
                            </div>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <div class="form-inline">
                            <label class="layui-form-label">未回款汇率</label>
                            <div class="layui-input-inline">
                                #set( whkhl = xmjkmxMap==null ? null : xmjkmxMap.get("whkhl") )
                                <input type="hidden" name="<EMAIL>" value="#(whkhl.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="whkhl">
                                <input type="text" class="layui-input" name="<EMAIL>" value="#(whkhl.sz??)" placeholder="未回款汇率">
                            </div>
                        </div>
                        <div class="form-inline">
                            <label class="layui-form-label">回款情况</label>
                            <div class="layui-input-inline">
                                #set( hkqk = xmjkmxMap==null ? null : xmjkmxMap.get("hkqk") )
                                <input type="hidden" name="<EMAIL>" value="#(hkqk.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="hkqk">
                                <input type="hidden" value="#(hkqk.sz??)">
                                #@ss_select("<EMAIL>", ",已回全款,部分回款", hkqk.sz??)
                            </div>
                        </div>
                        <div class="form-inline">
                            <label class="layui-form-label">订单状态</label>
                            <div class="layui-input-inline">
                                #set( ddzt = xmjkmxMap==null ? null : xmjkmxMap.get("ddzt") )
                                <input type="hidden" name="<EMAIL>" value="#(ddzt.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="ddzt">
                                #@ss_select("<EMAIL>", ",未完结,已完结", ddzt.sz??)
                            </div>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <div class="form-inline">
                            <label class="layui-form-label">更多回款</label>
                            <div class="layui-input-inline">
                                #set( hkje6 = xmjkmxMap==null ? null : xmjkmxMap.get("hkje6") )
                                <input type="hidden" name="<EMAIL>" value="#(hkje6.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="hkje6">
                                <input type="text" data-number class="layui-input" name="<EMAIL>" value="#(hkje6.sz??)" placeholder="回款金额">
                            </div>
                            <div class="layui-input-inline">
                                #set( hksj6 = xmjkmxMap==null ? null : xmjkmxMap.get("hksj6") )
                                <input type="hidden" name="<EMAIL>" value="#(hksj6.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="hksj6">
                                <input type="text" class="layui-input" name="<EMAIL>" value="#(hksj6.sz??)" autocomplete="off" placeholder="回款时间">
                            </div>
                            <div class="layui-input-inline">
                                #set( sjjkhl6 = xmjkmxMap==null ? null : xmjkmxMap.get("sjjkhl6") )
                                <input type="hidden" name="<EMAIL>" value="#(sjjkhl6.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="sjjkhl6">
                                <input type="text" class="layui-input" name="<EMAIL>" value="#(sjjkhl6.sz??)" placeholder="回款汇率">
                            </div>
                            <div class="layui-input-inline">
                                #set( hkfs6 = xmjkmxMap==null ? null : xmjkmxMap.get("hkfs6") )
                                <input type="hidden" name="<EMAIL>" value="#(hkfs6.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="hkfs6">
                                <input type="text" class="layui-input" name="<EMAIL>" list="fsList" value="#(hkfs6.sz??)" placeholder="回款方式">
                            </div>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <div class="form-inline">
                            <label class="layui-form-label"></label>
                            <div class="layui-input-inline">
                                #set( hkje7 = xmjkmxMap==null ? null : xmjkmxMap.get("hkje7") )
                                <input type="hidden" name="<EMAIL>" value="#(hkje7.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="hkje7">
                                <input type="text" data-number class="layui-input" name="<EMAIL>" value="#(hkje7.sz??)" placeholder="回款金额">
                            </div>
                            <div class="layui-input-inline">
                                #set( hksj7 = xmjkmxMap==null ? null : xmjkmxMap.get("hksj7") )
                                <input type="hidden" name="<EMAIL>" value="#(hksj7.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="hksj7">
                                <input type="text" class="layui-input" name="<EMAIL>" value="#(hksj7.sz??)" autocomplete="off" placeholder="回款时间">
                            </div>
                            <div class="layui-input-inline">
                                #set( sjjkhl7 = xmjkmxMap==null ? null : xmjkmxMap.get("sjjkhl7") )
                                <input type="hidden" name="<EMAIL>" value="#(sjjkhl7.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="sjjkhl7">
                                <input type="text" class="layui-input" name="<EMAIL>" value="#(sjjkhl7.sz??)" placeholder="回款汇率">
                            </div>
                            <div class="layui-input-inline">
                                #set( hkfs7 = xmjkmxMap==null ? null : xmjkmxMap.get("hkfs7") )
                                <input type="hidden" name="<EMAIL>" value="#(hkfs7.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="hkfs7">
                                <input type="text" class="layui-input" name="<EMAIL>" list="fsList" value="#(hkfs7.sz??)" placeholder="回款方式">
                            </div>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <div class="form-inline">
                            <label class="layui-form-label"></label>
                            <div class="layui-input-inline">
                                #set( hkje8 = xmjkmxMap==null ? null : xmjkmxMap.get("hkje8") )
                                <input type="hidden" name="<EMAIL>" value="#(hkje8.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="hkje8">
                                <input type="text" data-number class="layui-input" name="<EMAIL>" value="#(hkje8.sz??)" placeholder="回款金额">
                            </div>
                            <div class="layui-input-inline">
                                #set( hksj8 = xmjkmxMap==null ? null : xmjkmxMap.get("hksj8") )
                                <input type="hidden" name="<EMAIL>" value="#(hksj8.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="hksj8">
                                <input type="text" class="layui-input" name="<EMAIL>" value="#(hksj8.sz??)" autocomplete="off" placeholder="回款时间">
                            </div>
                            <div class="layui-input-inline">
                                #set( sjjkhl8 = xmjkmxMap==null ? null : xmjkmxMap.get("sjjkhl8") )
                                <input type="hidden" name="<EMAIL>" value="#(sjjkhl8.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="sjjkhl8">
                                <input type="text" class="layui-input" name="<EMAIL>" value="#(sjjkhl8.sz??)" placeholder="回款汇率">
                            </div>
                            <div class="layui-input-inline">
                                #set( hkfs8 = xmjkmxMap==null ? null : xmjkmxMap.get("hkfs8") )
                                <input type="hidden" name="<EMAIL>" value="#(hkfs8.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="hkfs8">
                                <input type="text" class="layui-input" name="<EMAIL>" list="fsList" value="#(hkfs8.sz??)" placeholder="回款方式">
                            </div>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <div class="form-inline">
                            <label class="layui-form-label"></label>
                            <div class="layui-input-inline">
                                #set( hkje9 = xmjkmxMap==null ? null : xmjkmxMap.get("hkje9") )
                                <input type="hidden" name="<EMAIL>" value="#(hkje9.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="hkje9">
                                <input type="text" data-number class="layui-input" name="<EMAIL>" value="#(hkje9.sz??)" placeholder="回款金额">
                            </div>
                            <div class="layui-input-inline">
                                #set( hksj9 = xmjkmxMap==null ? null : xmjkmxMap.get("hksj9") )
                                <input type="hidden" name="<EMAIL>" value="#(hksj9.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="hksj9">
                                <input type="text" class="layui-input" name="<EMAIL>" value="#(hksj9.sz??)" autocomplete="off" placeholder="回款时间">
                            </div>
                            <div class="layui-input-inline">
                                #set( sjjkhl9 = xmjkmxMap==null ? null : xmjkmxMap.get("sjjkhl9") )
                                <input type="hidden" name="<EMAIL>" value="#(sjjkhl9.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="sjjkhl9">
                                <input type="text" class="layui-input" name="<EMAIL>" value="#(sjjkhl9.sz??)" placeholder="回款汇率">
                            </div>
                            <div class="layui-input-inline">
                                #set( hkfs9 = xmjkmxMap==null ? null : xmjkmxMap.get("hkfs9") )
                                <input type="hidden" name="<EMAIL>" value="#(hkfs9.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="hkfs9">
                                <input type="text" class="layui-input" name="<EMAIL>" list="fsList" value="#(hkfs9.sz??)" placeholder="回款方式">
                            </div>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <div class="form-inline">
                            <label class="layui-form-label"></label>
                            <div class="layui-input-inline">
                                #set( hkje10 = xmjkmxMap==null ? null : xmjkmxMap.get("hkje10") )
                                <input type="hidden" name="<EMAIL>" value="#(hkje10.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="hkje10">
                                <input type="text" data-number class="layui-input" name="<EMAIL>" value="#(hkje10.sz??)" placeholder="回款金额">
                            </div>
                            <div class="layui-input-inline">
                                #set( hksj10 = xmjkmxMap==null ? null : xmjkmxMap.get("hksj10") )
                                <input type="hidden" name="<EMAIL>" value="#(hksj10.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="hksj10">
                                <input type="text" class="layui-input" name="<EMAIL>" value="#(hksj10.sz??)" autocomplete="off" placeholder="回款时间">
                            </div>
                            <div class="layui-input-inline">
                                #set( sjjkhl10 = xmjkmxMap==null ? null : xmjkmxMap.get("sjjkhl10") )
                                <input type="hidden" name="<EMAIL>" value="#(sjjkhl10.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="sjjkhl10">
                                <input type="text" class="layui-input" name="<EMAIL>" value="#(sjjkhl10.sz??)" placeholder="回款汇率">
                            </div>
                            <div class="layui-input-inline">
                                #set( hkfs10 = xmjkmxMap==null ? null : xmjkmxMap.get("hkfs10") )
                                <input type="hidden" name="<EMAIL>" value="#(hkfs10.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="hkfs10">
                                <input type="text" class="layui-input" name="<EMAIL>" list="fsList" value="#(hkfs10.sz??)" placeholder="回款方式">
                            </div>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <div class="form-inline">
                            <label class="layui-form-label"></label>
                            <div class="layui-input-inline">
                                #set( hkje11 = xmjkmxMap==null ? null : xmjkmxMap.get("hkje11") )
                                <input type="hidden" name="<EMAIL>" value="#(hkje11.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="hkje11">
                                <input type="text" data-number class="layui-input" name="<EMAIL>" value="#(hkje11.sz??)" placeholder="回款金额">
                            </div>
                            <div class="layui-input-inline">
                                #set( hksj11 = xmjkmxMap==null ? null : xmjkmxMap.get("hksj11") )
                                <input type="hidden" name="<EMAIL>" value="#(hksj11.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="hksj11">
                                <input type="text" class="layui-input" name="<EMAIL>" value="#(hksj11.sz??)" autocomplete="off" placeholder="回款时间">
                            </div>
                            <div class="layui-input-inline">
                                #set( sjjkhl11 = xmjkmxMap==null ? null : xmjkmxMap.get("sjjkhl11") )
                                <input type="hidden" name="<EMAIL>" value="#(sjjkhl11.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="sjjkhl11">
                                <input type="text" class="layui-input" name="<EMAIL>" value="#(sjjkhl11.sz??)" placeholder="回款汇率">
                            </div>
                            <div class="layui-input-inline">
                                #set( hkfs11 = xmjkmxMap==null ? null : xmjkmxMap.get("hkfs11") )
                                <input type="hidden" name="<EMAIL>" value="#(hkfs11.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="hkfs11">
                                <input type="text" class="layui-input" name="<EMAIL>" list="fsList" value="#(hkfs11.sz??)" placeholder="回款方式">
                            </div>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <div class="form-inline">
                            <label class="layui-form-label"></label>
                            <div class="layui-input-inline">
                                #set( hkje12 = xmjkmxMap==null ? null : xmjkmxMap.get("hkje12") )
                                <input type="hidden" name="<EMAIL>" value="#(hkje12.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="hkje12">
                                <input type="text" data-number class="layui-input" name="<EMAIL>" value="#(hkje12.sz??)" placeholder="回款金额">
                            </div>
                            <div class="layui-input-inline">
                                #set( hksj12 = xmjkmxMap==null ? null : xmjkmxMap.get("hksj12") )
                                <input type="hidden" name="<EMAIL>" value="#(hksj12.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="hksj12">
                                <input type="text" class="layui-input" name="<EMAIL>" value="#(hksj12.sz??)" autocomplete="off" placeholder="回款时间">
                            </div>
                            <div class="layui-input-inline">
                                #set( sjjkhl12 = xmjkmxMap==null ? null : xmjkmxMap.get("sjjkhl12") )
                                <input type="hidden" name="<EMAIL>" value="#(sjjkhl12.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="sjjkhl12">
                                <input type="text" class="layui-input" name="<EMAIL>" value="#(sjjkhl12.sz??)" placeholder="回款汇率">
                            </div>
                            <div class="layui-input-inline">
                                #set( hkfs12 = xmjkmxMap==null ? null : xmjkmxMap.get("hkfs12") )
                                <input type="hidden" name="<EMAIL>" value="#(hkfs12.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="hkfs12">
                                <input type="text" class="layui-input" name="<EMAIL>" list="fsList" value="#(hkfs12.sz??)" placeholder="回款方式">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="layui-tab-item">
                    <div class="layui-form-item">
                        <span>今日未回款汇率: 美元[<span class="copyMe">#(whkhlMap["美元"]??)</span>], 欧元[<span class="copyMe">#(whkhlMap["欧元"]??)</span>], 澳元[<span class="copyMe">#(whkhlMap["澳元"]??)</span>]</span>
                    </div>
                    <div class="layui-form-item">
                        <div class="form-inline">
                            <label class="layui-form-label">港杂</label>
                            <div class="layui-input-inline">
                                #set( gz = xmjkmxMap==null ? null : xmjkmxMap.get("gz") )
                                <input type="hidden" name="<EMAIL>" value="#(gz.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="gz">
                                <input type="text" class="layui-input yfjs" name="<EMAIL>" value="#(gz.sz??)" placeholder="港杂">
                            </div>
                            <div class="layui-input-inline">
                                #set( gzbz = xmjkmxMap==null ? null : xmjkmxMap.get("gzbz") )
                                <input type="hidden" name="<EMAIL>" value="#(gzbz.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="gzbz">
                                #@ss_select("<EMAIL>", ",美元,欧元,澳元,英镑,港币,元", gzbz.sz??)
                            </div>
                            <div class="layui-input-inline">
                                #set( gzhl = xmjkmxMap==null ? null : xmjkmxMap.get("gzhl") )
                                <input type="hidden" name="<EMAIL>" value="#(gzhl.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="gzhl">
                                <input type="text" class="layui-input yfjs" name="<EMAIL>" value="#(gzhl.sz??)" placeholder="港杂汇率">
                            </div>
                        </div>
                    </div>


                    <div class="layui-form-item">
                        <div class="form-inline">
                            <label class="layui-form-label">运费</label>
                            <div class="layui-input-inline">
                                #set( yf = xmjkmxMap==null ? null : xmjkmxMap.get("yf") )
                                <input type="hidden" name="<EMAIL>" value="#(yf.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="yf">
                                <input type="text" class="layui-input yfjs" name="<EMAIL>" value="#(yf.sz??)" placeholder="运费">
                            </div>
                            <div class="layui-input-inline">
                                #set( yfbz = xmjkmxMap==null ? null : xmjkmxMap.get("yfbz") )
                                <input type="hidden" name="<EMAIL>" value="#(yfbz.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="yfbz">
                                #@ss_select("<EMAIL>", ",美元,欧元,澳元,英镑,港币,元", yfbz.sz??)
                            </div>
                            <div class="layui-input-inline">
                                #set( yfhl = xmjkmxMap==null ? null : xmjkmxMap.get("yfhl") )
                                <input type="hidden" name="<EMAIL>" value="#(yfhl.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="yfhl">
                                <input type="text" class="layui-input yfjs" name="<EMAIL>" value="#(yfhl.sz??)" placeholder="运费汇率">
                            </div>
                        </div>
                    </div>


                    <div class="layui-form-item">
                        <div class="form-inline">
                            <label class="layui-form-label">清关</label>
                            <div class="layui-input-inline">
                                #set( qg = xmjkmxMap==null ? null : xmjkmxMap.get("qg") )
                                <input type="hidden" name="<EMAIL>" value="#(qg.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="qg">
                                <input type="text" class="layui-input yfjs" name="<EMAIL>" value="#(qg.sz??)" placeholder="清关">
                            </div>
                            <div class="layui-input-inline">
                                #set( qgbz = xmjkmxMap==null ? null : xmjkmxMap.get("qgbz") )
                                <input type="hidden" name="<EMAIL>" value="#(qgbz.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="qgbz">
                                #@ss_select("<EMAIL>", ",美元,欧元,澳元,英镑,港币,元", qgbz.sz??)
                            </div>
                            <div class="layui-input-inline">
                                #set( qghl = xmjkmxMap==null ? null : xmjkmxMap.get("qghl") )
                                <input type="hidden" name="<EMAIL>" value="#(qghl.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="qghl">
                                <input type="text" class="layui-input yfjs" name="<EMAIL>" value="#(qghl.sz??)" placeholder="清关汇率">
                            </div>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <div class="form-inline">
                            <label class="layui-form-label">送货</label>
                            <div class="layui-input-inline">
                                #set( sh = xmjkmxMap==null ? null : xmjkmxMap.get("sh") )
                                <input type="hidden" name="<EMAIL>" value="#(sh.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="sh">
                                <input type="text" class="layui-input yfjs" name="<EMAIL>" value="#(sh.sz??)" placeholder="送货">
                            </div>
                            <div class="layui-input-inline">
                                #set( shbz = xmjkmxMap==null ? null : xmjkmxMap.get("shbz") )
                                <input type="hidden" name="<EMAIL>" value="#(shbz.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="shbz">
                                #@ss_select("<EMAIL>", ",美元,欧元,澳元,英镑,港币,元", shbz.sz??)
                            </div>
                            <div class="layui-input-inline">
                                #set( shhl = xmjkmxMap==null ? null : xmjkmxMap.get("shhl") )
                                <input type="hidden" name="<EMAIL>" value="#(shhl.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="shhl">
                                <input type="text" class="layui-input yfjs" name="<EMAIL>" value="#(shhl.sz??)" placeholder="送货汇率">
                            </div>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <div class="form-inline">
                            <label class="layui-form-label">税金</label>
                            <div class="layui-input-inline">
                                #set( sj = xmjkmxMap==null ? null : xmjkmxMap.get("sj") )
                                <input type="hidden" name="<EMAIL>" value="#(sj.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="sj">
                                <input type="text" class="layui-input yfjs" name="<EMAIL>" value="#(sj.sz??)" placeholder="税金">
                            </div>
                            <div class="layui-input-inline">
                                #set( sjbz = xmjkmxMap==null ? null : xmjkmxMap.get("sjbz") )
                                <input type="hidden" name="<EMAIL>" value="#(sjbz.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="sjbz">
                                #@ss_select("<EMAIL>", ",美元,欧元,澳元,英镑,港币,元", sjbz.sz??)
                            </div>
                            <div class="layui-input-inline">
                                #set( sjhl = xmjkmxMap==null ? null : xmjkmxMap.get("sjhl") )
                                <input type="hidden" name="<EMAIL>" value="#(sjhl.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="sjhl">
                                <input type="text" class="layui-input yfjs" name="<EMAIL>" value="#(sjhl.sz??)" placeholder="税金汇率">
                            </div>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <div class="form-inline">
                            <label class="layui-form-label">汇总运费</label>
                            <div class="layui-input-inline">
                                #set( hzyf = xmjkmxMap==null ? null : xmjkmxMap.get("hzyf") )
                                <input type="hidden" name="<EMAIL>" value="#(hzyf.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="hzyf">
                                <input type="text" class="layui-input" name="<EMAIL>" value="#(hzyf.sz??)" placeholder="汇总运费, 人民币">
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="form-inline">
                            <label class="layui-form-label">是否支付</label>
                            <div class="layui-input-inline" style="width:90px;">
                                <select id="gzzf" name="gzzf" lay-filter="gzzfCheck">
                                    <option value=""></option>
                                    <option value="已支付" #if(dd.gzzf??=="已支付") selected #end>已支付</option>
                                    <option value="未支付" #if(dd.gzzf??=="未支付") selected #end>未支付</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="form-inline">
                            <label class="layui-form-label">支付金额1</label>
                            <div class="layui-input-inline" style="width:80px;">
                                <input type="text" data-number name="gzzfje" value="#(dd.gzzfje??)" placeholder="支付金额1" autocomplete="off" class="layui-input">
                            </div>
                            <div class="layui-input-inline" style="width:100px;">
                                <input type="text" name="gzzfsj" id="gzzfsj" value="#(dd.gzzfsj??)" placeholder="支付时间1" autocomplete="off" class="layui-input">
                            </div>
                            <div class="layui-input-inline" style="width:100px;">
                                <input type="text" name="gzzffs1" id="gzzffs1" value="#(dd.gzzffs1??)" placeholder="支付方式1" autocomplete="off" class="layui-input" list="fsList">
                                <datalist style="display: none" id="fsList">
                                    <option>建行卡</option>
                                    <option>杨鹏卡</option>
                                    <option>秋卡</option>
                                    <option>建行公户</option>
                                    <option>小柳</option>
                                    <option>交行（公）</option>
                                    <option>信用社（公）</option>
                                    <option>壹林（中行）</option>
                                    <option>交行（林）</option>
                                </datalist>
                            </div>
                            <div class="layui-input-inline" style="width:100px;">
                                <input type="text" name="gzzfkm1" id="gzzfkm1" value="#(dd.gzzfkm1??'其他应付款')" placeholder="支付科目1" autocomplete="off" class="layui-input" list="kmList">
                                <datalist style="display: none" id="kmList">
                                    #for(km:kmList)
                                    <option>#(km??)</option>
                                    #end
                                </datalist>
                            </div>
                            <div class="layui-input-inline" style="width:120px;">
                                <input type="text" name="gzzfejkm1" id="gzzfejkm1" value="#(dd.gzzfejkm1??'港杂费')" placeholder="支付二级科目1" autocomplete="off" class="layui-input" list="ejkmList">
                                <datalist style="display: none" id="ejkmList">
                                    #for(ejkm:ejkmList)
                                    <option>#(ejkm??)</option>
                                    #end
                                </datalist>
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="form-inline">
                            <label class="layui-form-label">支付金额2</label>
                            <div class="layui-input-inline" style="width:80px;">
                                <input type="text" data-number name="gzzfje2" value="#(dd.gzzfje2??)" placeholder="支付金额2" autocomplete="off" class="layui-input">
                            </div>
                            <div class="layui-input-inline" style="width:100px;">
                                <input type="text" name="gzzfsj2" id="gzzfsj2" value="#(dd.gzzfsj2??)" placeholder="支付时间2" autocomplete="off" class="layui-input">
                            </div>
                            <div class="layui-input-inline" style="width:100px;">
                                <input type="text" name="gzzffs2" id="gzzffs2" value="#(dd.gzzffs2??)" placeholder="支付方式2" autocomplete="off" class="layui-input" list="fsList">
                            </div>
                            <div class="layui-input-inline" style="width:100px;">
                                <input type="text" name="gzzfkm2" id="gzzfkm2" value="#(dd.gzzfkm2??'其他应付款')" placeholder="支付科目2" autocomplete="off" class="layui-input" list="kmList">
                            </div>
                            <div class="layui-input-inline" style="width:120px;">
                                <input type="text" name="gzzfejkm2" id="gzzfejkm2" value="#(dd.gzzfejkm2??'港杂费')" placeholder="支付二级科目2" autocomplete="off" class="layui-input" list="ejkmList">
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="form-inline">
                            <label class="layui-form-label">支付金额3</label>
                            <div class="layui-input-inline" style="width:80px;">
                                <input type="text" data-number name="gzzfje3" value="#(dd.gzzfje3??)" placeholder="支付金额3" autocomplete="off" class="layui-input">
                            </div>
                            <div class="layui-input-inline" style="width:100px;">
                                <input type="text" name="gzzfsj3" id="gzzfsj3" value="#(dd.gzzfsj3??)" placeholder="支付时间3" autocomplete="off" class="layui-input">
                            </div>
                            <div class="layui-input-inline" style="width:100px;">
                                <input type="text" name="gzzffs3" id="gzzffs3" value="#(dd.gzzffs3??)" placeholder="支付方式3" autocomplete="off" class="layui-input" list="fsList">
                            </div>
                            <div class="layui-input-inline" style="width:100px;">
                                <input type="text" name="gzzfkm3" id="gzzfkm3" value="#(dd.gzzfkm3??'其他应付款')" placeholder="支付科目3" autocomplete="off" class="layui-input" list="kmList">
                            </div>
                            <div class="layui-input-inline" style="width:120px;">
                                <input type="text" name="gzzfejkm3" id="gzzfejkm3" value="#(dd.gzzfejkm3??'港杂费')" placeholder="支付二级科目3" autocomplete="off" class="layui-input" list="ejkmList">
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="form-inline">
                            <label class="layui-form-label">支付金额4</label>
                            <div class="layui-input-inline" style="width:80px;">
                                <input type="text" data-number name="gzzfje4" value="#(dd.gzzfje4??)" placeholder="支付金额4" autocomplete="off" class="layui-input">
                            </div>
                            <div class="layui-input-inline" style="width:100px;">
                                <input type="text" name="gzzfsj4" id="gzzfsj4" value="#(dd.gzzfsj4??)" placeholder="支付时间4" autocomplete="off" class="layui-input">
                            </div>
                            <div class="layui-input-inline" style="width:100px;">
                                <input type="text" name="gzzffs4" id="gzzffs4" value="#(dd.gzzffs4??)" placeholder="支付方式4" autocomplete="off" class="layui-input" list="fsList">
                            </div>
                            <div class="layui-input-inline" style="width:100px;">
                                <input type="text" name="gzzfkm4" id="gzzfkm4" value="#(dd.gzzfkm4??'其他应付款')" placeholder="支付科目4" autocomplete="off" class="layui-input" list="kmList">
                            </div>
                            <div class="layui-input-inline" style="width:120px;">
                                <input type="text" name="gzzfejkm4" id="gzzfejkm4" value="#(dd.gzzfejkm4??'港杂费')" placeholder="支付二级科目4" autocomplete="off" class="layui-input" list="ejkmList">
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="form-inline">
                            <label class="layui-form-label">支付金额5</label>
                            <div class="layui-input-inline" style="width:80px;">
                                <input type="text" data-number name="gzzfje5" value="#(dd.gzzfje5??)" placeholder="支付金额5" autocomplete="off" class="layui-input">
                            </div>
                            <div class="layui-input-inline" style="width:100px;">
                                <input type="text" name="gzzfsj5" id="gzzfsj5" value="#(dd.gzzfsj5??)" placeholder="支付时间5" autocomplete="off" class="layui-input">
                            </div>
                            <div class="layui-input-inline" style="width:100px;">
                                <input type="text" name="gzzffs5" id="gzzffs5" value="#(dd.gzzffs5??)" placeholder="支付方式5" autocomplete="off" class="layui-input" list="fsList">
                            </div>
                            <div class="layui-input-inline" style="width:100px;">
                                <input type="text" name="gzzfkm5" id="gzzfkm5" value="#(dd.gzzfkm5??'其他应付款')" placeholder="支付科目5" autocomplete="off" class="layui-input" list="kmList">
                            </div>
                            <div class="layui-input-inline" style="width:120px;">
                                <input type="text" name="gzzfejkm5" id="gzzfejkm5" value="#(dd.gzzfejkm5??'港杂费')" placeholder="支付二级科目5" autocomplete="off" class="layui-input" list="ejkmList">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="layui-tab-item">
                    <div class="layui-form-item">
                        <div class="form-inline">
                            <label class="layui-form-label">DDU运费</label>
                            <div class="layui-input-inline">
                                #set( dduyf = xmjkmxMap==null ? null : xmjkmxMap.get("dduyf") )
                                <input type="hidden" name="<EMAIL>" value="#(dduyf.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="dduyf">
                                <input type="text" class="layui-input" name="<EMAIL>" value="#(dduyf.sz??)" placeholder="DDU运费">
                            </div>
                        </div>
                        <div class="form-inline">
                            <label class="layui-form-label">税金代缴</label>
                            <div class="layui-input-inline">
                                #set( sjdj = xmjkmxMap==null ? null : xmjkmxMap.get("sjdj") )
                                <input type="hidden" name="<EMAIL>" value="#(sjdj.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="sjdj">
                                <input type="text" class="layui-input" name="<EMAIL>" value="#(sjdj.sz??)" placeholder="税金代缴">
                            </div>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <div class="form-inline">
                            <label class="layui-form-label">提成海运费</label>
                            <div class="layui-input-inline">
                                #set( tchyf = xmjkmxMap==null ? null : xmjkmxMap.get("tchyf") )
                                <input type="hidden" name="<EMAIL>" value="#(tchyf.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="tchyf">
                                <input type="text" class="layui-input" name="<EMAIL>" value="#(tchyf.sz??)" placeholder="提成海运费">
                            </div>
                        </div>
                        <div class="form-inline">
                            <label class="layui-form-label">提成索赔</label>
                            <div class="layui-input-inline">
                                #set( tcsp = xmjkmxMap==null ? null : xmjkmxMap.get("tcsp") )
                                <input type="hidden" name="<EMAIL>" value="#(tcsp.id??)">
                                <input type="hidden" name="<EMAIL>" value="#(jkid??)">
                                <input type="hidden" name="<EMAIL>" value="tcsp">
                                <input type="text" class="layui-input" name="<EMAIL>" value="#(tcsp.sz??)" placeholder="提成索赔">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>

</div>
#end
#end

#define css()
<style>
    .copyMe {
        color: red;
    }
</style>
#end

#define js()
<script type="text/javascript">
    var copyMeElements = document.querySelectorAll('.copyMe');
    for (var i = 0; i < copyMeElements.length; i++) {
        copyMeElements[i].addEventListener('click', function(event) {
            var text = event.target.innerText;
            navigator.clipboard.writeText(text).then(function() {
                console.log('Async: Copying to clipboard was successful!');
            }, function(err) {
                console.error('Async: Could not copy text: ', err);
            });
        });
    }
    function submitForm() {
        let s = mnjs();
        var hzyf = math.evaluate(s);
        console.log(hzyf);
        let zhj = document.getElementsByName("<EMAIL>")[0].value;
        var cha = math.evaluate(hzyf + "-" + zhj);
        $("input[data-number]").each(function() {
            var vaIn = $(this).val();
            if (vaIn && !isIntNumber(vaIn) && !isFloatNumber(vaIn)) {
                showFailMsg(vaIn + " 不能填非数字!");
                return false;
            }
        });
        if (Math.abs(cha) > 10) {
            return confirm("海运费汇总，实际填写和模拟计算差值为" + cha + "，确认保存？");
        }
    }

    $(document).ready(function () {

        $("input[name^='xmjkmxList@hksj']").each(function () {
            layui.laydate.render({
                elem: this
            });
        });

        $("#myArticleForm").ajaxForm({
            dataType: "json"
            , success: function (ret) {
                layer_alert_with_callback(ret.msg, ret.state, "/my/order/fin?ddbh="+ret.ddbh);
            }
            , error: function (ret) {
                layer_alert(ret);
            }
        });
    });

    function mnjs() {
        var gz = $("input[name='<EMAIL>']").val();
        var yf = $("input[name='<EMAIL>']").val();
        var qg = $("input[name='<EMAIL>']").val();
        var sh = $("input[name='<EMAIL>']").val();
        var sj = $("input[name='<EMAIL>']").val();
        var gzhl = $("input[name='<EMAIL>']").val();
        var yfhl = $("input[name='<EMAIL>']").val();
        var qghl = $("input[name='<EMAIL>']").val();
        var shhl = $("input[name='<EMAIL>']").val();
        var sjhl = $("input[name='<EMAIL>']").val();

        gz = (gz === "") ? "0" : gz;
        yf = (yf === "") ? "0" : yf;
        qg = (qg === "") ? "0" : qg;
        sh = (sh === "") ? "0" : sh;
        sj = (sj === "") ? "0" : sj;
        gzhl = (gzhl === "") ? "0" : gzhl;
        yfhl = (yfhl === "") ? "0" : yfhl;
        qghl = (qghl === "") ? "0" : qghl;
        shhl = (shhl === "") ? "0" : shhl;
        sjhl = (sjhl === "") ? "0" : sjhl;

        var s = gz + ' * ' + gzhl + ' + ' + yf + ' * ' + yfhl + ' + ' + qg + ' * ' + qghl + ' + ' + sh + ' * ' + shhl + "+" + sj + "*" + sjhl;
        return s;
    }

    function jsyf() {
        var s = mnjs();
        var hzyf = math.evaluate(s);
        document.getElementsByName("<EMAIL>")[0].value = hzyf;
    }

    $(".yfjs").on('keyup', jsyf);



    layui.use('laydate', function () {
        var laydate = layui.laydate;

        laydate.render({
            elem: '#gzzfsj'
        });
        laydate.render({
            elem: '#gzzfsj2'
        });
        laydate.render({
            elem: '#gzzfsj3'
        });
        laydate.render({
            elem: '#gzzfsj4'
        });
        laydate.render({
            elem: '#gzzfsj5'
        });
    });
</script>
#end