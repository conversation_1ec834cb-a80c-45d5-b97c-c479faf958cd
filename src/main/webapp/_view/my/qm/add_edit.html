#set(seoTitle="签名管理 " + (isAdd?"创建":"编辑"))
#@layout()
#define main()
#include("/_view/my/common/_my_menu_bar.html")

#define content()
<div>
    <div class="jf-breadcrumb-box">
        <ol class="jf-breadcrumb">
            <li><a href="/">首页</a></li>
            <li><a href="/my/qm">签名</a></li>
            <li class="active">#(isAdd ? "创建签名" : "编辑签名")</li>
        </ol>
    </div>
    <br>
    <br>
    <form class="layui-form" action="/my/qm/#(isAdd ? 'save' : 'update')" method="post">
        <input type="hidden" name="qm.id" value="#(qm.id??)">

        <div class="layui-col-sm4" style="margin-bottom: 10px;">
            名称 <input type="text" class="layui-input" id="qm.mc" name="qm.mc" value="#(qm.mc??)">
        </div>
        <div class="layui-col-sm4" style="margin-bottom: 10px;">
            是否公用
            <select lay-verify="required" lay-search="" id="qm.sfgy" name="qm.sfgy" >
                <option #if(qm.sfgy??=="是") selected #end>是</option>
                <option #if(qm.sfgy??=="否") selected #end>否</option>
            </select>
        </div>
        <div class="layui-col-sm4" style="margin-bottom: 10px;">
            分类 <input type="text" class="layui-input" id="qm.fl" name="qm.fl" value="#(qm.fl??)">
        </div>

        <div class="layui-col-sm12" style="margin-bottom: 10px;">
            内容
            <textarea class="layui-textarea" id="qm.nr" name="qm.nr" cols="100" rows="20">#(qm.nr??)</textarea>
        </div>

        <div class="layui-form-item">
             <div class="layui-input-inline">
                <input class="layui-btn layui-btn-sm" type="submit" value="提交"/>
            </div>
        </div>
    </form>
</div>
#end
#end

#define js()
<script type="text/javascript">
    $(document).ready(function () {
        $("form").ajaxForm({
            dataType: "json"
            , success: function (ret) {
                layer_alert_with_callback(ret.msg, ret.state, "/my/qm/edit?id="+ret.id);
            }
            , error: function (data) {
                layer_alert("网络操作失败，请咨询老柳! QQ75272683");
            }
        });
    });
</script>
#end
