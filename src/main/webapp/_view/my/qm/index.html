#set(seoTitle="签名管理")
#@layout()
#define main()
#include("/_view/my/common/_my_menu_bar.html")

    #define content()
	<div>
		<div class="jf-breadcrumb-box">
			<ol class="jf-breadcrumb">
				<li><a href="/">首页</a></li>
				<li class="active">签名</li>
			</ol>
			<div class="layui-col8">
				<form method="post" action="/my/qm">
					<input type="text" name="query" placeholder="名称" size="10">
					<input type="text" id="queryKsrq" name="queryKsrq" placeholder="开始日期" autocomplete="off" size="12">
					<input type="text" id="queryJsrq" name="queryJsrq" placeholder="结束日期" autocomplete="off" size="12">
					<select lay-verify="required" lay-search="" name="queryPx">
						<option value=" order by mc desc">名称倒序</option>
						<option value=" order by mc asc">名称顺序</option>
					</select>
					<input class="layui-btn layui-btn-sm" type="submit" value="查找">
				</form>
			</div>
			<div class="jf-btn-box">
				<a class="layui-btn layui-btn-sm" href="/my/qm/add" >创&nbsp;&nbsp;建</a>
			</div>
		</div>
		<br>
		<table  class="layui-table" lay-size="sm">
			<thead>
				<tr>
					<th>名称</th>
					<th>内容</th>
					<th>是否公用</th>
					<th>分类</th>
					<th>创建人</th>
					<th>创建时间</th>
					<th >操作</th>
				</tr>
			</thead>
			<tbody>
			#for(x : page.list)
				<tr>
					<td>#(x.mc??)</td>
					<td>#(x.nr??)</td>
					<td>#(x.sfgy??)</td>
					<td>#(x.fl??)</td>
					<td>#(x.cjr??)</td>
					<td>#(x.cjsj??)</td>
					<td>
						<div class="layui-btn-group">
							<a class="layui-btn layui-btn-sm" href="/my/qm/edit?id=#(x.id)" target="_blank">编辑</a>&nbsp;&nbsp;
							#role("权限管理员", "超级管理员", "总经理")
							<span class="layui-btn layui-btn-sm" onclick="confirmAjaxLayer('删除 #escape(x.mc??) 后无法恢复，确定要删除？', '/my/qm/delete?id=#(x.id)', this);">删除</span>
							#end
						</div>
					</td>
				</tr>
			#end
			</tbody>
		</table>
		#@paginate(page.pageNumber, page.totalPage, "/my/qm?query=" + query + "&p=")
	</div>
	#end
#end
#define js()
<script>
	layui.use('laydate', function() {
		var laydate = layui.laydate;

		laydate.render({
			elem: '#queryKsrq'
		});
		laydate.render({
			elem: '#queryJsrq'
		});
	});
</script>
#end