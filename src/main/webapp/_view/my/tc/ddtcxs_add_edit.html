#set(seoTitle="提成管理 " + (isAdd?"创建":"编辑"))
#@layout()
#define main()
#include("/_view/my/common/_my_menu_bar.html")

#define content()
    <div>&nbsp;
        <div class="jf-breadcrumb-box">
            <ol class="jf-breadcrumb">
                <li><a href="/">首页</a></li>
                <li id="financeModule" class="clickable-menu-item"><i class="layui-icon layui-icon-rmb"></i><button class="layui-btn layui-btn-sm" onclick="openModuleModal('财务模块')">财务模块</button></li>
                <li><a href="/my/tc">提成系数管理</a></li>
                <li><a href="/my/tc/ddtcxsIndex">订单提成系数管理</a></li>
                <li class="active">#(isAdd ? "创建提成管理" : "编辑提成管理")</li>
            </ol>
        </div>
        <br>
        <br>
        <form class="layui-form" action="/my/tc/#(isAdd ? 'ddtcxsSave' : 'ddtcxsUpdate')" method="post">
            <input type="hidden" name="ddtcxs.id" value="#(ddtcxs.id??)">
    
            <div class="layui-form-item">
                <label class="layui-form-label">订单编号</label>
                 <div class="layui-input-inline">
                    <select lay-verify="required" lay-search="" id="ddtcxs.ddbh" name="ddtcxs.ddbh">
                        <option></option>
                        #for( d : ddbhList)
                        <option value="#(d.ddbh??)" #if(d.ddbh??==ddtcxs.ddbh??) selected #end>#(d.ddbh??)</option>
                        #end
                    </select>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">业务员</label>
                 <div class="layui-input-inline">
                     #@select("ddtcxs.ywyid","account","id","xm"," and status>0 and (bq is null or bq <>'WDS') ", ddtcxs.ywyid??)
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">提成系数</label>
                 <div class="layui-input-inline">
                    <input type="text" class="layui-input" id="ddtcxs.xs" name="ddtcxs.xs" value="#(ddtcxs.xs??'0.01')" placeholder="1%即输入0.01">
                </div>
            </div>
            <div class="layui-form-item">
                 <div class="layui-input-inline" style="margin-left: 100px;">
                    <input class="layui-btn layui-btn-sm" type="submit" value="提交"/>
                </div>
            </div>
        </form>
    </div>
#end
#end

#define js()
<script type="text/javascript">
    $(document).ready(function () {
        $("form").ajaxForm({
            dataType: "json"
            , success: function (ret) {
                layer_alert_with_callback(ret.msg, ret.state, "/my/tc/ddtcxsIndex?p=#(p??1)");
            }
            , error: function (data) {
                layer_alert("网络操作失败，请咨询老柳! QQ75272683");
            }
        });
    });
</script>
#end
