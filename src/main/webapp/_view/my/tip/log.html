#@layout()
#define main()
<!-- 个人空间左侧菜单栏 -->
#include("/_view/my/common/_my_menu_bar.html")

<!-- 内容容器 -->
    #define content()
    <!-- 项目 -->
    <div>
        <div class="jf-breadcrumb-box">
            <ol class="jf-breadcrumb">
                <li><a href="/">首页</a></li>
                <li id="yilinReportModule" class="clickable-menu-item"><i class="layui-icon layui-icon-chart-screen"></i><button class="layui-btn layui-btn-sm" onclick="openModuleModal('壹林报表')">壹林报表</button></li>
                <li><a href="/my/tip">我的提醒</a></li>
                <li class="active">执行记录</li>
            </ol>
        </div>

        <ul class="jf-my-article-list">
            #for(x : logList)
            <li>
                <div class="jf-panel-item">
                    <p>
                        #(x.taskName??)
                    </p>
                </div>
                <div class="jf-panel-item">
                    <p>
                        #(x.createAt??)
                    </p>
                </div>
            </li>
            #end
        </ul>
    </div>
    #end
#end