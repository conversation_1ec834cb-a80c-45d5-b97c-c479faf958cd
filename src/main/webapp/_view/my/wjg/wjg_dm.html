#set(seoTitle="大磨外加工管理") #@layout() #define main() #include("/_view/my/common/_my_menu_bar.html") #define content()
<div>
  <div class="jf-breadcrumb-box">
    <ol class="jf-breadcrumb">
      <li><a href="/">首页</a></li>
      <li id="financeModule" class="clickable-menu-item"><i class="layui-icon layui-icon-rmb"></i><button class="layui-btn layui-btn-sm" onclick="openModuleModal('财务模块')">财务模块</button></li>
      <li class="active"><a href="/my/wjg" class="layui-btn layui-btn-sm">外加工和喷砂</a></li>
      <li><a href="/my/wjg/index_qt?table=wjg_dm" class="layui-btn layui-btn-sm layui-btn-danger">大磨</a></li>
      <li><a href="/my/wjg/index_qt?table=wjg_rm" class="layui-btn layui-btn-sm">软磨</a></li>
      <li><a href="/my/wjg/index_qt?table=wjg_zx" class="layui-btn layui-btn-sm">造型</a></li>
      <li><a href="/my/wjg/clfj" class="layui-btn layui-btn-sm">材料附加</a></li>
      <li><a href="/my/wjg/zhcx" class="layui-btn layui-btn-sm">综合查询</a></li>
    </ol>
    <div class="jf-btn-box">
      <!--				<a class="layui-btn layui-btn-sm" href="/my/wjg/add" >创&nbsp;&nbsp;建</a>-->
      <a class="layui-btn layui-btn-sm" href="/my/wjg/fixError?table=wjg_dm">错误更正(#(cws??))</a>
    </div>
  </div>
  <br />
  <form class="layui-form" method="post" action="/my/wjg/index_qt?table=wjg_dm">
    <div class="layui-row layui-col-space10 layui-form-item">
      <div class="layui-col-lg1"><input class="layui-input" type="text" value="#(mhcx??)" name="mhcx" placeholder="模糊查询" /></div>
      <div class="layui-col-lg1"><input class="layui-input" type="text" value="#(queryYf??)" id="queryYf" name="queryYf" placeholder="输入月份查询" /></div>
      <div class="layui-col-lg1"><input class="layui-input" type="text" value="#(queryZx??)" id="queryZx" name="queryZx" placeholder="最小金额" /></div>
      <div class="layui-col-lg1"><input class="layui-input" type="text" value="#(queryZd??)" id="queryZd" name="queryZd" placeholder="最大金额" /></div>
      <div class="layui-col-lg1"><input class="layui-input" type="text" value="#(query??)" name="query" placeholder="输入订单查询" /></div>
      <div class="layui-col-lg1"><input class="layui-input" type="text" value="#(queryGr??)" name="queryGr" placeholder="输入工人查询" /></div>
      <div class="layui-col-lg1"><input class="layui-input" type="text" value="#(queryPm??)" name="queryPm" placeholder="输入品名查询" /></div>
      <div class="layui-col-lg1"><input class="layui-input" type="text" value="#(queryGcdd??)" name="queryGcdd" placeholder="输入工程名称" /></div>
      <div class="layui-col-lg3">
        <button class="layui-btn layui-btn-sm" lay-submit lay-filter="component-form-element">查询</button>
        <a href="#" onclick="doExport('#myTable01', {type: 'xlsx', htmlHyperlink: 'content'});"> <img src="/assets/img/xls.png" alt="XLSX" style="width: 24px" /></a>
        <a class="layui-btn layui-btn-sm" href="/download/大磨导入模版.xls" target="_blank"> 大磨导入模版</a>
      </div>
    </div>
    <div class="layui-row layui-col-space10 layui-form-item">
      <div class="layui-col-lg1"><input class="layui-input layui-required" style="width: 150px" type="text" id="yf" name="yf" placeholder="输入导入月份" /></div>
      <div class="layui-col-lg6">
        <button type="button" class="layui-btn layui-btn-sm" id="uploadFile"><i class="layui-icon"></i>导入</button>
      </div>
    </div>
    <div class="layui-row" id="errorMsg"></div>
  </form>
  <table class="layui-table" lay-size="sm" id="myTable01">
    <thead>
      <tr>
        <th>月份</th>
        <th>加工方式</th>
        <th>订单编号</th>
        <th>订单ID</th>
        <th>工程名称</th>
        <th>客户订单</th>
        <th>工人</th>
        <th>石种</th>
        <th>品名</th>
        <th>Size A*B*C</th>
        <th>数量</th>
        <th>才数</th>
        <th>单价</th>
        <th>金额</th>
        <th>备注</th>
        <th>操作</th>
      </tr>
    </thead>
    <tbody>
      #for(x : page.list)
      <tr>
        <td>#(x.yf??)</td>
        <td>#(x.jgfs??)</td>
        <td><a class="layui-btn layui-btn-sm" href="/my/dd/editShtz?id=#(x.id??)&ddbh=#(x.ddbh??)&type=sht" target="_blank">#if(x.yddbh??)<font style="color: red">*</font>#end#(x.ddbh??)</a></td>
        <td>#(x.ddid??)</td>
        <td>
          <button onclick="showPic('#(x.ddbh??)', '#(x.gcdd??)', '#(x.gcbh??)')" class="layui-btn layui-btn-sm layui-btn-normal"><i class="layui-icon layui-icon-picture"></i> #(x.gcdd??x.gcbh??)</button>
        </td>
        <td>#(x.khddh??)</td>
        <td>#(x.gh??)</td>
        <td>#(x.sz??)</td>
        <td>#(x.pm??)</td>
        <td>#(x.sizea??)*#(x.sizeb??)*#(x.sizec??)</td>
        <td>#(x.sl??)</td>
        <td>#(x.cs??)</td>
        <td>#(x.dj??)</td>
        <td>#(x.je??)</td>
        <td>#(x.bz??)</td>
        <td>
          <div class="layui-btn-group">
            <a class="layui-btn layui-btn-sm" href="/my/wjg/fj_edit?table=wjg_dm&id=#(x.id)" target="_blank">编辑</a>&nbsp;&nbsp; #role("权限管理员", "超级管理员", "总经理")
            <span class="layui-btn layui-btn-sm" onclick="confirmAjaxLayer('删除 #escape(x.gcdd??) #escape(x.jglx??) 后无法恢复，确定要删除？', '/my/wjg/delete?table=wjg_dm&id=#(x.id)', this);">删除</span>
            #end
          </div>
        </td>
      </tr>
      #end
    </tbody>
  </table>
  #@paginate(page.pageNumber, page.totalPage, "/my/wjg/index_qt?table=wjg_dm&query=" + query + "&queryZx=" + queryZx + "&queryZd=" + queryZd + "&queryYf=" + queryYf + "&queryLx=" + queryLx + "&queryGr=" + queryGr + "&queryPm=" + queryPm + "&queryGcdd=" + queryGcdd + "&queryPx=" + queryPx + "&p=")
</div>
#end #end #define js()
<script type="application/javascript" src="/assets/export/js-xlsx/xlsx.core.min.js"></script>
<script type="application/javascript" src="/assets/export/tableExport.min.js"></script>

<script type="text/javascript">
  function showPic(ddbh, gcdd, gcbh) {
    if (!ddbh || (!gcbh && !gcdd)) {
      layer.msg("订单编号或工程编号不能为空", { icon: 2 });
      return;
    }

    // 调用后端接口获取图片
    $.get("/my/dd/getDrawingPic", { ddbh: ddbh, gcdd: gcdd, gcbh: gcbh }, function (res) {
      var images = Array.isArray(res.data) ? res.data : [res.data];
      var content = '<div style="text-align:right;margin-bottom:10px;"><button class="layui-btn layui-btn-sm" onclick="showAllDraws(\'' + res.id + '\', \'' + res.ddbh + '\');">查看所有图纸</button></div>';

      if (!images.length || (images.length === 1 && !images[0])) {
        content += '<div style="text-align:center;padding:20px;"><p>暂无该工程相关图纸，可以点击右上角查看该订单的所有图纸</p></div>';
      } else {
        content += '<div class="layui-carousel" id="drawing-carousel"><div carousel-item>';
        images.forEach(function (imgSrc) {
          var fileName = imgSrc.split("/").pop();
          content += '<div style="text-align:center;"><img src="' + imgSrc + '" style="max-width:100%;max-height:100%;cursor:pointer;" onclick="layer.photos({photos: {title: \'查看大图\',data: [{src: \'' + imgSrc + '\'}]},anim: 5});"><div style="margin-top:10px;font-size:14px;">' + fileName + "</div></div>";
        });
        content += "</div></div>";
      }
      // 使用layer弹出层展示图片
      layer.open({
        type: 1,
        title: ddbh + ", 工程图纸 - " + gcbh,
        area: ["80%", "80%"],
        shadeClose: true,
        content: content,
        success: function () {
          // 初始化轮播
          layui.carousel.render({
            elem: "#drawing-carousel",
            width: "100%",
            height: "100%",
            arrow: "always",
            autoplay: false,
          });
        },
      });
    });
  }
  function showAllDraws(id, ddbh) {
    if (!id) {
      layer.msg("订单编号不能为空", {icon: 2});
      return;
    }
    // 打开新窗口显示所有图纸
    window.open("/my/dd/showDraws?id=" + id + "&ddbh=" + ddbh, "_blank");
  }

  layui.use(["laydate", "form", "layer"], function () {
    var laydate = layui.laydate;
    var form = layui.form;
    var layer = layui.layer;

    laydate.render({
      elem: "#yf",
      format: "yyyyMM",
      type: "month",
      range: false,
    });
    laydate.render({
      elem: "#queryYf",
      format: "yyyyMM",
      type: "month",
      range: false,
    });

    // 监听表单提交事件
    form.on("submit(component-form-element)", function (data) {
      // 显示加载层
      var index = layer.msg("查询中...", { icon: 16, shade: 0.3, time: 0 }); // 修改为文本提示“查询中”

      // 阻止表单默认提交
      return true;
    });
  });
  var layerIndex;
  layui.upload.render({
    elem: "#uploadFile",
    url: "/my/wjg/importDmFile",
    data: {
      yf: function () {
        return $("#yf").val();
      },
    },
    accept: "file",
    timeout: 300000,
    before: function () {
      var val = document.getElementById("yf").value;
      if (val === undefined || val === "" || val.length < 6) {
        layer.msg("月份不能放空", {
          icon: 5,
          anim: 6,
        });
        document.getElementById("yf").focus();
        return false;
      }
      layerIndex = layer.load(0);
      layer.msg("导入中，请稍后...");
    },
    done: function (res) {
      layer.close(layerIndex);
      console.log(res);
      if (res.errorMsg != undefined && res.errorMsg != null && res.errorMsg != "") {
        document.getElementById("errorMsg").innerHTML = res.errorMsg + "<br> 请手动刷新!";
      } else {
        if (res.state == "ok") {
          layer_alert("导入成功!");
        }
        if (res.message) {
          layer_alert(res.message);
        } else if (res.msg) {
          layer_alert(res.msg);
        }
        // location.reload();
      }
    },
  });

  function doExport(selector, params) {
    var options = {
      fileName: "外加工-大磨",
    };
    jQuery.extend(true, options, params);
    $(selector).tableExport(options);
  }
</script>
#end
