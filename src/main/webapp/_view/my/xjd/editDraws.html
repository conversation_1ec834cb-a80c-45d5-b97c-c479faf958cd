#set(seoTitle="图纸编辑")
#@layout()
#define main()
#include("/_view/my/common/_my_menu_bar.html")

#define content()
<div style="width: 80%; margin-left: 30px;">
    <div class="jf-breadcrumb-box">
        <ol class="jf-breadcrumb">
            <li><a href="/">首页</a></li>
            <li id="businessRelated" class="clickable-menu-item"><i class="layui-icon layui-icon-form"></i><button class="layui-btn layui-btn-sm" onclick="openModuleModal('业务相关')">业务相关</button></li>
            <li><a href="/my/xjd">询价单</a></li>
            <li><a href="/my/xjd/edit?id=#(id??)">#(ddbh??)</a></li>
            <li class="active">图纸编辑</li>
        </ol>
        <div class="jf-btn-box">
<!--            <button class="layui-btn layui-btn-sm" onclick="saveAll()">全部图片保存</button>-->
            <a class="layui-btn layui-btn-sm" href="/my/yxkh/edit?id=#(yxkh.id??)" target="_blank">客户:#(yxkh.gsmc??)</a>
            <a class="layui-btn layui-btn-sm" onclick="tpscwc('#(id??)', '#(ddbh??)')" href="javascript:void(0);">已传好图纸[邮件知会相关同事帮填附加费]</a>
            <a class="layui-btn layui-btn-sm" onclick="fjfwc('#(id??)', '#(ddbh??)')" href="javascript:void(0);">已填完附加费[邮件知会相关同事]</a>
            <a class="layui-btn layui-btn-sm" href="/my/xjd/showDraws?id=#(id??)&ddbh=#(ddbh??)" target="_blank">单页查看所有</a>
        </div>

    </div>
    <br>
    <div><span><h3>#(msg??)</h3></span></div>
    <br>
    #if(files??)
    #@paginate(p??1, totalPage??1, "/my/xjd/editDraws?id="+id+"&ddbh="+ddbh+"&pageSize=5&p=")
    #for(x : files)
    <div class="layui-row">
        <div class="layui-col-md10">
            <div id="wPaint_#(for.index)" data-name="#(x??)" style="position:relative; width:600px; height:600px; background-color:#7a7a7a; margin:70px auto 20px auto;"></div>
            <center style="margin-bottom: 50px;">
                <input type="button" value="工具栏换向" onclick="console.log($('#wPaint_#(for.index??)').wPaint('menuOrientation')); $('#wPaint_#(for.index??)').wPaint('menuOrientation', $('#wPaint_#(for.index??)').wPaint('menuOrientation') === 'vertical' ? 'horizontal' : 'vertical');"/>
                <span>#(x??)</span>
                #set(xx=encode(x))
                <a href='/my/xjd/deleteDraw?id=#(id??)&ddbh=#(ddbh??)&fileName=#(urlEncode(xx??))' title="#(x??)">X</a>
            </center>
        </div>
    </div>
    #end
    #@paginate(p??1, totalPage??1, "/my/xjd/editDraws?id="+id+"&ddbh="+ddbh+"&pageSize=5&p=")
    #end
</div>
#end
#end
#define css()
<link rel="Stylesheet" type="text/css" href="/assets/wPaint/demo/demo.css"/>
#end

#define js()
<script type="text/javascript" src="/assets/wPaint/lib/jquery.ui.core.1.10.3.min.js"></script>
<script type="text/javascript" src="/assets/wPaint/lib/jquery.ui.widget.1.10.3.min.js"></script>
<script type="text/javascript" src="/assets/wPaint/lib/jquery.ui.mouse.1.10.3.min.js"></script>
<script type="text/javascript" src="/assets/wPaint/lib/jquery.ui.draggable.1.10.3.min.js"></script>

<!-- wColorPicker -->
<link rel="Stylesheet" type="text/css" href="/assets/wPaint/lib/wColorPicker.min.css"/>
<script type="text/javascript" src="/assets/wPaint/lib/wColorPicker.min.js"></script>

<!-- wPaint -->
<link rel="Stylesheet" type="text/css" href="/assets/wPaint/wPaint.min.css"/>
<script type="text/javascript" src="/assets/wPaint/wPaint.min.js"></script>
<script type="text/javascript" src="/assets/wPaint/plugins/main/wPaint.menu.main.min.js"></script>
<script type="text/javascript" src="/assets/wPaint/plugins/text/wPaint.menu.text.js"></script>
<script type="text/javascript" src="/assets/wPaint/plugins/shapes/wPaint.menu.main.shapes.min.js"></script>
<script type="text/javascript" src="/assets/wPaint/plugins/file/wPaint.menu.main.file.min.js"></script>
<script type="text/javascript">
    function fjfwc(id, ddbh) {
        $.ajax({
            type: 'POST',
            url: '/my/xjd/fjfwc',
            data: {id: id, ddbh: ddbh},
            success: function (resp) {
                alert(resp.msg);
            }
        });
    }
    function tpscwc(id, ddbh) {
        $.ajax({
            type: 'POST',
            url: '/my/xjd/tpscwc',
            data: {id: id, ddbh: ddbh},
            success: function (resp) {
                alert(resp.msg);
            }
        })
    }

    $.extend($.fn.wPaint.defaults, {
        mode:        'text',
        lineWidth:   '1',
        fontSize:    '20',
        fillStyle:   '#FF0000',
        strokeStyle: '#FF0000',
        strokeWidth: '1'
    });

    var images = [
        #for(x : files)
        '/upload/xjd_draw/#(id??)/#(x??)',
        #end
    ];

    function fjfwc(id, ddbh) {
        $.ajax({
            type: 'POST',
            url: '/my/xjd/fjfwc',
            data: {id: id, ddbh: ddbh},
            success: function (resp) {
                if (resp.state == "ok") {
                    layer_alert(resp.msg);
                } else {
                    layer_alert(resp.msg);
                }
            }
        });
    }

    function saveImg(image) {
        var _this = this;
        $.ajax({
            type: 'POST',
            url: '/my/xjd/uploadImage' + "?r="+Math.random(),
            data: {id: #(id??), ddbh:"#(ddbh??)", preImage: $(_this).attr('options').image, image: image},
            success: function (resp) {
                if (resp.state == "ok") {
                    layer_alert("#(ddbh??) 的图纸: "+ $(_this).attr('options').image + " 保存成功!");
				} else {
					layer_alert(resp.msg);
				}
            }
        });
    }

    function loadImgBg () {
        // internal function for displaying background images modal
        // where images is an array of images (base64 or url path)
        // NOTE: that if you can't see the bg image changing it's probably
        // becasue the foregroud image is not transparent.
        this._showFileModal('bg', images);
    }

    function loadImgFg () {

        // internal function for displaying foreground images modal
        // where images is an array of images (base64 or url path)
        this._showFileModal('fg', images);
    }

    // init wPaint
    #for(x : files)
    $('#wPaint_#(for.index??)').wPaint({
        menuOffsetLeft: -35,
        menuOffsetTop: -50,
        saveImg: saveImg,
        image:"/upload/xjd_draw/#(id??)/#(x??)" + "?r="+Math.random(),
        loadImgBg: loadImgBg,
        loadImgFg: loadImgFg
    });
    #end

    function saveAll(){
        $.ajax({
            type: 'POST',
            url: '/my/xjd/uploadImage' + "?r="+Math.random(),
            data: {
                id: #(id??),
                ddbh:"#(ddbh??)",
                preImage: $(_this).attr('options').image,
                image: image
            },
            success: function (resp) {
                if (resp.state == "ok") {
                    layer_alert("保存成功!");
				} else {
					layer_alert(resp.msg);
				}
            }
        });
    }

</script>
#end
