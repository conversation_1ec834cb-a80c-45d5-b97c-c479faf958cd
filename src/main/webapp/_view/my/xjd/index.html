#set(seoTitle="询价单管理")
#@layout()
#define main()
#include("/_view/my/common/_my_menu_bar.html")

#define content()
<div>
    <div class="jf-breadcrumb-box">
        <ol class="jf-breadcrumb">
            <li><a href="/">首页</a></li>
            <li id="businessRelated" class="clickable-menu-item"><i class="layui-icon layui-icon-form"></i><button class="layui-btn layui-btn-sm" onclick="openModuleModal('业务相关')">业务相关</button></li>
            <li class="active">询价单</li>
        </ol>
        <div class="jf-btn-box">
            <a class="layui-btn layui-btn-sm" href="/my/xjd/add">创&nbsp;&nbsp;建</a>
        </div>
    </div>

    <form method="post" action="/my/xjd" class="layui-form">
        <div class="layui-row layui-col-space10 layui-form-item">
            <div class="layui-col-lg1">
                <input type="text" name="query" class="layui-input" value="#(query??)" placeholder="搜索内容">
            </div>
            <div class="layui-col-lg1">
                <input type="text" name="queryGcdd" class="layui-input" value="#(queryGcdd??)" placeholder="工程编号">
            </div>
            <div class="layui-col-lg1">
                <input type="text" name="queryKhdd" class="layui-input" value="#(queryKhdd??)" placeholder="客户订单号">
            </div>
            <div class="layui-col-lg1">
                <input type="text" name="querySz" class="layui-input" value="#(querySz??)" placeholder="石种">
            </div>
            <div class="layui-col-lg1">
                <input type="text" name="queryPm" class="layui-input" value="#(queryPm??)" placeholder="品名">
            </div>
            <div class="layui-col-lg2">
                <input type="hidden" name="khid" value="#(khid??)">
                <div id="remoteSelect"></div>
            </div>
            <div class="layui-col-lg1"><input type="submit" class="layui-btn layui-btn-sm" value="查找"></div>
        </div>
    </form>
    <table class="layui-table" lay-size="sm">
        <thead>
        <tr>
            <td>询价单号</td>
            <td>公司</td>
            <td>总才数</td>
            <td>总金额</td>
            <td>录单人</td>
            <td>录入时间</td>
            <td>操作</td>
        </tr>
        </thead>
        <tbody>
        #for( x: page.list )
        <tr>
            <td><a href="/my/xjd/edit?id=#(x.id??)" target="_blank">#(x.ddbh??)</a></td>
            <td>#(x.khjc??)</td>
            <td>#(x.zcs??)</td>
            <td>#(x.zje??)#(x.hb??)</td>
            <td>#(x.lrr??)</td>
            <td>#(x.lrfs??)</td>
            <td>
                <div class="layui-btn-group">
                    <a class="layui-btn layui-btn-sm" href="/my/xjd/edit?id=#(x.id??)" target="_blank">编辑</a>
                    #role("权限管理员", "超级管理员", "总经理")
                    <span class="layui-btn layui-btn-sm" onclick="confirmAjaxLayer('删除 #escape(x.ddbh??) 后无法恢复，确定要删除？', '/my/xjd/delete?ddbh=#(x.ddbh??)', this);">删除</span>
                    #end
                </div>
            </td>
        </tr>
        #end
        </tbody>
    </table>
    #@paginate(page.pageNumber, page.totalPage, "/my/xjd?query=" + query + "&queryGcdd=" + queryGcdd + "&queryPm=" +
    queryPm + "&khid=" + khid + "&p=")
</div>
#end
#end

#define js()
<script type="text/javascript">
    var remoteSelect = xmSelect.render({
        el: '#remoteSelect',
        autoRow: true,
        radio: true,
        filterable: true,
        filterMethod: filterFn,
        remoteSearch: true,
        remoteMethod: function (val, cb, show) {
            //这里如果val为空, 则不触发搜索
            if (!val || val.length < 3) {
                return cb([]);
            }
            //这里引入了一个第三方插件axios, 相当于$.ajax
            axios({
                method: 'get',
                url: '/my/xjd/searchKh',
                params: {
                    keyword: val,
                }
            }).then(response => {
                var d = $.parseJSON(response.data.data);
                cb(d);
            }).catch(err => {
                cb([]);
            });
        },
        on: function (o) {
            if (o.length > 0) {
                document.getElementById("khid").value = o.arr[0];
            }
        }
    })
</script>
#end