#set(seoTitle="客户维护邮件模版管理")
#@layout()
#define main()
#include("/_view/my/common/_my_menu_bar.html")

#define content()
<div>
    <div class="jf-breadcrumb-box">
        <ol class="jf-breadcrumb">
            <li><a href="/">首页</a></li>
            <li id="managementModule" class="clickable-menu-item"><i class="layui-icon layui-icon-set"></i><button class="layui-btn layui-btn-sm" onclick="openModuleModal('管理相关')">管理相关</button></li>
            #if(fl??=="营销邮件")
            <li class="active">营销邮件</li>
            #else
            <li class="active">客户维护邮件模板</li>
            #end
        </ol>
        <div class="layui-col8">
            <form method="post" action="/my/yj">
                <input type="text" name="query" placeholder="名称" size="10">
                <input type="hidden" name="fl" value="#(fl??)">
                <input type="text" id="queryKsrq" name="queryKsrq" placeholder="开始日期" autocomplete="off" size="12">
                <input type="text" id="queryJsrq" name="queryJsrq" placeholder="结束日期" autocomplete="off" size="12">
                <select lay-verify="required" lay-search="" name="queryPx">
                    <option></option>
                    <option value=" order by px">顺序</option>
                    <option value=" order by px desc">倒序</option>
                    <option value=" order by mc desc">名称倒序</option>
                    <option value=" order by mc asc">名称顺序</option>
                </select>
                <select lay-verify="required" placeholder="搜创建人" lay-search="" name="queryCjr">
                    <option></option>
                    #for(cjr : cjrList)
                    <option #if(cjr==queryCjr) selected #end>#(cjr??)</option>
                    #end
                </select>
                <input class="layui-btn layui-btn-sm" type="submit" value="查找">
            </form>
        </div>
        <div class="jf-btn-box">
            <a class="layui-btn layui-btn-sm" href="/my/yj/add?fl=#(fl??)">创&nbsp;&nbsp;建</a>
        </div>
    </div>
    <br>
    <table  class="layui-table" lay-size="sm">
        <thead>
        <tr>
            <th>名称</th>
            <th>标题</th>
            <th>内容</th>
            <th>是否公用</th>
            <th>状态</th>
            <th>排序</th>
            <th>创建人</th>
            <th>创建时间</th>
            <th style="width: 100px;">操作</th>
        </tr>
        </thead>
        <tbody>
        #for(x : page.list)
        <tr>
            <td>#(x.mc??)</td>
            <td>#(x.bt??)</td>
            <td>#(nr??)</td>
            <td>#(x.sfgy??)</td>
            <td><input data-id="#(x.id)" #if(x.zt??!="禁用") checked #end type="checkbox" name="mgcCheck" class="mgc-switch mgc-tiny"></td>
            <td><input type="text" value="#(x.px??)" onkeyup="changeValue('px', '#(x.id??)', this.value)" class="input-group w60 input_bottom"></td>
            <td>#(x.cjr??)</td>
            <td>#(x.cjsj??)</td>
            <td>
                <div class="layui-btn-group">
                    <a class="layui-btn layui-btn-sm" href="/my/yj/edit?id=#(x.id)&fl=#(x.fl??)" target="_blank">编辑</a>&nbsp;&nbsp;
                    #role("权限管理员", "超级管理员", "总经理")
                    <span class="layui-btn layui-btn-sm" onclick="confirmAjaxLayer('删除 #escape(x.bt??) 后无法恢复，确定要删除？', '/my/yj/delete?id=#(x.id)&fl=#(fl??)', this);">删除</span>
                    #end
                </div>
            </td>
        </tr>
        #end
        </tbody>
    </table>
    #@paginate(page.pageNumber, page.totalPage, "/my/yj?query=" + query + "&p=")
</div>
#end
#end
#define js()
<script>
    $(document).ready(function() {
        initMagicInput(prepareAction);
    });

    function prepareAction($this, state) {
        return {
            url: "/my/yj/magicCheck",
            data : {
                id: $this.data("id"),
            }
        }
    }
    layui.use('laydate', function() {
        var laydate = layui.laydate;

        laydate.render({
            elem: '#queryKsrq'
        });
        laydate.render({
            elem: '#queryJsrq'
        });
    });
    function changeValue(name, id, value) {
        $.ajax({
            url: '/my/yj/changeValue',
            type: 'post',
            data: {
                id: id,
                name: name,
                value: value
            },
            success: function (data) {
                if (data.state != "ok") {
                    layer_alert(data.msg);
                }
            },
            error: function (data) {
                layer_alert("网络异常，请稍后再试！");
            }
        });
    }

</script>
#end