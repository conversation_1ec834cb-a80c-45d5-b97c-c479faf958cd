#set(seoTitle="壹林备用金管理 " + (isAdd?"创建":"编辑"))
#@layout()
#define main()
#include("/_view/my/common/_my_menu_bar.html")

#define content()
<div>
    <div class="jf-breadcrumb-box">
        <ol class="jf-breadcrumb">
            <li><a href="/">首页</a></li>
            <li id="financeModule" class="clickable-menu-item"><i class="layui-icon layui-icon-rmb"></i><button class="layui-btn layui-btn-sm" onclick="openModuleModal('财务模块')">财务模块</button></li>
            <li><a href="/my/ylbyj">壹林备用金</a></li>
            <li class="active">#(isAdd ? "创建壹林备用金" : "编辑壹林备用金")</li>
        </ol>
    </div>
    <div class="layui-card-body">
        <form id="mainForm" class="layui-form" style="margin-top: 80px;white-space:nowrap!important;" action="/my/ylbyj/#(isAdd ? 'save' : 'update')" method="post">
            <input type="hidden" name="ylbyj.id" value="#(ylbyj.id??)">

            <div class="layui-row layui-col-space10 layui-form-item">
                <label class="layui-form-label layui-required layui-col-md1">月份:</label>
                <div class="layui-input-block layui-col-md2">
                    <input type="text" name="ylbyj.yf" value="#(ylbyj.yf??)" lay-verify="required" placeholder="例如:202101" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-row layui-col-space10 layui-form-item">
                <label class="layui-form-label layui-required layui-col-md1">项目:</label>
                <div class="layui-input-block layui-col-md2">
                    <input type="text" name="ylbyj.xm" value="#(ylbyj.xm??)" lay-verify="required" placeholder="" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-row layui-col-space10 layui-form-item">
                <label class="layui-form-label layui-required layui-col-md1">金额:</label>
                <div class="layui-input-block layui-col-md2">
                    <input type="text" data-number name="ylbyj.je" value="#(ylbyj.je??)" lay-verify="required" placeholder="" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-row layui-col-space10 layui-form-item">
                <label class="layui-form-label layui-required layui-col-md1">开销:</label>
                <div class="layui-input-block layui-col-md2">
                    <input type="text" name="ylbyj.kx" value="#(ylbyj.kx??)" lay-verify="required" placeholder="" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-row layui-col-space10 layui-form-item">
                <label class="layui-form-label layui-required layui-col-md1">余额:</label>
                <div class="layui-input-block layui-col-md2">
                    <input type="text" name="ylbyj.ye" value="#(ylbyj.ye??)" lay-verify="required" placeholder="" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <div class="form-inline">
                    <label class="layui-form-label">支付时间:</label>
                    <div class="layui-input-inline" style="width: 300px;">
                        <input type="text" name="ylbyj.sj" id="sj" value="#(ylbyj.sj??)" placeholder="请输入支付时间" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="form-inline">
                    <label class="layui-form-label">支付方式:</label>
                    <div class="layui-input-inline" style="width: 300px;">
                        <input type="text" name="ylbyj.fs" value="#(ylbyj.fs??)" placeholder="请输入支付方式" autocomplete="off" class="layui-input" list="fsList">
                        <datalist style="display: none" id="fsList">
                            <option>建行卡</option>
                            <option>杨鹏卡</option>
                            <option>秋卡</option>
                            <option>建行公户</option>
                            <option>小柳</option>
                            <option>交行（公）</option>
                            <option>信用社（公）</option>
                            <option>壹林（中行）</option>
                            <option>交行（林）</option>
                        </datalist>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="form-inline">
                    <label class="layui-form-label">二级科目:</label>
                    <div class="layui-input-inline" style="width: 300px;">
                        <input type="text" name="ylbyj.ejkm" value='#(ylbyj.ejkm??)' placeholder="请输入二级科目" autocomplete="off" class="layui-input" list="ejkmList">
                        <datalist style="display: none" id="ejkmList">
                            #for(ejkm:ejkmList)
                            <option>#(ejkm??)</option>
                            #end
                        </datalist>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="form-inline">
                    <label class="layui-form-label">科目:</label>
                    <div class="layui-input-inline" style="width: 300px;">
                        <input type="text" name="ylbyj.km" value='#(ylbyj.km??)' placeholder="请输入科目" autocomplete="off" class="layui-input" list="kmList">
                        <datalist style="display: none" id="kmList">
                            #for(km:kmList)
                            <option>#(km??)</option>
                            #end
                        </datalist>
                    </div>
                </div>
            </div>
            <div class="layui-row layui-col-space10 layui-form-item">
                <label class="layui-form-label layui-required layui-col-md1">备注:</label>
                <div class="layui-input-block layui-col-md2">
                    <input type="text" name="ylbyj.bz" value="#(ylbyj.bz??)" lay-verify="required" placeholder="" autocomplete="off" class="layui-input">
                </div>
            </div>


            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn layui-btn-sm" lay-submit lay-filter="component-form-element">立即提交</button>
                    <button type="reset" class="layui-btn layui-btn-sm layui-btn-primary">重置</button>
                </div>
            </div>
        </form>
    </div>
</div>
#end
#end


#define js()

<script type="text/javascript">
    layui.use('laydate', function() {
        var laydate = layui.laydate;
        laydate.render({
            elem: '#sj'
        });
    });
    $(document).ready(function () {
        $("form").ajaxForm({
            dataType: "json"
            ,beforeSerialize: function(){
                $("input[data-number]").each(function() {
                    var vaIn = $(this).val();
                    if (vaIn && !isIntNumber(vaIn) && !isFloatNumber(vaIn)) {
                        showFailMsg(vaIn + " 不能填非数字!");
                        return false;
                    }
                });
            },
            success: function (ret) {
                layer_alert_with_callback(ret.msg, ret.state, "/my/ylbyj/edit?id="+ret.id);
            },
            before: function () {
                layer.load(0, {shade: false});
            }, error: function (data) {
                layer_alert("网络操作失败，请咨询老柳! QQ75272683");
            }
        });
    });
</script>
#end
