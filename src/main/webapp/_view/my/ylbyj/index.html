#set(seoTitle="壹林备用金管理")
#@layout()
#define main()
#include("/_view/my/common/_my_menu_bar.html")

#define content()
<div>
    <div class="jf-breadcrumb-box">
        <ol class="jf-breadcrumb">
            <li><a href="/">首页</a></li>
            <li id="financeModule" class="clickable-menu-item"><i class="layui-icon layui-icon-rmb"></i><button class="layui-btn layui-btn-sm" onclick="openModuleModal('财务模块')">财务模块</button></li>
            <li class="active">壹林备用金</li>
        </ol>
        <div class="jf-btn-box">
            <a class="layui-btn layui-btn-sm" href="/my/ylbyj/add">创&nbsp;&nbsp;建</a>
        </div>
        <div class="layui-col8">
            <form class="layui-form" method="post" action="/my/ylbyj">
                <div class="layui-row layui-col-space10 layui-form-item">
                    <div class="layui-col-lg1"><input class="layui-input" type="text" name="query" placeholder="输入内容查询"></div>
                    <div class="layui-col-lg1"><input type="text" class="layui-input" name="queryKsrq" autocomplete="off" id="queryKsrq" placeholder="yyyyMM"></div>
                    <div class="layui-col-lg1"><input type="text" class="layui-input" name="queryJsrq" autocomplete="off" id="queryJsrq" placeholder="yyyyMM"></div>
                    <div class="layui-col-lg1">
                        <select name="queryPx" lay-verify="required" lay-search="">
                            <option value=" order by xm desc" #if(queryPx??==" order by xm desc") selected #end>名称倒序</option>
                            <option value=" order by xm asc" #if(queryPx??==" order by xm asc") selected #end>名称顺序</option>
                        </select>
                    </div>
                    <div class="layui-col-lg1">
                        <button class="layui-btn layui-btn-sm" lay-submit lay-filter="component-form-element">查询</button>
                    </div>
                    <div class="layui-col-lg1"><a href="#" onclick="doExport('#myTable01', {type: 'xlsx', htmlHyperlink: 'content'});"> <img src="/assets/img/xls.png" alt="XLSX" style="width:24px"></a></div>
                </div>
                <div class="layui-row layui-col-space10 layui-form-item">
                    <div class="layui-col-lg2">
                        <input class="layui-input layui-required" style="width: 150px;" type="text" id="yf" name="yf" placeholder="输入导入月份" onblur="yfBlur()">
                    </div>
                    <div class="layui-col-lg8">
                        <button type="button" class="layui-btn layui-btn-sm" id="uploadFile"><i class="layui-icon"></i>导入</button>
                        <a class="layui-btn layui-btn-sm" href="/download/壹林备用金模版.xls">模版下载</a>
<!--                        <a class="layui-btn layui-btn-sm" href="/my/ylbyj/export" target="_blank">导出</a>-->
                    </div>
                </div>
            </form>
        </div>
    </div>
    <br>
    <div class="layui-fluid">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <table class="layui-table" lay-size="sm">
                            <thead>
                            <tr>
                                <th>月份</th>
                                <th>项目</th>
                                <th>金额</th>
                                <th>开销</th>
                                <th>余额</th>
                                <th>备注</th>
                                <th style="width: 100px;">操作</th>
                            </tr>
                            </thead>
                            <tbody>
                            #for(x : page.list)
                            <tr>
                                <td>#(x.yf??)</td>
                                <td>#(x.xm??)</td>
                                <td>#(x.je??)</td>
                                <td>#(x.kx??)</td>
                                <td>#(x.ye??)</td>
                                <td>#(x.bz??)</td>

                                <td>
                                    <div class="layui-btn-group">
                                        <a class="layui-btn layui-btn-sm" href="/my/ylbyj/edit?id=#(x.id)">编辑</a>&nbsp;&nbsp;
                                        #role("权限管理员", "超级管理员", "总经理")
                                        <span class="layui-btn layui-btn-sm" onclick="confirmAjaxLayer('删除 #escape(x.je??) 后无法恢复，确定要删除？', '/my/ylbyj/delete?id=#(x.id)', this);">删除</span>
                                        #end
                                    </div>
                                </td>
                            </tr>
                            #end
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    #@paginate(page.pageNumber, page.totalPage, "/my/ylbyj?query=" + query + "&queryKsrq=" + queryKsrq + "&queryJsrq=" + queryJsrq + "&queryPx=" + queryPx + "&p=")
</div>
#end
#end

#define js()
<script type="application/javascript">
    layui.use('laydate', function() {
        var laydate = layui.laydate;

        laydate.render({
            elem: '#yf'
            ,format: 'yyyyMM'
            ,type: 'month'
            ,range: false
        });
    });
    function yfBlur(){
        var val = document.getElementById("yf").value;
        if(val === undefined || val === '' || val.length<6){
            $("#uploadFile").attr("disabled","disabled");
            $("#uploadFile").attr("class","layui-btn-disabled");
        }else{
            $("#uploadFile").removeAttr("disabled");
            $("#uploadFile").attr("class","layui-btn");
        }
    }

    layui.upload.render({
        elem: '#uploadFile'
        ,url: '/my/ylbyj/uploadFile'
        , data: {yf: function(){return $('#yf').val();}}
        ,accept: 'file'
        , timeout: 300000
        ,done: function(res){
            location.reload();
        }
    });

    $(document).ready(function () {
        yfBlur();
    });
    layui.laydate.render({
        elem: '#queryKsrq'
        ,format: 'yyyyMM'
    });
    layui.laydate.render({
        elem: '#queryJsrq'
        ,format: 'yyyyMM'
    });
</script>
#end