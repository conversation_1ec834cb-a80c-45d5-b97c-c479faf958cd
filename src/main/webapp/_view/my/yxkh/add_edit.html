#set(seoTitle="营销客户管理 " + (isAdd?"创建":"编辑"))
#@layout()
#define main()
#include("/_view/my/common/_my_menu_bar.html")

#define content()
<div>
    <div class="jf-breadcrumb-box">
        <ol class="jf-breadcrumb">
            <li><a href="/">首页</a></li>
            <li id="businessRelated" class="clickable-menu-item"><i class="layui-icon layui-icon-form"></i><button
                    class="layui-btn layui-btn-sm" onclick="openModuleModal('业务相关')">业务相关</button></li>
            <li><a href="/my/yxkh">营销客户</a></li>
            <li class="active">#(isAdd ? "创建营销客户" : "编辑营销客户")</li>
        </ol>
    </div>
    <div class="layui-card-body">
        <form id="mainForm" class="layui-form" style="margin-top: 10px;white-space:nowrap!important;"
            action="/my/yxkh/#(isAdd ? 'save' : 'update')" method="post">
            <input type="hidden" name="yxkh.id" value="#(yxkh.id??)">
            <input type="hidden" value="#(yxkh.fg??)" id="yxkh.fg" name="yxkh.fg">

            <div class="layui-form-item">
                <div class="layui-inline w600">
                    <button class="layui-btn layui-btn-sm" id="submitButton" lay-submit
                        lay-filter="component-form-element">立即提交</button>
                    <button type="reset" class="layui-btn layui-btn-sm layui-btn-primary">重置</button>
                    <a class="layui-btn layui-btn-sm" href="/my/yxkh/good?id=#(yxkh.id??)">转为已合作</a>
                    <a data-toggle="modal" href="#" onclick="fjClick()" class="layui-btn btn-large">相关文件</a>
                    <a data-toggle="modal" href="#" onclick="xjClick()" class="layui-btn btn-large">询价记录</a>
                    <a href="/my/xjd/add?gsmc=#(yxkh.gsmc??)&khid=#(yxkh.id??)" class="layui-btn btn-large"
                        target="_blank">新增询价</a>
                    #if(!isAdd)
                    <button type="button" class="layui-btn layui-btn-normal btn-large" onclick="showSurveyMenuInEdit()">
                        <i class="layui-icon layui-icon-form"></i>市场调研
                    </button>
                    <a href="/my/yxkh/delete?id=#(yxkh.id??)" class="layui-btn btn-large">删除客户</a>
                    #end
                </div>
                #role("权限管理员", "超级管理员", "总经理")
                <div class="layui-inline" style="display: none">
                    <label class="layui-form-label w50" for="yxkh.fg">分管</label>
                    <div class="layui-input-inline w400">
                        <div id="ywySelect"></div>
                    </div>
                </div>
                #end
            </div>

            <div class="layui-row layui-col-space10 layui-form-item">
                <div class="layui-col-lg3">
                    <label class="layui-form-label required-input w50">公司名称</label>
                    <div class="layui-input-block">
                        <input type="text" id="yxkh.gsmc" name="yxkh.gsmc" value="#(yxkh.gsmc??)" lay-verify="required"
                            placeholder="" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-col-lg3">
                    #if(yxkh.wz??)
                    #set(wz=yxkh.wz??)
                    #set(wz=wz.replace("http://", ""))
                    #set(wz=wz.replace("https://", ""))
                    #set(wz="http://"+wz)
                    #else
                    #set(wz="")
                    #end
                    <label class="layui-form-label required-input w50"><a class="layui-btn layui-btn-sm" href="#(wz??)"
                            target="_blank">网址</a></label>
                    <div class="layui-input-block">
                        <input type="text" name="yxkh.wz" value="#(yxkh.wz??)" placeholder="" autocomplete="off"
                            class="layui-input">
                    </div>
                </div>
                <div class="layui-col-lg3" title='1.店铺3个以上，则可以定义"3星"及以上;&#13;&#10;2.零售商年进口5柜及以上，则可定4或5星；批发商15柜起算;&#13;&#10;3.莫用客户的情绪定义星级。'>
                    <label class="layui-form-label data-check w50">星级等级</label>
                    <div class="layui-input-block">
                        <select id="yxkh.zydj" name="yxkh.zydj">
                            <option></option>
                            #(selectOption("5星", 5, yxkh.zydj??))
                            #(selectOption("4星", 4, yxkh.zydj??))
                            #(selectOption("3星", 3, yxkh.zydj??))
                            #(selectOption("2星", 2, yxkh.zydj??))
                            #(selectOption("1星", 1, yxkh.zydj??))
                        </select>
                    </div>
                </div>
                <div class="layui-col-lg3">
                    <label class="layui-form-label required-input w50">
                        客户性质
                        <span class="survey-answers-btn" onclick="showSurveyAnswers('A3', 'yxkh.khxz')" title="查看调研答案">
                            <i class="layui-icon layui-icon-survey" style="color: #1890ff; cursor: pointer;"></i>
                        </span>
                    </label>
                    <div class="layui-input-block">
                        #@simple_select("yxkh.khxz", "批发商,零售商", yxkh.khxz??, "")
                        <div id="survey-answers-A3" class="survey-answers-container" style="display: none;"></div>
                    </div>
                </div>
            </div>
            <div class="layui-row layui-col-space10 layui-form-item">
                <div class="layui-col-lg3">
                    <label class="layui-form-label data-check w50">
                        交货方式
                        <span class="survey-answers-btn" onclick="showSurveyAnswers('A4', 'yxkh.cgjgfs')" title="查看调研答案">
                            <i class="layui-icon layui-icon-survey" style="color: #1890ff; cursor: pointer;"></i>
                        </span>
                    </label>
                    <div class="layui-input-block" title="如果客户和我们合作会选择的交货方式">
                        #@simple_select("yxkh.cgjgfs", ",FOB货到本地我方港口,CIF货到境外客户港口含海上保险,DDU送货上门不含境外客户进口税,DDP送货上门含境外客户进口税,其他", yxkh.cgjgfs??, "")
                    </div>
                </div>
                <div class="layui-col-lg3">
                    <label class="layui-form-label data-check w50">采购模式</label>
                    <div class="layui-input-block">
                        #@simple_select("yxkh.cgms", "自己从国外进口,经由代理商从国外进口,当地供应商采购", yxkh.cgms??, "")
                    </div>
                </div>
                <div class="layui-col-lg3">
                    <label class="layui-form-label data-check w50">采购源头进口国</label>
                    <div class="layui-input-block">
                        #@simple_select("yxkh.cgytjkg", "中国,印度,越南,当地,中国+印度,中国+越南,中国+当地,印度+越南,印度+当地,越南+当地,中国+印度+越南,中国+印度+当地,中国+越南+当地,印度+越南+当地,其他", yxkh.cgytjkg??, "")
                    </div>
                </div>
                <div class="layui-col-lg3">
                    <label class="layui-form-label data-check w50">客户询价/订单数量</label>
                    <div class="layui-input-block">
                        #@simple_select("yxkh.cgdw", "整柜 FCL,5吨以上散货LCL,5吨以下散货LCL", yxkh.cgdw??, "")
                    </div>
                </div>
            </div>
            <div class="layui-row layui-col-space10 layui-form-item">
                <div class="layui-col-lg3">
                    <label class="layui-form-label required-input w50">公司群发域名</label>
                    <div class="layui-input-block">
                        <select id="yxkh.fjr" name="yxkh.fjr">
                            <option></option>
                            <option value="<EMAIL>" #if(yxkh.fjr??=="<EMAIL>" ) selected #end>澳林</option>
                            <option value="<EMAIL>" #if(yxkh.fjr??=="<EMAIL>" ) selected #end>壹林</option>
                            <option value="<EMAIL>" #if(yxkh.fjr??=="<EMAIL>" ) selected #end>菉能</option>
                            <option value="VNG" #if(yxkh.fjr??=="VNG" ) selected #end>VNG</option>
                        </select>
                    </div>
                </div>
                <div class="layui-col-lg3">
                    <label class="layui-form-label required-input w50">国别</label>
                    <div class="layui-input-block">
                        <input type="text" id="yxkh.gb" name="yxkh.gb" value="#(yxkh.gb??)" placeholder=""
                            autocomplete="off" class="layui-input" list="gbList">
                        <datalist id="gbList" style="display: none">
                            <option></option>
                            #for(g:gbList)
                            <option>#(g.gb??)</option>
                            #end
                        </datalist>
                    </div>
                </div>
                <div class="layui-col-lg3" title="不要空格">
                    <label class="layui-form-label required-input w50">州</label>
                    <div class="layui-input-block">
                        <input type="text" id="yxkh.zhou" name="yxkh.zhou" value="#(yxkh.zhou??)" placeholder=""
                            autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-col-lg3" title="不要空格">
                    <label class="layui-form-label required-input w50">市</label>
                    <div class="layui-input-block">
                        <input type="text" id="yxkh.shi" name="yxkh.shi" value="#(yxkh.shi??)" placeholder=""
                            autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>
            <div class="layui-row layui-col-space10 layui-form-item">
                <div class="layui-col-lg2">
                    <label class="layui-form-label required-input w50">邮编</label>
                    <div class="layui-input-block">
                        <input type="text" name="yxkh.yb" value="#(yxkh.yb??)" placeholder="" autocomplete="off"
                            class="layui-input">
                    </div>
                </div>
                <div class="layui-col-lg2">
                    <label class="layui-form-label data-check w50">决策者性别</label>
                    <div class="layui-input-block">
                        #@simple_select("yxkh.jczxb", ",Mr,Ms,Herr,Frau,Madame,Monsieur,男,女", yxkh.jczxb??, "")
                    </div>
                </div>
                <div class="layui-col-lg2">
                    <label class="layui-form-label data-check w50">决策者</label>
                    <div class="layui-input-block">
                        <input type="text" name="yxkh.jcz" value="#(yxkh.jcz??)" placeholder="" autocomplete="off"
                            class="layui-input">
                    </div>
                </div>
                <div class="layui-col-lg6">
                    <label class="layui-form-label data-check" style="width: 120px !important">决策手机(含国际区号)</label>
                    <div class="layui-input-block">
                        <input type="text" name="yxkh.jcsj" id="yxkh.jcsj" value="#(yxkh.jcsj??)" placeholder=""
                            autocomplete="off" class="layui-input" style="width: 65%; display: inline-block;">
                        <button type="button" class="layui-btn layui-btn-sm layui-btn-primary" onclick="openCallModal()"
                            style="width: 13%; margin-left: 1%;">
                            <i class="layui-icon layui-icon-cellphone"></i>拨打
                        </button>
                        <button type="button" class="layui-btn layui-btn-sm layui-btn-normal"
                            onclick="openVoiceRecordsModal()" style="width: 13%; margin-left: 1%;">
                            <i class="layui-icon layui-icon-voice"></i>录音
                        </button>
                    </div>
                </div>
            </div>
            <div class="layui-row layui-col-space10 layui-form-item">
                <div class="layui-col-lg10" title="邮箱和邮箱之间只能用英文;分隔, 比如:<EMAIL>;<EMAIL>">
                    <label class="layui-form-label w50">决策者邮箱</label>
                    <div class="layui-input-block">
                        <input type="text" name="yxkh.jczyx" value="#(yxkh.jczyx??)" placeholder="" autocomplete="off"
                            class="layui-input" onchange="changeJczyx(this.value)">
                    </div>
                </div>
                #role("权限管理员", "超级管理员", "总经理")
                <div class="layui-col-lg1">
                    <div class="layui-input-block">
                        <input type="hidden" id="yxkh.qzbc" name="yxkh.qzbc" value="#(yxkh.qzbc??0)">
                        <input type="checkbox" id="qzbcCheckBox" title="强制保存" #if(yxkh.qzbc??0>0) checked #end>
                    </div>
                </div>
                #end
            </div>
            <div class="layui-row layui-col-space10 layui-form-item">
                <div class="layui-col-lg3">
                    <label class="layui-form-label w50">地址</label>
                    <div class="layui-input-block">
                        <input type="text" id="yxkhDz" name="yxkh.dz" value="#(yxkh.dz??)" placeholder=""
                            autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-col-lg3">
                    <label class="layui-form-label w50">FACEBOOK</label>
                    <div class="layui-input-block">
                        <input type="text" name="yxkh.facebook" value="#(yxkh.facebook??)" placeholder=""
                            autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-col-lg3">
                    <label class="layui-form-label data-check w50">销售规模</label>
                    <div class="layui-input-block">
                        <input type="text" name="yxkh.gm" value="#(yxkh.gm??)" placeholder="" autocomplete="off"
                            class="layui-input">
                    </div>
                </div>
                <div class="layui-col-lg3">
                    <label class="layui-form-label required-input w50">邮件可否群发</label>
                    <div class="layui-input-block">
                        <select lay-verify="required" lay-search="" name="yxkh.sfqy">
                            <option value="否" #if(yxkh.sfqy??=="否" ) selected #end>邮件群发</option>
                            <option value="是" #if(yxkh.sfqy??=="是" ) selected #end>邮件不群发</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="layui-row layui-col-space10 layui-form-item">
                <div class="layui-col-lg3">
                    <label class="layui-form-label w50">标准地址</label>
                    <div class="layui-input-block">
                        <input type="text" id="yxkh.bzdz" name="yxkh.bzdz" value="#(yxkh.bzdz??)" placeholder=""
                            autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-col-lg3">
                    <label class="layui-form-label w50">经度</label>
                    <div class="layui-input-block">
                        <input type="text" id="yxkh.jd" name="yxkh.jd" value="#(yxkh.jd??)" placeholder=""
                            autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-col-lg3">
                    <label class="layui-form-label w50">纬度</label>
                    <div class="layui-input-block">
                        <input type="text" id="yxkh.wd" name="yxkh.wd" value="#(yxkh.wd??)" placeholder=""
                            autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-col-lg3">
                    <a data-toggle="modal" href="#" onclick="hqjwd()" class="layui-btn btn-large">获取经纬度</a>
                </div>
            </div>
            <div class="layui-row layui-col-space10 layui-form-item">
                <div class="layui-col-lg3" title="多个产品用+分隔，销量大的写前面">
                    <label class="layui-form-label required-input w50">
                        主营产品
                        <span class="survey-answers-btn" onclick="showSurveyAnswers('A6', 'yxkh.zycp')" title="查看调研答案">
                            <i class="layui-icon layui-icon-survey" style="color: #1890ff; cursor: pointer;"></i>
                        </span>
                    </label>
                    <div class="layui-input-block">
                        <input type="text" name="yxkh.zycp" value="#(yxkh.zycp??)"
                               placeholder="如：花岗岩板材,大理石瓷砖,石英石台面"
                               autocomplete="off" class="layui-input">
                        <div id="survey-answers-A6" class="survey-answers-container" style="display: none;"></div>
                    </div>
                </div>
                <div class="layui-col-lg3">
                    <label class="layui-form-label w50">主供应商</label>
                    <div class="layui-input-block">
                        <input type="text" id="yxkh.zgys" name="yxkh.zgys" value="#(yxkh.zgys??)" placeholder=""
                            autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-col-lg3">
                    <label class="layui-form-label w50">比较产品</label>
                    <div class="layui-input-block">
                        <input type="text" name="yxkh.bjcp" value="#(yxkh.bjcp??)" placeholder="" autocomplete="off"
                            class="layui-input">
                    </div>
                </div>
            </div>


            <div class="layui-row layui-col-space10 layui-form-item">
                <div class="layui-col-lg3">
                    <label class="layui-form-label required-input w50">目的港</label>
                    <div class="layui-input-block">
                        <input type="text" name="yxkh.mdg" value="#(yxkh.mdg??)" placeholder="目的港" autocomplete="off"
                            class="layui-input">
                    </div>
                </div>
                <div class="layui-col-lg3">
                    <label class="layui-form-label required-input w50">驾驶距离KM</label>
                    <div class="layui-input-block">
                        <input type="text" name="yxkh.ddujsjl" value="#(yxkh.ddujsjl??)"
                            placeholder="DDU,DDP送货地离港口驾驶距离KM" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-col-lg3">
                    <label class="layui-form-label w50">标签</label>
                    <div class="layui-input-block">
                        <input type="text" name="yxkh.bq" value="#(yxkh.bq??)" placeholder="客户标签" autocomplete="off"
                            class="layui-input">
                    </div>
                </div>
            </div>
            <div class="layui-row layui-col-space10 layui-form-item">
                <div class="layui-col-lg3">
                    <label class="layui-form-label w50">客户背调想法</label>
                    <div class="layui-input-block">
                        <textarea name="yxkh.bz1" placeholder="" class="layui-textarea"
                            style="height: 150px;">#(yxkh.bz1??)</textarea>
                    </div>
                </div>
                <div class="layui-col-lg3">
                    <label class="layui-form-label w50">备注2</label>
                    <div class="layui-input-block">
                        <textarea name="yxkh.bz2" placeholder="" class="layui-textarea"
                            style="height: 150px;">#(yxkh.bz2??)</textarea>
                    </div>
                </div>
                <div class="layui-col-lg6">
                    <label class="layui-form-label w50">客户的客户评价</label>
                    <div class="layui-input-block">
                        <textarea name="yxkh.bz4" placeholder="客户的客户在Google或Yelp或Facebook上的评价" class="layui-textarea"
                            style="height: 150px;">#(yxkh.bz4??)</textarea>
                    </div>
                </div>
            </div>
            <div class="layui-row layui-col-space10 layui-form-item">
                <div class="layui-col-lg12">
                    <label class="layui-form-label w100">沟通进度/记录</label>
                    <div class="layui-input-inline" style="width: 400px;">
                        <textarea name="gtjy" id="gtjy" placeholder="" class="layui-textarea">#(gtjy??)</textarea>
                    </div>
                    <div class="layui-input-inline">
                        <a class="layui-btn layui-btn-fluid layui-btn-sm btn-vertical-center" style="height:100px;"
                            onclick="changeGtfs()">
                            <font size="5">保存沟通</font>
                        </a>
                    </div>
                </div>
            </div>

            <div class="layui-row">
                <div class="layui-col-lg12">
                    <div class="layui-input-inline">
                        <table class="layui-table table sampleTable table-striped table-bordered" style="width: 960px;">
                            <thead>
                                <tr>
                                    <th>沟通纪要: 联系人</th>
                                    <th>联系时间</th>
                                    <th>联系内容</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                #for( gtjy:gtjyList )
                                <tr>
                                    <td>#(gtjy.ywy??)</td>
                                    <td>#(gtjy.gtsj??)</td>
                                    <td>#(gtjy.jy??)</td>
                                    <td><a href="javascript:void(0);" class="layui-btn layui-btn-sm"
                                            onclick="confirmDelete('删除后无法恢复，确定要删除？', '/my/yxkh/deleteGtjy?id=#(gtjy.id??)', this, 'tr');">删除</a>
                                    </td>
                                </tr>
                                #end
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </form>
        <br>

        <!-- 市场调研概览区域 -->
        #if(!isAdd)
        #role("权限管理员", "超级管理员", "总经理")
        <div class="layui-card" style="margin-bottom: 20px;">
            <div class="layui-card-header">
                <h3 style="margin: 0; display: inline-block;">
                    <i class="layui-icon layui-icon-form" style="color: #1890ff;"></i> 市场调研概览
                </h3>
                <div style="float: right;">
                    <button type="button" class="layui-btn layui-btn-sm layui-btn-normal" onclick="showSurveyMenuInEdit()">
                        <i class="layui-icon layui-icon-add-1"></i> 调研操作
                    </button>
                </div>
            </div>
            <div class="layui-card-body">
                <div id="surveyOverview" style="min-height: 60px;">
                    <div style="text-align: center; color: #999; padding: 20px;">
                        <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop" style="font-size: 20px;"></i>
                        <span style="margin-left: 8px;">正在加载调研数据...</span>
                    </div>
                </div>
            </div>
        </div>
        #end
        #end

        <h1>该客户其他联系人</h1>
        <form class="layui-form" method="post">
            <table class="layui-table" lay-size="sm">
                <thead>
                    <tr>
                        <th>名称</th>
                        <th>性别</th>
                        <th>邮箱</th>
                        <th>岗位</th>
                        <th>备注</th>
                        <th style="width: 100px;">操作</th>
                    </tr>
                </thead>
                <tbody>
                    #for(x : yxkhLxrList)
                    <tr>
                        <td>#(x.xm??)</td>
                        <td>#(x.xb??)</td>
                        <td><span class="email-link" onclick="openComposeEmail('#(x.email??)')" title="点击发送邮件">#(x.email??)</span></td>
                        <td>#(x.zw??)</td>
                        <td>#(x.bz??)</td>
                        <td>
                            <span
                                onclick="confirmAjaxLayer('删除后无法恢复,确定要删除？', '/my/yxkh/deleteLxr?id=#(yxkh.id??)&yxkhlxrId=#(x.id)', this);">删除</span>
                        </td>
                    </tr>
                    #end

                    <tr>
                        <td><input type="text" id="yxkhlxr.xm" name="yxkhlxr.xm"></td>
                        <td><input type="text" id="yxkhlxr.xb" name="yxkhlxr.xb"></td>
                        <td><input type="text" id="yxkhlxr.email" name="yxkhlxr.email"
                                onchange="changeJczyx(this.value)"></td>
                        <td><input type="text" id="yxkhlxr.zw" name="yxkhlxr.zw"></td>
                        <td><input type="text" id="yxkhlxr.bz" name="yxkhlxr.bz"></td>
                        <td>
                            <a href="#" onclick="add()">添加</a>
                        </td>
                    </tr>
                </tbody>
            </table>
        </form>
    </div>
    <h3>客户沟通</h3>
    <table class="layui-table" lay-size="sm">
        <thead>
            <tr>
                <td width="10%">简要描述</td>
                <td>问题范例</td>
                <td width="20%">答案选项</td>
                <td width="20%">备注</td>
            </tr>
        </thead>
        <tbody>
            #for(khwt : khwtList)
            <tr>
                <td>#(khwt.jyms_hh??)</td>
                <td>#(khwt.wtfl_hh??)</td>
                <td>
                    #if(khwt.daxx??)
                    #set(daList=(khwt.daxx).split("##"))
                    <ul>
                        #for(da : daList)
                        <li style="display:inline-block;"><input type="checkbox" #if(khwt.ada??==da??) checked #end
                                onchange="changeDa('#(khwt.id??)', '#(da??)')">#(da??)
                        </li>&nbsp;&nbsp;
                        #end
                        #end
                        <li><input type="checkbox" #if(khwt.aqt??) checked #end>其他:
                            <textarea class="text-area" style="width: 98%; height: 100px;"
                                onchange="changeQt('#(khwt.id??)', this.value)">#(khwt.aqt??)</textarea>
                        </li>
                    </ul>
                </td>
                <td><textarea class="layui-textarea" style="width: 98%; height: 100px;"
                        onchange="changeBz('#(khwt.id??)', this.value)">#(khwt.abz??)</textarea></td>
            </tr>
            #end
        </tbody>
    </table>
    <div id="xjDIv" style="width: 800px; height: 600px; display: none">
        <table class="layui-table" lay-size="sm">
            <thead>
                <tr>
                    <td>询价订单</td>
                    <td>询价时间</td>
                    <td>总金额</td>
                </tr>
            </thead>
            <tbody>
                #for(khxj : khxjList)
                <tr>
                    <td><a href="/my/xjd/edit?ddbh=#(khxj.ddbh??)" target="_blank">#(khxj.ddbh??)</a></td>
                    <td>#(khxj.lrsj??)</td>
                    <td>#(khxj.ddje??'')#(khxj.hb??'')</td>
                </tr>
                #end

            </tbody>
        </table>
    </div>
</div>
<div id="defaultModal" style="display: none;">
    <form action="/my/yxkh/uploadWj" method="post" enctype="multipart/form-data">
        <div class="layui-row">
            <div class="layui-input-inline">
                <input type="hidden" name="upload_id" id="upload_id" value="#(yxkh.id??)">
                <input type="file" name="wj" multiple="multiple">
            </div>
            <div class="layui-input-inline">
                <button type="submit" class="layui-btn layui-btn-sm">上传</button>&nbsp;
            </div>
        </div>
    </form>
    <br>
    <br>
    <br>
    #if(wjList??)
    <table class="layui-table">
        <tr>
            <th>文件名</th>
            <th style="width: 100px;">操作</th>
        </tr>
        #for( wj : wjList)
        #if(wj!=null && wj!="")
        <tr id='tr_#(wj??)'>
            <td><a href="/upload/yxkh/#(yxkh.id??)/#(wj??)" target="_blank">#(wj??)</a></td>
            <td><a class="btn btn-dark" href="/my/yxkh/deleteFile?id=#(yxkh.id??)&wj=#(wj??)">删除</a></td>
        </tr>
        #end
        #end
    </table>
    #end
</div>
#end
#end

<div id="gtfsModal" class="layui-form" style="width: 400px; height: 200px; display: none">
    <div class="layui-form-item">
        <div class="form-inline">
            <label class="layui-form-label w50">沟通方式</label>
            <div class="layui-input-inline">
                <select id='gtfs'>
                    <option>电话</option>
                    <option>SNS</option>
                    <option>邮件</option>
                </select>
            </div>
        </div>
    </div>
</div>

<!-- 拨打电话弹出界面 -->
<div id="callModal" style="width: 800px; height: 550px; display: none; padding: 20px;">
    <div class="layui-row layui-col-space20">
        <div class="layui-col-md8">
            <h3>解析的电话号码</h3>
            <div id="phoneNumbers" style="margin-bottom: 20px;">
                <!-- 解析出的电话号码将显示在这里 -->
            </div>

            <h3>拨打电话</h3>
            <div class="layui-form">
                <div class="layui-form-item">
                    <label class="layui-form-label" style="width: 80px;">主叫号码</label>
                    <div class="layui-input-block" style="margin-left: 110px;">
                        <div style="display: flex; align-items: center; gap: 8px; flex-wrap: wrap;">
                            <input type="text" id="caller" placeholder="请输入主叫号码" class="layui-input"
                                style="width: 220px; min-width: 180px; font-size: 14px; padding: 8px 12px;">
                            <button type="button" class="layui-btn layui-btn-sm layui-btn-normal"
                                onclick="saveCallerNumber()" title="保存号码到本地"
                                style="padding: 8px 12px; border-radius: 4px; display: flex; align-items: center; justify-content: center;">
                                <i class="layui-icon layui-icon-download" style="font-size: 14px;"></i>
                                <span style="margin-left: 4px;">保存</span>
                            </button>
                            <button type="button" class="layui-btn layui-btn-sm layui-btn-primary"
                                onclick="clearSavedCallerNumber()" title="清除保存的号码"
                                style="padding: 8px 12px; border-radius: 4px; display: flex; align-items: center; justify-content: center;">
                                <i class="layui-icon layui-icon-delete" style="font-size: 14px;"></i>
                                <span style="margin-left: 4px;">清除</span>
                            </button>
                        </div>
                        <div style="margin-top: 8px; font-size: 12px; color: #999; line-height: 1.4;">
                            <i class="layui-icon layui-icon-tips"
                                style="font-size: 12px; color: #01AAED; margin-right: 4px;"></i>
                            点击保存按钮可将号码保存到浏览器本地，下次自动填充
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label" style="width: 80px;">被叫号码</label>
                    <div class="layui-input-block" style="margin-left: 110px;">
                        <input type="text" id="callees" placeholder="请输入被叫号码" class="layui-input"
                            style="width: 220px; font-size: 14px; padding: 8px 12px;">
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-input-block" style="margin-left: 110px;">
                        <button type="button" class="layui-btn layui-btn-normal" onclick="call()"
                            style="padding: 10px 25px; font-size: 14px; border-radius: 4px; display: flex; align-items: center; justify-content: center;">
                            <i class="layui-icon layui-icon-cellphone" style="margin-right: 6px;"></i>
                            拨打电话
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-col-md4">
            <h3>录音上传二维码</h3>
            <div id="qrcode" style="text-align: center; padding: 15px; background: #f8f8f8; border-radius: 8px;">
                <!-- 二维码将显示在这里 -->
            </div>
            <div style="text-align: center; margin-top: 10px; color: #666; font-size: 13px;">
                <i class="layui-icon layui-icon-cellphone" style="margin-right: 4px;"></i>
                扫描二维码上传录音文件
            </div>
        </div>
    </div>
</div>

<!-- 录音文件列表弹出界面 -->
<div id="voiceRecordsModal" style="width: 800px; height: 600px; display: none; padding: 20px;">
    <div class="layui-row">
        <div class="layui-col-md12">
            <h3 style="margin-bottom: 20px;">
                <i class="layui-icon layui-icon-voice" style="color: #1E9FFF;"></i>
                客户录音文件 - #(yxkh.gsmc??"客户")
            </h3>

            <!-- 录音文件列表 -->
            <div id="voiceRecordsList" style="max-height: 450px; overflow-y: auto;">
                <div style="text-align: center; color: #999; padding: 50px;">
                    <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"
                        style="font-size: 30px;"></i>
                    <p style="margin-top: 10px;">正在加载录音文件...</p>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div style="text-align: center; margin-top: 20px; border-top: 1px solid #e6e6e6; padding-top: 15px;">
                <button type="button" class="layui-btn layui-btn-normal" onclick="uploadNewVoice()">
                    <i class="layui-icon layui-icon-upload"></i>上传新录音
                </button>
                <button type="button" class="layui-btn layui-btn-primary" onclick="refreshVoiceList()">
                    <i class="layui-icon layui-icon-refresh"></i>刷新列表
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 音频播放器模态框 -->
<div id="audioPlayerModal" style="width: 500px; height: 200px; display: none; padding: 20px; text-align: center;">
    <h3 id="audioTitle" style="margin-bottom: 20px;">播放录音</h3>
    <audio id="audioPlayer" controls style="width: 100%; margin-bottom: 20px;" preload="metadata">
        您的浏览器不支持音频播放。
    </audio>
    <div style="color: #666; font-size: 12px;">
        <p id="audioInfo"></p>
    </div>
</div>

#define css()
<style>
    /* 字段高亮样式 - 舒服的蓝色主题 */
    .field-highlight {
        border: 3px solid #1890FF !important;
        box-shadow: 0 0 15px rgba(24, 144, 255, 0.6) !important;
        background-color: #E6F7FF !important;
        outline: 2px solid #0050B3 !important;
        outline-offset: 2px !important;
        z-index: 9999 !important;
        animation: fieldPulse 0.4s ease-in-out 2; /* 0.4秒动画，重复2次 */
    }

    @keyframes fieldPulse {
        0% {
            transform: scale(1);
            border-color: #1890FF;
        }
        100% {
            transform: scale(1.03);
            border-color: #0050B3;
        }
    }

    .btn-vertical-center {
        display: flex;
        /* 使用 Flexbox 布局 */
        justify-content: center;
        /* 水平居中 */
        align-items: center;
        /* 垂直居中 */
        height: 50px;
        /* 按钮高度 */
        padding: 0 20px;
        /* 按钮内边距 */
    }

    /* 录音文件列表样式 */
    #voiceRecordsList .layui-table th {
        background-color: #f8f8f8;
        font-weight: bold;
    }

    #voiceRecordsList .layui-table td {
        vertical-align: middle;
    }

    /* 音频播放器样式 */
    #audioPlayerModal audio {
        border-radius: 5px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    /* 滚动条样式 */
    #voiceRecordsList::-webkit-scrollbar {
        width: 6px;
    }

    #voiceRecordsList::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
    }

    #voiceRecordsList::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;
    }

    #voiceRecordsList::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }

    /* 主营产品提示样式 */
    .product-tip {
        cursor: help;
        margin-left: 5px;
    }

    .product-tip:hover {
        opacity: 0.8;
    }

    /* 让提示文字更显眼 */
    .layui-word-aux strong {
        color: #FF5722 !important;
        font-weight: bold;
    }

    /* 交货方式提示样式 */
    .delivery-tip {
        cursor: help;
        margin-left: 5px;
    }

    .delivery-tip:hover {
        opacity: 0.8;
    }

    /* 交货方式建议文字样式 */
    .delivery-advice strong {
        color: #1890FF !important;
        font-weight: bold;
    }

    /* 交货方式帮助图标样式 */
    .delivery-help-icon {
        display: inline-block;
        transition: transform 0.3s ease;
    }

    .delivery-help-icon:hover {
        transform: scale(1.2);
        filter: drop-shadow(0 2px 4px rgba(24, 144, 255, 0.3));
    }

    /* 交货方式卡片样式 */
    .delivery-card {
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    .delivery-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    /* 调研答案显示样式 */
    .survey-answers-btn {
        margin-left: 8px;
        cursor: pointer;
    }

    .survey-answers-btn:hover {
        opacity: 0.8;
    }

    .survey-answers-container {
        margin-top: 8px;
        padding: 10px;
        background: #f8f9fa;
        border-radius: 6px;
        border-left: 3px solid #1890ff;
    }

    .survey-answer-item {
        background: white;
        border-radius: 4px;
        padding: 8px 12px;
        margin-bottom: 8px;
        border: 1px solid #e8e8e8;
        position: relative;
    }

    .survey-answer-item:last-child {
        margin-bottom: 0;
    }

    .survey-answer-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 6px;
        font-size: 12px;
    }

    .survey-answer-surveyor {
        color: #1890ff;
        font-weight: bold;
    }

    .survey-answer-date {
        color: #999;
    }

    .survey-answer-content {
        color: #333;
        font-size: 13px;
        line-height: 1.4;
        margin-bottom: 8px;
        word-break: break-all;
    }

    .survey-answer-actions {
        display: flex;
        gap: 8px;
    }

    .survey-answer-btn {
        padding: 2px 8px;
        font-size: 11px;
        border-radius: 3px;
        cursor: pointer;
        border: none;
        transition: all 0.2s;
    }

    .survey-answer-btn.copy-btn {
        background: #52c41a;
        color: white;
    }

    .survey-answer-btn.copy-btn:hover {
        background: #73d13d;
    }

    .survey-answer-btn.select-btn {
        background: #1890ff;
        color: white;
    }

    .survey-answer-btn.select-btn:hover {
        background: #40a9ff;
    }

    .survey-answers-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
        padding-bottom: 8px;
        border-bottom: 1px solid #e8e8e8;
    }

    .survey-answers-title {
        font-weight: bold;
        color: #333;
        font-size: 14px;
    }

    .survey-answers-close {
        cursor: pointer;
        color: #999;
        font-size: 16px;
    }

    .survey-answers-close:hover {
        color: #ff4d4f;
    }

    .survey-no-answers {
        text-align: center;
        color: #999;
        font-size: 12px;
        padding: 20px;
    }

    /* 提交按钮禁用状态样式 */
    #submitButton:disabled {
        background: #d9d9d9 !important;
        color: #999 !important;
        cursor: not-allowed !important;
        border-color: #d9d9d9 !important;
        opacity: 0.8;
    }

    #submitButton:disabled:hover {
        background: #d9d9d9 !important;
        color: #999 !important;
        transform: none !important;
        box-shadow: none !important;
    }

    /* 保存中的动画效果 */
    #submitButton:disabled .layui-icon-loading {
        color: #666;
    }
</style>
#end

#define js()
<script src="/assets/fancyTable/fancyTable.min.js"></script>
<script type="text/javascript">
    $(".sampleTable").fancyTable({
        pagination: true,
        perPage: 100,
        sortable: true,
        inputPlaceholder: "查找"
    });
    #role("权限管理员", "超级管理员", "总经理")
    var ywySelect = xmSelect.render({
        el: '#ywySelect',
        tips: '请选择业务员?',
        filterable: true,
        filterMethod: filterFn,
        data: $.parseJSON('#(accountList??"[]")')
    });
    #end

    function confirmDelete(msg, operationUrl, obj, t) {
        layer.confirm(msg, {
            icon: 0
            , title: ''
            , shade: 0.4
            , offset: "139px"
        }, function (index) {
            $.ajax({
                url: operationUrl,
                type: 'POST',
                cache: false,
                dataType: 'json',
                success: function (data) {
                    alert(data.msg);
                    console.log($(obj));
                    console.log($(obj).closest(t));
                    $(obj).closest(t).remove();
                },
                fail: function (data) {
                    console.log(data);
                }
            });
            layer.close(index);
        });
    }

    function showMsg(id) {
        $.ajax({
            url: "/my/yxkh/getMsg",
            type: "POST",
            cache: false,
            dataType: "json",
            data: {
                "id": id
            },
            before: function () {
                layer.load(0, { shade: false });
            },
            success: function (ret) {
                var content = ret.content;
                layer.open({
                    type: 1,
                    title: "历史沟通纪要",
                    content: content
                });
                return true;
            },
            fail: function (ret) {
                return false;
            }
        });
    }

    function fjClick() {
        layer.open({
            type: 1,
            title: "附件",
            content: $('#defaultModal'),
            end: function () {
                document.getElementById("defaultModal").style.display = "none";
            }
        });
    }

    function xjClick() {
        layer.open({
            type: 1,
            title: "历史询价单",
            content: $('#xjDIv'),
            end: function () {
                document.getElementById("xjDIv").style.display = "none";
            }
        });
    }

    function hqjwd() {
        // 显示加载框
        var loadingIndex = layer.open({
            type: 1,
            title: '获取经纬度',
            content: '<div style="padding: 20px; text-align: center;"><i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop" style="font-size: 30px;"></i><p style="margin-top: 10px;">正在获取经纬度，请稍候...</p></div>',
            closeBtn: 1,
            shadeClose: true
        });

        $.ajax({
            url: "/my/yxkh/hqjwd",
            type: "POST",
            cache: false,
            dataType: "json",
            data: {
                "country": $('#yxkh\\.gb').val(),
                "zhou": $('#yxkh\\.zhou').val(),
                "shi": $('#yxkh\\.shi').val(),
                "address": $('#yxkhDz').val()
            },
            complete: function () {
                // 无论成功失败都关闭加载框
                layer.close(loadingIndex);
            },
            success: function (ret) {
                console.log(ret.data);
                if (ret.state == "ok") {
                    $('#yxkh\\.jd').val(ret.data.longitude);
                    $('#yxkh\\.wd').val(ret.data.latitude);
                    $('#yxkh\\.bzdz').val(ret.data.formattedAddress);
                } else {
                    alert(ret.msg);
                }
            },
            error: function () {
                alert("获取经纬度失败，请稍后重试");
            }
        });
    }

    $(document).ready(function () {
        #role("权限管理员", "超级管理员", "总经理")
        var ywyArray = '#(yxkh.fg??"")'.split(/;|,|、/);
        ywySelect.setValue(ywyArray);
        #end

        // 加载调研概览数据
        #if(!isAdd)
        loadSurveyOverview();
        #end
        let index;
        var isFormSubmitting = false; // 防止重复提交

        // 添加标志变量，用于跳过 data-check 检查
        var skipDataCheckValidation = false;

        // 页面加载完成后检查并提醒空字段
        // 只在编辑页面（非新增页面）显示提醒
        #if(!isAdd)
        setTimeout(function() {
            checkAndShowEmptyFieldsReminder();
        }, 1000); // 延迟1秒，确保页面完全加载
        #end

        $("#mainForm").ajaxForm({
            dataType: "json",
            beforeSerialize: function () {
                console.log('beforeSerialize 被调用，isFormSubmitting:', isFormSubmitting, 'skipDataCheckValidation:', skipDataCheckValidation);

                // 防止重复提交
                if (isFormSubmitting) {
                    console.log('表单正在提交中，阻止重复提交');
                    layer.msg('正在保存中，请稍候...', {icon: 16});
                    return false;
                }
                #role("权限管理员", "超级管理员", "总经理")
                skipDataCheckValidation = true;
                #end

                // 如果已经跳过验证，直接继续提交
                if (skipDataCheckValidation) {
                    console.log('跳过验证标志已设置，直接继续提交');
                    continueFormSubmissionProcess();
                    return true; // 继续提交，不进行验证
                }

                // 动态验证所有必填字段
                var requiredLabels = document.querySelectorAll('label.required-input');
                for (var i = 0; i < requiredLabels.length; i++) {
                    var label = requiredLabels[i];
                    var labelText = label.textContent || label.innerText;

                    // 清理标签文本，移除多余的空白字符和图标
                    labelText = labelText.replace(/\s+/g, ' ').trim();

                    // 查找关联的输入元素
                    var inputElement = null;
                    var inputBlock = label.nextElementSibling;

                    if (inputBlock && inputBlock.classList.contains('layui-input-block')) {
                        // 查找输入框或选择框
                        inputElement = inputBlock.querySelector('input, select, textarea');
                    }

                    if (inputElement) {
                        var value = inputElement.value;

                        // 检查值是否为空
                        if (value == undefined || value == null || value.trim() == "") {
                            // 使用 layui 的美观弹窗提醒
                            layer.open({
                                type: 1,
                                title: '<i class="layui-icon layui-icon-close-fill" style="color: #FF5722;"></i> 必填字段提醒',
                                content: '<div style="padding: 20px; text-align: center;">' +
                                        '<div style="font-size: 16px; color: #333; margin-bottom: 15px;">' +
                                        '<i class="layui-icon layui-icon-about" style="font-size: 24px; color: #FF5722; margin-right: 8px;"></i>' +
                                        '<strong>' + labelText + '</strong> 不能为空！' +
                                        '</div>' +
                                        '<div style="font-size: 14px; color: #666;">请填写此必填字段后再提交</div>' +
                                        '</div>',
                                area: ['400px', '200px'],
                                btn: ['知道了'],
                                btnAlign: 'c',
                                yes: function(index) {
                                    layer.close(index);

                                    // 将光标跳转到对应的输入框
                                    inputElement.focus();

                                    // 如果是文本输入框，选中所有文本（如果有的话）
                                    if (inputElement.type === 'text' || inputElement.type === 'textarea') {
                                        inputElement.select();
                                    }

                                    // 滚动到该元素位置，确保用户能看到
                                    inputElement.scrollIntoView({
                                        behavior: 'smooth',
                                        block: 'center'
                                    });
                                }
                            });

                            return false;
                        }
                    }
                }

                // 检查 data-check 字段（可选字段提醒）- 只有在未跳过验证时才执行
                var emptyDataCheckFields = [];
                if (!skipDataCheckValidation) {
                    var dataCheckLabels = document.querySelectorAll('label.data-check');

                    for (var j = 0; j < dataCheckLabels.length; j++) {
                        var label = dataCheckLabels[j];
                        var labelText = label.textContent || label.innerText;

                        // 清理标签文本，移除多余的空白字符和图标
                        labelText = labelText.replace(/\s+/g, ' ').trim();

                        // 查找关联的输入元素
                        var inputElement = null;
                        var inputBlock = label.nextElementSibling;

                        if (inputBlock && inputBlock.classList.contains('layui-input-block')) {
                            // 查找输入框或选择框
                            inputElement = inputBlock.querySelector('input, select, textarea');
                        }

                        if (inputElement) {
                            var value = inputElement.value;

                            // 检查值是否为空
                            if (value == undefined || value == null || value.trim() == "") {
                                emptyDataCheckFields.push({
                                    label: labelText,
                                    element: inputElement
                                });
                            }
                        }
                    }
                }

                // 如果有空的 data-check 字段，给出提醒但允许继续
                if (emptyDataCheckFields.length > 0) {
                    var fieldList = emptyDataCheckFields.map(function(field) {
                        return '<li style="padding: 5px 0; border-bottom: 1px dashed #eee;"><i class="layui-icon layui-icon-radio" style="color: #FFB800; margin-right: 8px;"></i>' + field.label + '</li>';
                    }).join('');

                    layer.open({
                        type: 1,
                        title: '<i class="layui-icon layui-icon-tips" style="color: #FFB800;"></i> 建议填写字段',
                        content: '<div style="padding: 20px;">' +
                                '<div style="font-size: 16px; color: #333; margin-bottom: 15px; text-align: center;">' +
                                '<i class="layui-icon layui-icon-survey" style="font-size: 24px; color: #FFB800; margin-right: 8px;"></i>' +
                                '以下字段建议填写，有助于客户画像分析和精准报价：' +
                                '</div>' +
                                '<ul style="list-style: none; padding: 0; margin: 15px 0; max-height: 200px; overflow-y: auto;">' +
                                fieldList +
                                '</ul>' +
                                '<div style="font-size: 14px; color: #666; text-align: center; margin-top: 15px;">' +
                                '您可以选择现在填写，或稍后继续保存' +
                                '</div>' +
                                '</div>',
                        area: ['500px', 'auto'],
                        btn: ['现在填写', '继续保存'],
                        btnAlign: 'c',
                        btn1: function(index) {
                            console.log('btn1 被点击');
                            layer.close(index);

                            // 用户选择填写，跳转到第一个空字段
                            if (emptyDataCheckFields && emptyDataCheckFields.length > 0) {
                                var firstEmptyField = emptyDataCheckFields[0];
                                console.log('跳转到第一个空字段：', firstEmptyField.label);

                                try {
                                    // 先滚动到目标位置
                                    firstEmptyField.element.scrollIntoView({
                                        behavior: 'smooth',
                                        block: 'center'
                                    });

                                    // 延迟一点时间再聚焦和高亮，确保滚动完成
                                    setTimeout(function() {
                                        firstEmptyField.element.focus();

                                        if (firstEmptyField.element.type === 'text' || firstEmptyField.element.type === 'textarea') {
                                            firstEmptyField.element.select();
                                        }

                                        // 为所有类型的元素添加高亮效果
                                        highlightElement(firstEmptyField.element);
                                    }, 500);
                                } catch (e) {
                                    console.error('跳转到字段时出错：', e);
                                    layer.msg('跳转到字段时出错：' + e.message);
                                }
                            } else {
                                console.log('没有找到空字段');
                                layer.msg('没有找到需要填写的字段');
                            }
                        },
                        btn2: function(index) {
                            console.log('btn2 被点击');
                            layer.close(index);

                            try {
                                // 用户选择继续保存，设置跳过标志并继续提交
                                skipDataCheckValidation = true;
                                console.log('设置跳过验证标志，调用继续提交函数');
                                continueFormSubmissionProcess();
                            } catch (e) {
                                console.error('继续提交时出错：', e);
                                layer.msg('继续提交时出错：' + e.message);
                            }
                        }
                    });

                    return false; // 暂停提交，等待用户选择
                }
                continueFormSubmissionProcess();
            },
            success: function (ret) {
                // 关闭加载提示
                layer.close(index);

                if (ret.state === "ok") {
                    layer.msg("保存成功！", {
                        icon: 1,
                        time: 2000
                    }, function() {
                        // 可以选择刷新页面或其他操作
                        // window.location.reload();
                    });
                } else {
                    layer.open({
                        type: 1,
                        title: "历史询价单",
                        content: ret.msg
                    });
                }

                // 恢复按钮状态
                var $submitBtn = $('#submitButton');
                $submitBtn.html('立即提交').prop('disabled', false);
                isFormSubmitting = false;

                // 重置跳过验证标志
                skipDataCheckValidation = false;
            },
            error: function (data) {
                // 关闭加载提示
                layer.close(index);
                layer_alert("网络操作失败，请咨询老柳! QQ75272683");

                // 恢复按钮状态
                var $submitBtn = $('#submitButton');
                $submitBtn.html('立即提交').prop('disabled', false);
                isFormSubmitting = false;

                // 重置跳过验证标志
                skipDataCheckValidation = false;
            }
        });
    });

    // 检查并显示空字段提醒的函数
    function checkAndShowEmptyFieldsReminder() {
        console.log('开始检查页面空字段');

        var emptyDataCheckFields = [];
        var dataCheckLabels = document.querySelectorAll('label.data-check');

        console.log('找到 data-check 标签数量：', dataCheckLabels.length);

        for (var j = 0; j < dataCheckLabels.length; j++) {
            var label = dataCheckLabels[j];
            var labelText = label.textContent || label.innerText;

            // 清理标签文本，移除多余的空白字符和图标
            labelText = labelText.replace(/\s+/g, ' ').trim();

            // 查找关联的输入元素
            var inputElement = null;
            var inputBlock = label.nextElementSibling;

            if (inputBlock && inputBlock.classList.contains('layui-input-block')) {
                // 查找输入框或选择框
                inputElement = inputBlock.querySelector('input, select, textarea');
            }

            if (inputElement) {
                var value = inputElement.value;

                // 检查值是否为空
                if (value == undefined || value == null || value.trim() == "") {
                    emptyDataCheckFields.push({
                        label: labelText,
                        element: inputElement
                    });
                }
            }
        }

        console.log('找到空字段数量：', emptyDataCheckFields.length);

        // 如果有空的 data-check 字段，给出提醒
        if (emptyDataCheckFields.length > 0) {
            var fieldList = emptyDataCheckFields.map(function(field) {
                return '<li style="padding: 5px 0; border-bottom: 1px dashed #eee;"><i class="layui-icon layui-icon-radio" style="color: #FFB800; margin-right: 8px;"></i>' + field.label + '</li>';
            }).join('');

            layer.open({
                type: 1,
                title: '<i class="layui-icon layui-icon-tips" style="color: #FFB800;"></i> 建议填写字段',
                content: '<div style="padding: 20px;">' +
                        '<div style="font-size: 16px; color: #333; margin-bottom: 15px; text-align: center;">' +
                        '<i class="layui-icon layui-icon-survey" style="font-size: 24px; color: #FFB800; margin-right: 8px;"></i>' +
                        '以下字段建议填写，有助于客户画像分析和精准报价：' +
                        '</div>' +
                        '<ul style="list-style: none; padding: 0; margin: 15px 0; max-height: 200px; overflow-y: auto;">' +
                        fieldList +
                        '</ul>' +
                        '<div style="font-size: 14px; color: #666; text-align: center; margin-top: 15px;">' +
                        '您可以选择现在填写，或稍后再处理' +
                        '</div>' +
                        '</div>',
                area: ['500px', 'auto'],
                btn: ['现在填写', '稍后处理'],
                btnAlign: 'c',
                btn1: function(index) {
                    console.log('页面加载提醒 - btn1 被点击');
                    layer.close(index);

                    // 用户选择填写，跳转到第一个空字段
                    if (emptyDataCheckFields && emptyDataCheckFields.length > 0) {
                        var firstEmptyField = emptyDataCheckFields[0];
                        console.log('跳转到第一个空字段：', firstEmptyField.label);

                        try {
                            // 先滚动到目标位置
                            firstEmptyField.element.scrollIntoView({
                                behavior: 'smooth',
                                block: 'center'
                            });

                            // 延迟一点时间再聚焦和高亮，确保滚动完成
                            setTimeout(function() {
                                firstEmptyField.element.focus();

                                if (firstEmptyField.element.type === 'text' || firstEmptyField.element.type === 'textarea') {
                                    firstEmptyField.element.select();
                                }

                                // 为所有类型的元素添加高亮效果
                                highlightElement(firstEmptyField.element);
                            }, 500);
                        } catch (e) {
                            console.error('跳转到字段时出错：', e);
                            layer.msg('跳转到字段时出错：' + e.message);
                        }
                    }
                },
                btn2: function(index) {
                    console.log('页面加载提醒 - btn2 被点击');
                    layer.close(index);
                    // 用户选择稍后处理，直接关闭弹窗
                }
            });
        } else {
            console.log('没有找到空的 data-check 字段');
        }
    }

    // 继续提交表单的函数
    function continueFormSubmissionProcess() {
        console.log('continueFormSubmissionProcess 函数开始执行');

        try {
            // 设置提交状态
            isFormSubmitting = true;
            console.log('设置 isFormSubmitting = true');

            // 更新提交按钮状态
            var $submitBtn = $('#submitButton');
            console.log('找到提交按钮：', $submitBtn.length);
            var originalText = $submitBtn.html();
            $submitBtn.html('<i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i> 保存中...').prop('disabled', true);
            console.log('更新按钮状态完成');

            // 显示保存中的提示
            index = layer.load(2, {
                shade: [0.3, '#000'],
                content: '正在保存客户信息...',
                success: function(layero) {
                    layero.find('.layui-layer-content').css({
                        'padding-top': '40px',
                        'width': '200px'
                    });
                }
            });
            console.log('显示加载提示完成');

            #role("权限管理员", "超级管理员", "总经理")
            try {
                if (document.getElementById('qzbcCheckBox')) {
                    if (document.getElementById('qzbcCheckBox').checked) {
                        document.getElementById('yxkh.qzbc').value = 1;
                    } else {
                        document.getElementById('yxkh.qzbc').value = 0;
                    }
                    console.log('qzbc 字段处理完成');
                }
            } catch (e) {
                console.log('qzbc 字段处理出错：', e);
            }
            #end
            #role("权限管理员", "超级管理员", "总经理")
            try {
                if (document.getElementById("yxkh.fg") && typeof ywySelect !== 'undefined') {
                    document.getElementById("yxkh.fg").value = ywySelect.getValue("valueStr").replace(/,/g, ";");
                    console.log('fg 字段处理完成');
                }
            } catch (e) {
                console.log('fg 字段处理出错：', e);
            }
            #end

            // 手动提交表单
            console.log('准备提交表单');
            var $form = $("#mainForm");
            console.log('找到表单：', $form.length);

            if ($form.length > 0) {
                console.log('开始提交表单，跳过验证');
                // 设置提交状态，确保不会重复验证
                isFormSubmitting = true;
                // 直接调用 ajaxForm 的提交，但跳过 beforeSerialize 验证
                $form.ajaxSubmit({
                    dataType: "json",
                    beforeSubmit: function() {
                        console.log('beforeSubmit: 开始提交表单数据');
                        return true;
                    },
                    success: function (ret) {
                        console.log('表单提交成功：', ret);
                        // 关闭加载提示
                        layer.close(index);

                        if (ret.state === "ok") {
                            layer.msg("保存成功！", {
                                icon: 1,
                                time: 2000
                            }, function() {
                                // 可以选择刷新页面或其他操作
                                window.location.reload();
                            });
                        }else {
                            layer.open({
                                type: 1,
                                title: "保存失败信息",
                                content: ret.msg
                            });
                        }

                        // 恢复按钮状态
                        var $submitBtn = $('#submitButton');
                        $submitBtn.html('立即提交').prop('disabled', false);
                        isFormSubmitting = false;

                        // 重置跳过验证标志
                        skipDataCheckValidation = false;
                    },
                    error: function (data) {
                        console.log('表单提交失败：', data);
                        // 关闭加载提示
                        layer.close(index);
                        layer_alert("网络操作失败，请咨询老柳! QQ75272683");

                        // 恢复按钮状态
                        var $submitBtn = $('#submitButton');
                        $submitBtn.html('立即提交').prop('disabled', false);
                        isFormSubmitting = false;

                        // 重置跳过验证标志
                        skipDataCheckValidation = false;
                    }
                });
                console.log('表单提交命令已发送');
            } else {
                console.error('未找到表单元素');
                layer.msg('未找到表单元素，无法提交');
            }
        } catch (e) {
            console.error('continueFormSubmissionProcess 执行出错：', e);
            layer.msg('提交过程出错：' + e.message);

            // 恢复按钮状态
            var $submitBtn = $('#submitButton');
            $submitBtn.html('立即提交').prop('disabled', false);
            isFormSubmitting = false;

            // 关闭加载提示
            if (typeof index !== 'undefined') {
                layer.close(index);
            }
        }
    }

    // 高亮元素的函数 - 使用多种方法确保可见性
    function highlightElement(element) {
        if (!element) {
            console.log('highlightElement: 元素为空');
            return;
        }

        console.log('开始高亮元素：', element.tagName, element.type, element.id, element.className);

        // 方法1: 创建一个明显的覆盖层 - 使用舒服的蓝色
        var overlay = document.createElement('div');
        overlay.style.position = 'absolute';
        overlay.style.border = '3px solid #1890FF';
        overlay.style.backgroundColor = 'rgba(24, 144, 255, 0.1)';
        overlay.style.boxShadow = '0 0 15px rgba(24, 144, 255, 0.6)';
        overlay.style.zIndex = '99999';
        overlay.style.pointerEvents = 'none';
        overlay.style.borderRadius = '4px';
        overlay.className = 'highlight-overlay';

        // 获取元素位置和大小
        var rect = element.getBoundingClientRect();
        var scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        var scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

        overlay.style.top = (rect.top + scrollTop - 5) + 'px';
        overlay.style.left = (rect.left + scrollLeft - 5) + 'px';
        overlay.style.width = (rect.width + 10) + 'px';
        overlay.style.height = (rect.height + 10) + 'px';

        document.body.appendChild(overlay);
        console.log('创建覆盖层，位置：', overlay.style.top, overlay.style.left, overlay.style.width, overlay.style.height);

        // 方法2: 尝试高亮原始元素 - 使用蓝色主题
        element.classList.add('field-highlight');
        element.style.setProperty('border', '3px solid #1890FF', 'important');
        element.style.setProperty('box-shadow', '0 0 15px rgba(24, 144, 255, 0.6)', 'important');
        element.style.setProperty('background-color', '#E6F7FF', 'important');

        // 方法3: 查找并高亮所有可能的Layui包装器
        var possibleWrappers = [
            element.closest('.layui-form-select'),
            element.closest('.layui-input-block'),
            element.parentElement
        ];

        possibleWrappers.forEach(function(wrapper, index) {
            if (wrapper && wrapper !== element) {
                console.log('找到包装器' + index + '：', wrapper.className);
                wrapper.style.setProperty('border', '3px solid #1890FF', 'important');
                wrapper.style.setProperty('box-shadow', '0 0 15px rgba(24, 144, 255, 0.6)', 'important');
                wrapper.style.setProperty('background-color', '#E6F7FF', 'important');
                wrapper.classList.add('field-highlight');
            }
        });

        // 移除箭头功能，只保留有效的覆盖层高亮
        console.log('跳过箭头创建，只使用覆盖层高亮效果');

        // 方法5: 添加脉冲动画到覆盖层 - 只闪两下
        var pulseCount = 0;
        var pulseInterval = setInterval(function() {
            if (pulseCount >= 4) { // 4次变化 = 2次完整闪动
                clearInterval(pulseInterval);
                // 恢复到默认状态
                overlay.style.transform = 'scale(1)';
                overlay.style.borderColor = '#1890FF';
                return;
            }

            if (pulseCount % 2 === 0) {
                overlay.style.transform = 'scale(1.05)';
                overlay.style.borderColor = '#0050B3';
            } else {
                overlay.style.transform = 'scale(1)';
                overlay.style.borderColor = '#1890FF';
            }
            pulseCount++;
        }, 200); // 缩短间隔时间到200ms

        // 5秒后清理所有高亮效果
        setTimeout(function() {
            console.log('开始清理高亮效果');

            // 清理覆盖层
            var overlays = document.querySelectorAll('.highlight-overlay');
            overlays.forEach(function(overlay) {
                overlay.remove();
            });

            // 清理元素样式
            element.classList.remove('field-highlight');
            element.style.border = '';
            element.style.boxShadow = '';
            element.style.backgroundColor = '';
            element.style.transform = '';

            // 清理所有可能的包装器样式
            var allWrappers = document.querySelectorAll('.field-highlight');
            allWrappers.forEach(function(wrapper) {
                wrapper.classList.remove('field-highlight');
                wrapper.style.border = '';
                wrapper.style.boxShadow = '';
                wrapper.style.backgroundColor = '';
            });

            console.log('高亮效果清理完成');
        }, 5000);
    }

    function changeJczyx(jczyx) {
        $.ajax({
            url: '/my/yxkh/changeJczyx',
            type: 'POST',
            data: {
                "id": '#(yxkh.id??"")',
                "jczyx": jczyx
            },
            cache: false,
            dataType: 'json',
            success: function (data) {
                console.log(data);
                if (data.msg.length > 5) {
                    layer_alert(data.msg);
                } else {
                }
            },
            error: function (data) {
                layer_alert(data.msg);
            }
        });
    }

    function changeDa(id, obj) {
        $.ajax({
            url: '/my/yxkh/modifyDa',
            type: 'POST',
            data: {
                "id": "#(yxkh.id??)",
                "wtid": id,
                "da": obj
            },
            cache: false,
            dataType: 'json',
            success: function (data) {
            },
            error: function (data) {
                layer_alert(data.msg);
            }
        });
    }

    function changeQt(id, obj) {
        $.ajax({
            url: '/my/yxkh/modifyQt',
            type: 'POST',
            data: {
                "id": "#(yxkh.id??)",
                "wtid": id,
                "qt": obj
            },
            cache: false,
            dataType: 'json',
            success: function (data) {
            },
            error: function (data) {
                layer_alert(data.msg);
            }
        });
    }

    function changeBz(id, obj) {
        $.ajax({
            url: '/my/yxkh/modifyBz',
            type: 'POST',
            data: {
                "id": "#(yxkh.id??)",
                "wtid": id,
                "bz": obj
            },
            cache: false,
            dataType: 'json',
            success: function (data) {
            },
            error: function (data) {
                layer_alert(data.msg);
            }
        });
    }

    function changeXjbz(ddbh, obj) {
        $.ajax({
            url: '/my/yxkh/modifyXjbz',
            type: 'POST',
            data: {
                "id": "#(yxkh.id??)",
                "ddbh": ddbh,
                "bz": obj
            },
            cache: false,
            dataType: 'json',
            success: function (data) {
            },
            error: function (data) {
                layer_alert(data.msg);
            }
        });
    }

    function add() {
        $.ajax({
            url: '/my/yxkh/addLxr',
            type: 'POST',
            data: {
                "id": "#(yxkh.id??)",
                "yxkhlxr.xm": $("#yxkhlxr\\.xm").val(),
                "yxkhlxr.xb": $("#yxkhlxr\\.xb").val(),
                "yxkhlxr.email": $("#yxkhlxr\\.email").val(),
                "yxkhlxr.zw": $("#yxkhlxr\\.zw").val(),
                "yxkhlxr.bz": $("#yxkhlxr\\.bz").val()
            },
            cache: false,
            dataType: 'json',
            success: function (data) {
                location.href = "/my/yxkh/edit?id=#(yxkh.id??)";
            },
            error: function (data) {
                layer_alert(data.msg);
            }
        });
    }

    function addXj() {
        $.ajax({
            url: '/my/yxkh/addXj',
            type: 'POST',
            data: {
                "id": "#(yxkh.id??)",
                "ddbh": $("#xj\\.ddbh").val(),
                "bz": $("#xj\\.bz").val()
            },
            cache: false,
            dataType: 'json',
            success: function (data) {
            },
            error: function (data) {
                layer_alert(data.msg);
            }
        });
    }

    function changeGtfs() {
        layer.open({
            type: 1,
            title: "沟通方式",
            content: $('#gtfsModal'),
            btn: ['确定', '关闭'],
            yes: function (index, layero) {
                changeGtjy();
                layer.close(index);
            }
        });
    }

    function changeGtjy() {
        $.ajax({
            url: "/my/yxkh/changeJybz",
            type: "POST",
            cache: false,
            dataType: "json",
            data: {
                "id": '#(yxkh.id??)',
                "gtfs": document.getElementById("gtfs").value,
                "value": document.getElementById("gtjy").value
            },
            before: function () {
                layer.load(0, { shade: false });
            },
            success: function (ret) {
                layer_alert("修改成功");
            },
            fail: function (ret) {
                layer_alert("修改失败");
            }
        });
    }

    // 测试函数
    function testFunction() {
        console.log('测试函数被调用');
        alert('测试函数正常工作！');

        // 测试是否能获取到决策手机号码
        var phoneField = document.getElementById('yxkh.jcsj');
        if (phoneField) {
            console.log('找到决策手机输入框，值为：', phoneField.value);
            alert('决策手机输入框值：' + phoneField.value);
        } else {
            console.log('未找到决策手机输入框');
            alert('未找到决策手机输入框');
        }
    }

    // 打开拨打电话弹出界面
    function openCallModal() {
        console.log('openCallModal 函数被调用');
        try {
            var phoneField = document.getElementById('yxkh.jcsj').value;
            console.log('获取到的电话号码：', phoneField);

            if (!phoneField || phoneField.trim() === '') {
                alert('请先填写决策手机号码');
                return;
            }

            // 解析电话号码
            parsePhoneNumbers(phoneField);

            // 生成二维码
            generateQRCode();

            // 显示弹出界面
            layer.open({
                type: 1,
                title: "拨打电话",
                content: $('#callModal'),
                area: ['850px', '650px'],
                success: function () {
                    // 弹窗打开后自动加载保存的主叫号码
                    loadSavedCallerNumber();
                },
                end: function () {
                    document.getElementById("callModal").style.display = "none";
                }
            });
        } catch (e) {
            console.error('openCallModal 函数执行出错：', e);
            alert('打开拨打电话界面时出错：' + e.message);
        }
    }

    // 解析电话号码
    function parsePhoneNumbers(phoneString) {
        console.log('解析电话号码：', phoneString);
        try {
            // 清空之前的内容
            $('#phoneNumbers').empty();

            if (!phoneString) {
                $('#phoneNumbers').html('<p>没有找到电话号码</p>');
                return;
            }

            // 使用分号分割电话号码
            var phones = phoneString.split(';');
            var phoneHtml = '';

            phones.forEach(function (phone) {
                phone = phone.trim();
                if (phone !== '') {
                    phoneHtml += '<div style="margin-bottom: 10px;">';
                    phoneHtml += '<a href="javascript:void(0);" onclick="selectPhone(\'' + phone + '\')" class="layui-btn layui-btn-sm layui-btn-normal">';
                    phoneHtml += '<i class="layui-icon layui-icon-cellphone"></i> ' + phone + '</a>';
                    phoneHtml += '</div>';
                }
            });

            if (phoneHtml === '') {
                phoneHtml = '<p>没有找到有效的电话号码</p>';
            }

            console.log('生成的电话号码HTML：', phoneHtml);
            $('#phoneNumbers').html(phoneHtml);
        } catch (e) {
            console.error('解析电话号码出错：', e);
        }
    }

    // 选择电话号码
    function selectPhone(phone) {
        document.getElementById('callees').value = phone;
    }

    // 生成二维码
    function generateQRCode() {
        var customerId = '#(yxkh.id??)';
        var qrUrl = 'http://360.theolympiastone.com/my/yxkh/uploadVoiceRecord?id=' + customerId;

        // 清空之前的二维码
        $('#qrcode').empty();

        // 使用在线二维码生成服务
        var qrCodeImg = '<img src="https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=' +
            encodeURIComponent(qrUrl) + '" alt="二维码" style="border: 1px solid #ddd;">';
        $('#qrcode').html(qrCodeImg);

        // 添加URL文本显示
        $('#qrcode').append('<div style="margin-top: 10px; font-size: 12px; color: #666; word-break: break-all;">' + qrUrl + '</div>');
    }

    // 拨打电话
    function call() {
        var caller = $("#caller").val();
        var callees = $("#callees").val();

        if (!caller || caller.trim() === '') {
            layer_alert('请输入主叫号码');
            return;
        }

        if (!callees || callees.trim() === '') {
            layer_alert('请输入被叫号码');
            return;
        }

        caller = caller.replace(/\s/gi, '');
        callees = callees.replace(/\s/gi, '');

        var param = "caller=" + caller + "&callees=" + callees;

        // 使用后端代理接口，避免跨域问题
        $.ajax({
            type: "POST",
            url: "/my/yxkh/callProxy",
            data: {
                'caller': caller,
                'callees': callees
            },
            cache: false,
            dataType: 'json',
            timeout: 20000, // 20秒超时
            success: function (result) {
                console.log('电话拨打响应：', result);
                if (result.state === 'ok') {
                    layer_alert(result.msg || "拨打成功，请接听系统来电");
                } else {
                    layer_alert(result.msg || "拨打失败，请稍后重试");
                }
            },
            error: function (xhr, status, error) {
                console.log('电话拨打请求出错：', status, error);
                if (status === 'timeout') {
                    layer_alert("请求超时，请稍后重试");
                } else if (xhr.status >= 500) {
                    layer_alert("服务器内部错误，请稍后重试");
                } else if (xhr.status === 404) {
                    layer_alert("电话服务接口未找到，请联系管理员");
                } else {
                    layer_alert("网络异常(状态码:" + xhr.status + ")，请稍后重试");
                }
            }
        });
    }

    // 保存主叫号码到本地存储
    function saveCallerNumber() {
        var caller = $("#caller").val().trim();
        if (!caller) {
            layer_alert('请先输入主叫号码');
            return;
        }

        localStorage.setItem('saved_caller_number', caller);
        layer_alert('号码已保存到本地');
    }

    // 加载保存的主叫号码
    function loadSavedCallerNumber() {
        var savedCaller = localStorage.getItem('saved_caller_number');
        if (savedCaller) {
            $("#caller").val(savedCaller);
        }
    }

    // 清除保存的主叫号码
    function clearSavedCallerNumber() {
        localStorage.removeItem('saved_caller_number');
        $("#caller").val('');
        layer_alert('已清除保存的号码');
    }

    // 打开录音文件列表弹出界面
    function openVoiceRecordsModal() {
        console.log('打开录音文件列表');
        try {
            // 显示弹出界面
            layer.open({
                type: 1,
                title: "录音文件管理",
                content: $('#voiceRecordsModal'),
                area: ['850px', '650px'],
                success: function () {
                    // 弹窗打开后加载录音文件列表
                    loadVoiceRecords();
                },
                end: function () {
                    document.getElementById("voiceRecordsModal").style.display = "none";
                }
            });
        } catch (e) {
            console.error('打开录音文件列表出错：', e);
            alert('打开录音文件列表时出错：' + e.message);
        }
    }

    // 加载录音文件列表
    function loadVoiceRecords() {
        var customerId = '#(yxkh.id??)';
        if (!customerId) {
            $('#voiceRecordsList').html('<div style="text-align: center; color: #999; padding: 50px;"><p>客户ID不存在</p></div>');
            return;
        }

        $.ajax({
            url: '/my/yxkh/getVoiceRecords',
            type: 'POST',
            data: {
                'customerId': customerId
            },
            cache: false,
            dataType: 'json',
            success: function (data) {
                console.log('录音文件数据：', data);
                displayVoiceRecords(data);
            },
            error: function () {
                $('#voiceRecordsList').html('<div style="text-align: center; color: #ff5722; padding: 50px;"><p>加载录音文件失败，请稍后重试</p></div>');
            }
        });
    }

    // 显示录音文件列表
    function displayVoiceRecords(voiceRecords) {
        var listHtml = '';

        if (!voiceRecords || voiceRecords.length === 0) {
            listHtml = '<div style="text-align: center; color: #999; padding: 50px;">' +
                '<i class="layui-icon layui-icon-voice" style="font-size: 48px; color: #ccc;"></i>' +
                '<p style="margin-top: 15px;">该客户暂无录音文件</p>' +
                '<p style="color: #ccc; font-size: 12px;">点击下方"上传新录音"按钮添加录音文件</p>' +
                '</div>';
        } else {
            listHtml = '<table class="layui-table" lay-size="sm">' +
                '<thead>' +
                '<tr>' +
                '<th width="20%">上传时间</th>' +
                '<th width="25%">文件名</th>' +
                '<th width="30%">备注</th>' +
                '<th width="25%">操作</th>' +
                '</tr>' +
                '</thead>' +
                '<tbody>';

            for (var i = 0; i < voiceRecords.length; i++) {
                var record = voiceRecords[i];
                listHtml += '<tr>' +
                    '<td>' + (record.uploadTime || '未知') + '</td>' +
                    '<td style="word-break: break-all;">' + (record.fileName || '未知文件') + '</td>' +
                    '<td style="word-break: break-all;">' + (record.remark || '无备注') + '</td>' +
                    '<td>' +
                    '<button type="button" class="layui-btn layui-btn-xs layui-btn-normal" onclick="playAudio(\'' + record.filePath + '\', \'' + record.fileName + '\', \'' + (record.remark || '') + '\')">' +
                    '<i class="layui-icon layui-icon-play"></i> 播放' +
                    '</button> ' +
                    '<button type="button" class="layui-btn layui-btn-xs layui-btn-primary" onclick="downloadAudio(\'' + record.filePath + '\', \'' + record.fileName + '\')">' +
                    '<i class="layui-icon layui-icon-download-circle"></i> 下载' +
                    '</button> ' +
                    '<button type="button" class="layui-btn layui-btn-xs layui-btn-danger" onclick="deleteVoiceRecord(' + record.id + ', \'' + record.fileName + '\')">' +
                    '<i class="layui-icon layui-icon-delete"></i> 删除' +
                    '</button>' +
                    '</td>' +
                    '</tr>';
            }
            listHtml += '</tbody></table>';
        }

        $('#voiceRecordsList').html(listHtml);
    }

    // 播放音频
    function playAudio(filePath, fileName, remark) {
        console.log('播放音频：', filePath);

        // 设置音频播放器
        var audioPlayer = document.getElementById('audioPlayer');
        var audioTitle = document.getElementById('audioTitle');
        var audioInfo = document.getElementById('audioInfo');

        audioPlayer.src = filePath;
        audioTitle.innerHTML = '<i class="layui-icon layui-icon-play"></i> ' + fileName;
        audioInfo.innerHTML = remark ? '备注：' + remark : '无备注';

        // 显示播放器弹窗
        layer.open({
            type: 1,
            title: "音频播放器",
            content: $('#audioPlayerModal'),
            area: ['550px', '250px'],
            success: function () {
                // 自动播放（某些浏览器可能需要用户交互才能播放）
                audioPlayer.play().catch(function (error) {
                    console.log('自动播放失败，需要用户手动点击播放按钮');
                });
            },
            end: function () {
                // 关闭弹窗时停止播放
                audioPlayer.pause();
                audioPlayer.currentTime = 0;
                document.getElementById("audioPlayerModal").style.display = "none";
            }
        });
    }

    // 下载音频
    function downloadAudio(filePath, fileName) {
        var link = document.createElement('a');
        link.href = filePath;
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    // 删除录音记录
    function deleteVoiceRecord(recordId, fileName) {
        layer.confirm('确定要删除录音文件 "' + fileName + '" 吗？', {
            icon: 3,
            title: '删除确认'
        }, function (index) {
            $.ajax({
                url: '/my/yxkh/deleteVoiceRecord',
                type: 'POST',
                data: {
                    'id': recordId
                },
                cache: false,
                dataType: 'json',
                success: function (data) {
                    if (data.state === 'ok') {
                        layer.msg('删除成功', { icon: 1 });
                        // 重新加载列表
                        loadVoiceRecords();
                    } else {
                        layer.msg('删除失败：' + (data.msg || '未知错误'), { icon: 2 });
                    }
                },
                error: function () {
                    layer.msg('删除失败，请稍后重试', { icon: 2 });
                }
            });
            layer.close(index);
        });
    }

    // 上传新录音
    function uploadNewVoice() {
        var customerId = '#(yxkh.id??)';
        var uploadUrl = '/my/yxkh/uploadVoiceRecord?id=' + customerId;

        layer.confirm('是否前往录音上传页面？', {
            icon: 3,
            title: '跳转确认',
            btn: ['新窗口打开', '当前窗口打开', '取消']
        }, function (index) {
            // 新窗口打开
            window.open(uploadUrl, '_blank');
            layer.close(index);
        }, function (index) {
            // 当前窗口打开
            window.location.href = uploadUrl;
            layer.close(index);
        });
    }

    // 刷新录音列表
    function refreshVoiceList() {
        $('#voiceRecordsList').html('<div style="text-align: center; color: #999; padding: 50px;">' +
            '<i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop" style="font-size: 30px;"></i>' +
            '<p style="margin-top: 10px;">正在刷新录音文件...</p>' +
            '</div>');
        loadVoiceRecords();
    }

    // 显示调研菜单（客户编辑页面专用）
    function showSurveyMenuInEdit() {
        var customerId = '#(yxkh.id??)';
        var customerName = "#(yxkh.gsmc??)";

        if (!customerId) {
            layer.msg('请先保存客户信息', {icon: 2});
            return;
        }

        var menuHtml = '<div style="padding: 25px; text-align: center;">' +
            '<h3 style="margin-bottom: 20px; color: #333;"><i class="layui-icon layui-icon-form"></i> 市场调研</h3>' +
            '<p style="margin-bottom: 20px; color: #666; font-size: 14px;">客户：' + customerName + '</p>' +
            '<div style="margin-bottom: 15px;">' +
            '<button onclick="openSurveyInEdit(' + customerId + ')" class="layui-btn layui-btn-normal" style="width: 160px; margin: 8px;">' +
            '<i class="layui-icon layui-icon-edit"></i> 填写调研问卷</button><br>' +
            '<button onclick="viewMySurveysInEdit(' + customerId + ')" class="layui-btn layui-btn-primary" style="width: 160px; margin: 8px;">' +
            '<i class="layui-icon layui-icon-search"></i> 查看我的调研</button><br>';

        #role("权限管理员", "超级管理员", "总经理")
            menuHtml += '<button onclick="viewAllSurveysInEdit(' + customerId + ')" class="layui-btn layui-btn-warm" style="width: 160px; margin: 8px;">' +
                       '<i class="layui-icon layui-icon-username"></i> 查看所有调研</button><br>' +
                       '<small style="color: #ff4d4f; font-size: 12px;">（管理员权限）</small><br>';
        #end

        menuHtml += '<button onclick="viewSurveyStatistics()" class="layui-btn layui-btn-disabled" style="width: 160px; margin: 8px;">' +
                   '<i class="layui-icon layui-icon-chart"></i> 调研统计总览</button>' +
                   '</div>' +
                   '<div style="margin-top: 15px; padding-top: 15px; border-top: 1px solid #f0f0f0;">' +
                   '<button onclick="openMobileEditInEdit(' + customerId + ')" class="layui-btn layui-btn-fluid" style="width: 160px; margin: 8px; background: #52c41a; border-color: #52c41a;">' +
                   '<i class="layui-icon layui-icon-cellphone"></i> 客户简版</button>' +
                   '</div>' +
                   '<div style="margin-top: 15px; padding-top: 15px; border-top: 1px solid #f0f0f0; color: #999; font-size: 12px;">' +
                   '💡 提示：调研问卷用于了解客户背景和需求<br>' +
                   '📱 客户简版：移动端优化的客户编辑界面' +
                   '</div></div>';

        layer.open({
            type: 1,
            title: false,
            closeBtn: 1,
            area: ['320px', 'auto'],
            content: menuHtml,
            skin: 'layui-layer-rim',
            shadeClose: true
        });
    }

    function openSurveyInEdit(customerId) {
        layer.closeAll();
        window.open('/my/marketresearch/survey?customerId=' + customerId, '_blank');
    }

    function viewMySurveysInEdit(customerId) {
        layer.closeAll();
        window.open('/my/marketresearch/viewAnswers?customerId=' + customerId, '_blank');
    }

    function viewAllSurveysInEdit(customerId) {
        layer.closeAll();
        window.open('/my/marketresearch/viewAllAnswers?customerId=' + customerId, '_blank');
    }

    function viewSurveyStatistics() {
        layer.closeAll();
        window.open('/my/marketresearch/statistics', '_blank');
    }

    function openMobileEditInEdit(customerId) {
        layer.closeAll();
        window.open('/my/yxkh/editMobile?id=' + customerId, '_blank');
    }

    // 加载调研概览数据
    function loadSurveyOverview() {
        var customerId = '#(yxkh.id??)';
        if (!customerId) {
            $('#surveyOverview').html('<div style="text-align: center; color: #999; padding: 20px;">请先保存客户信息</div>');
            return;
        }

        $.ajax({
            url: '/my/marketresearch/getSurveyOverview',
            type: 'GET',
            data: { customerId: customerId },
            success: function(ret) {
                if (ret.state === 'ok') {
                    renderSurveyOverview(ret.data);
                } else {
                    $('#surveyOverview').html('<div style="text-align: center; color: #ff4d4f; padding: 20px;">加载失败：' + (ret.msg || '未知错误') + '</div>');
                }
            },
            error: function() {
                $('#surveyOverview').html('<div style="text-align: center; color: #ff4d4f; padding: 20px;">网络错误，请刷新重试</div>');
            }
        });
    }

    // 渲染调研概览
    function renderSurveyOverview(data) {
        var html = '<div class="layui-row layui-col-space15">';

        // 统计信息
        html += '<div class="layui-col-md3">' +
                '<div style="text-align: center; padding: 15px; background: #f8f9fa; border-radius: 5px;">' +
                '<div style="font-size: 24px; font-weight: bold; color: #1890ff;">' + (data.totalQuestions || 0) + '</div>' +
                '<div style="color: #666; font-size: 12px;">总问题数</div>' +
                '</div></div>';

        html += '<div class="layui-col-md3">' +
                '<div style="text-align: center; padding: 15px; background: #f8f9fa; border-radius: 5px;">' +
                '<div style="font-size: 24px; font-weight: bold; color: #52c41a;">' + (data.answeredQuestions || 0) + '</div>' +
                '<div style="color: #666; font-size: 12px;">已回答</div>' +
                '</div></div>';

        html += '<div class="layui-col-md3">' +
                '<div style="text-align: center; padding: 15px; background: #f8f9fa; border-radius: 5px;">' +
                '<div style="font-size: 24px; font-weight: bold; color: #faad14;">' + (data.surveyorCount || 0) + '</div>' +
                '<div style="color: #666; font-size: 12px;">调研员数</div>' +
                '</div></div>';

        html += '<div class="layui-col-md3">' +
                '<div style="text-align: center; padding: 15px; background: #f8f9fa; border-radius: 5px;">' +
                '<div style="font-size: 24px; font-weight: bold; color: #722ed1;">' +
                (data.completionRate ? data.completionRate + '%' : '0%') + '</div>' +
                '<div style="color: #666; font-size: 12px;">完成率</div>' +
                '</div></div>';

        html += '</div>';

        // 最近调研记录
        if (data.recentAnswers && data.recentAnswers.length > 0) {
            html += '<div style="margin-top: 15px; padding-top: 15px; border-top: 1px solid #f0f0f0;">' +
                    '<h4 style="margin-bottom: 10px; color: #333;">最近调研记录</h4>';

            for (var i = 0; i < Math.min(3, data.recentAnswers.length); i++) {
                var answer = data.recentAnswers[i];
                html += '<div style="margin-bottom: 8px; padding: 8px; background: #fafafa; border-radius: 3px; font-size: 12px;">' +
                        '<span style="color: #1890ff; font-weight: bold;">' + answer.question_code + '</span> ' +
                        '<span style="color: #666;">' + (answer.answer_content ? answer.answer_content.substring(0, 50) + (answer.answer_content.length > 50 ? '...' : '') : '无答案') + '</span>' +
                        '<span style="float: right; color: #999;">' + answer.surveyor_username + ' ' + answer.survey_date + '</span>' +
                        '</div>';
            }

            html += '</div>';
        } else {
            html += '<div style="margin-top: 15px; padding: 15px; text-align: center; color: #999; background: #fafafa; border-radius: 5px;">' +
                    '<i class="layui-icon layui-icon-survey"></i> 暂无调研记录，点击上方按钮开始调研' +
                    '</div>';
        }

        $('#surveyOverview').html(html);
    }

    // 调研答案相关功能
    var surveyAnswersData = {}; // 缓存调研答案数据

    // 显示调研答案
    function showSurveyAnswers(questionCode, fieldName) {
        var customerId = '#(yxkh.id??)';
        if (!customerId) {
            layer.msg('请先保存客户信息', {icon: 2});
            return;
        }

        var container = $('#survey-answers-' + questionCode);

        // 如果已经显示，则隐藏
        if (container.is(':visible')) {
            container.slideUp(300);
            return;
        }

        // 如果已有数据，直接显示
        if (surveyAnswersData[questionCode]) {
            renderSurveyAnswers(questionCode, fieldName, surveyAnswersData[questionCode]);
            container.slideDown(300);
            return;
        }

        // 加载数据
        container.html('<div style="text-align: center; padding: 15px; color: #999;">' +
                      '<i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i> 正在加载调研答案...</div>');
        container.show();

        $.ajax({
            url: '/my/yxkh/getSurveyAnswersForFields',
            type: 'GET',
            data: { customerId: customerId },
            success: function(ret) {
                if (ret.state === 'ok') {
                    surveyAnswersData = ret.data;
                    renderSurveyAnswers(questionCode, fieldName, surveyAnswersData[questionCode] || []);
                } else {
                    container.html('<div style="text-align: center; color: #ff4d4f; padding: 15px;">加载失败：' + (ret.msg || '未知错误') + '</div>');
                }
            },
            error: function() {
                container.html('<div style="text-align: center; color: #ff4d4f; padding: 15px;">网络错误，请重试</div>');
            }
        });
    }

    // 渲染调研答案
    function renderSurveyAnswers(questionCode, fieldName, answers) {
        var container = $('#survey-answers-' + questionCode);

        if (!answers || answers.length === 0) {
            container.html('<div class="survey-no-answers">暂无调研答案</div>');
            return;
        }

        var html = '<div class="survey-answers-header">' +
                   '<span class="survey-answers-title">调研答案 (' + questionCode + ')</span>' +
                   '<span class="survey-answers-close" onclick="hideSurveyAnswers(\'' + questionCode + '\')">&times;</span>' +
                   '</div>';

        answers.forEach(function(answer, index) {
            var surveyorName = answer.surveyor_nick_name || answer.surveyor_username || '未知';
            var surveyDate = answer.survey_date || '未知日期';
            var content = answer.answer_content || '';

            html += '<div class="survey-answer-item">' +
                    '<div class="survey-answer-header">' +
                    '<span class="survey-answer-surveyor">' + surveyorName + '</span>' +
                    '<span class="survey-answer-date">' + surveyDate + '</span>' +
                    '</div>' +
                    '<div class="survey-answer-content">' + content + '</div>' +
                    '<div class="survey-answer-actions">' +
                    '<button class="survey-answer-btn copy-btn" onclick="copySurveyAnswer(\'' + content.replace(/'/g, "\\'") + '\')">复制</button>' +
                    '<button class="survey-answer-btn select-btn" onclick="selectSurveyAnswer(\'' + fieldName + '\', \'' + content.replace(/'/g, "\\'") + '\')">选择</button>' +
                    '</div>' +
                    '</div>';
        });

        container.html(html);
    }

    // 隐藏调研答案
    function hideSurveyAnswers(questionCode) {
        $('#survey-answers-' + questionCode).slideUp(300);
    }

    // 复制调研答案到剪贴板
    function copySurveyAnswer(content) {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(content).then(function() {
                layer.msg('已复制到剪贴板', {icon: 1, time: 1000});
            }).catch(function() {
                fallbackCopyTextToClipboard(content);
            });
        } else {
            fallbackCopyTextToClipboard(content);
        }
    }

    // 兼容性复制方法
    function fallbackCopyTextToClipboard(text) {
        var textArea = document.createElement("textarea");
        textArea.value = text;
        textArea.style.position = "fixed";
        textArea.style.top = "-1000px";
        textArea.style.left = "-1000px";
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
            var successful = document.execCommand('copy');
            if (successful) {
                layer.msg('已复制到剪贴板', {icon: 1, time: 1000});
            } else {
                layer.msg('复制失败，请手动复制', {icon: 2});
            }
        } catch (err) {
            layer.msg('复制失败，请手动复制', {icon: 2});
        }

        document.body.removeChild(textArea);
    }

    // 选择调研答案填入字段
    function selectSurveyAnswer(fieldName, content) {
        var field = $('[name="' + fieldName + '"]');
        if (field.length > 0) {
            if (field.is('select')) {
                // 对于下拉框，尝试匹配选项
                var options = field.find('option');
                var matched = false;
                options.each(function() {
                    var optionText = $(this).text().toLowerCase();
                    var contentLower = content.toLowerCase();
                    if (optionText.includes(contentLower) || contentLower.includes(optionText)) {
                        field.val($(this).val());
                        matched = true;
                        return false;
                    }
                });
                if (!matched) {
                    layer.msg('未找到匹配的选项，请手动选择', {icon: 3});
                }
            } else {
                // 对于文本框，直接填入
                field.val(content);
            }

            // 触发change事件
            field.trigger('change');
            layer.msg('已填入字段', {icon: 1, time: 1000});

            // 隐藏答案面板
            $('.survey-answers-container:visible').slideUp(300);
        } else {
            layer.msg('未找到对应字段', {icon: 2});
        }
    }

    // 显示交货方式指南
    function showDeliveryGuide() {
        var guideHtml = '<div style="padding: 25px; max-height: 600px; overflow-y: auto;">' +
            '<h2 style="text-align: center; color: #1890FF; margin-bottom: 25px;">' +
            '<i class="layui-icon layui-icon-ship" style="font-size: 24px; margin-right: 8px;"></i>' +
            '国际贸易交货方式指南</h2>' +

            '<div style="background: #f0f9ff; padding: 15px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #1890FF;">' +
            '<h4 style="color: #1890FF; margin: 0 0 8px 0;">💡 新手必读</h4>' +
            '<p style="margin: 0; color: #666; font-size: 13px;">选择合适的交货方式直接影响成本控制和风险管理，建议优先选择FOB或CIF</p>' +
            '</div>' +

            // FOB
            '<div class="delivery-card" style="background: #f6ffed; border: 1px solid #b7eb8f; border-radius: 8px; padding: 20px; margin-bottom: 15px;">' +
            '<div style="display: flex; align-items: center; margin-bottom: 12px;">' +
            '<h3 style="margin: 0; color: #52c41a; flex: 1;">FOB (Free On Board) - 船上交货</h3>' +
            '<div style="color: #faad14; font-size: 18px;">⭐⭐⭐⭐⭐</div>' +
            '</div>' +
            '<div style="margin-bottom: 12px;">' +
            '<span style="background: #52c41a; color: white; padding: 3px 8px; border-radius: 12px; font-size: 12px; margin-right: 8px;">最推荐</span>' +
            '<span style="background: #1890ff; color: white; padding: 3px 8px; border-radius: 12px; font-size: 12px;">风险最小</span>' +
            '</div>' +
            '<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 12px;">' +
            '<div>' +
            '<h4 style="color: #52c41a; margin: 0 0 8px 0;">✅ 优点</h4>' +
            '<ul style="margin: 0; padding-left: 18px; color: #333; font-size: 13px;">' +
            '<li>🛡️ 我方风险最小</li>' +
            '<li>💰 客户承担运费</li>' +
            '<li>📍 港口交货即完成</li>' +
            '<li>🎯 责任界限清晰</li>' +
            '</ul>' +
            '</div>' +
            '<div>' +
            '<h4 style="color: #ff4d4f; margin: 0 0 8px 0;">⚠️ 注意事项</h4>' +
            '<ul style="margin: 0; padding-left: 18px; color: #333; font-size: 13px;">' +
            '<li>🚢 客户需安排运输</li>' +
            '<li>📋 需要出口许可证</li>' +
            '<li>⏰ 装船时间要准确</li>' +
            '</ul>' +
            '</div>' +
            '</div>' +
            '<div style="background: #fff; padding: 12px; border-radius: 6px; border: 1px solid #d9f7be;">' +
            '<strong style="color: #52c41a;">💼 适用场景：</strong> ' +
            '<span style="color: #666; font-size: 13px;">客户有自己的货代，或者希望控制运输成本的情况</span>' +
            '</div>' +
            '</div>' +

            // CIF
            '<div class="delivery-card" style="background: #f0f9ff; border: 1px solid #91d5ff; border-radius: 8px; padding: 20px; margin-bottom: 15px;">' +
            '<div style="display: flex; align-items: center; margin-bottom: 12px;">' +
            '<h3 style="margin: 0; color: #1890ff; flex: 1;">CIF (Cost, Insurance, Freight) - 成本加保险费加运费</h3>' +
            '<div style="color: #faad14; font-size: 18px;">⭐⭐⭐⭐</div>' +
            '</div>' +
            '<div style="margin-bottom: 12px;">' +
            '<span style="background: #1890ff; color: white; padding: 3px 8px; border-radius: 12px; font-size: 12px; margin-right: 8px;">推荐</span>' +
            '<span style="background: #52c41a; color: white; padding: 3px 8px; border-radius: 12px; font-size: 12px;">客户喜欢</span>' +
            '</div>' +
            '<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 12px;">' +
            '<div>' +
            '<h4 style="color: #52c41a; margin: 0 0 8px 0;">✅ 优点</h4>' +
            '<ul style="margin: 0; padding-left: 18px; color: #333; font-size: 13px;">' +
            '<li>🤝 客户接受度高</li>' +
            '<li>🚢 包含运费保险</li>' +
            '<li>💼 客户只需清关</li>' +
            '<li>📈 有利于成交</li>' +
            '</ul>' +
            '</div>' +
            '<div>' +
            '<h4 style="color: #ff4d4f; margin: 0 0 8px 0;">⚠️ 注意事项</h4>' +
            '<ul style="margin: 0; padding-left: 18px; color: #333; font-size: 13px;">' +
            '<li>💰 我方承担运费</li>' +
            '<li>🛡️ 需购买海上保险</li>' +
            '<li>⚖️ 运输风险较大</li>' +
            '</ul>' +
            '</div>' +
            '</div>' +
            '<div style="background: #fff; padding: 12px; border-radius: 6px; border: 1px solid #bae7ff;">' +
            '<strong style="color: #1890ff;">💼 适用场景：</strong> ' +
            '<span style="color: #666; font-size: 13px;">客户希望一站式服务，或者我方有优势运输渠道的情况</span>' +
            '</div>' +
            '</div>' +

            // DDU
            '<div class="delivery-card" style="background: #fff7e6; border: 1px solid #ffd591; border-radius: 8px; padding: 20px; margin-bottom: 15px;">' +
            '<div style="display: flex; align-items: center; margin-bottom: 12px;">' +
            '<h3 style="margin: 0; color: #fa8c16; flex: 1;">DDU (Delivered Duty Unpaid) - 未完税交货</h3>' +
            '<div style="color: #faad14; font-size: 18px;">⭐⭐</div>' +
            '</div>' +
            '<div style="margin-bottom: 12px;">' +
            '<span style="background: #fa8c16; color: white; padding: 3px 8px; border-radius: 12px; font-size: 12px; margin-right: 8px;">谨慎选择</span>' +
            '<span style="background: #ff4d4f; color: white; padding: 3px 8px; border-radius: 12px; font-size: 12px;">风险较高</span>' +
            '</div>' +
            '<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 12px;">' +
            '<div>' +
            '<h4 style="color: #52c41a; margin: 0 0 8px 0;">✅ 优点</h4>' +
            '<ul style="margin: 0; padding-left: 18px; color: #333; font-size: 13px;">' +
            '<li>🚚 送货到客户门口</li>' +
            '<li>💼 客户只付进口税</li>' +
            '<li>🎯 服务更全面</li>' +
            '</ul>' +
            '</div>' +
            '<div>' +
            '<h4 style="color: #ff4d4f; margin: 0 0 8px 0;">⚠️ 风险</h4>' +
            '<ul style="margin: 0; padding-left: 18px; color: #333; font-size: 13px;">' +
            '<li>💰 我方承担所有运费</li>' +
            '<li>🚫 清关风险大</li>' +
            '<li>⏰ 时效难控制</li>' +
            '<li>📋 手续复杂</li>' +
            '</ul>' +
            '</div>' +
            '</div>' +
            '<div style="background: #fff; padding: 12px; border-radius: 6px; border: 1px solid #ffe7ba;">' +
            '<strong style="color: #fa8c16;">⚠️ 建议：</strong> ' +
            '<span style="color: #666; font-size: 13px;">仅在客户强烈要求且我方有丰富经验时选择</span>' +
            '</div>' +
            '</div>' +

            // DDP
            '<div class="delivery-card" style="background: #fff1f0; border: 1px solid #ffccc7; border-radius: 8px; padding: 20px; margin-bottom: 20px;">' +
            '<div style="display: flex; align-items: center; margin-bottom: 12px;">' +
            '<h3 style="margin: 0; color: #ff4d4f; flex: 1;">DDP (Delivered Duty Paid) - 完税后交货</h3>' +
            '<div style="color: #faad14; font-size: 18px;">⭐</div>' +
            '</div>' +
            '<div style="margin-bottom: 12px;">' +
            '<span style="background: #ff4d4f; color: white; padding: 3px 8px; border-radius: 12px; font-size: 12px; margin-right: 8px;">不推荐</span>' +
            '<span style="background: #722ed1; color: white; padding: 3px 8px; border-radius: 12px; font-size: 12px;">风险极高</span>' +
            '</div>' +
            '<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 12px;">' +
            '<div>' +
            '<h4 style="color: #52c41a; margin: 0 0 8px 0;">✅ 优点</h4>' +
            '<ul style="margin: 0; padding-left: 18px; color: #333; font-size: 13px;">' +
            '<li>🎁 客户零风险</li>' +
            '<li>🚚 门到门服务</li>' +
            '<li>💯 服务最全面</li>' +
            '</ul>' +
            '</div>' +
            '<div>' +
            '<h4 style="color: #ff4d4f; margin: 0 0 8px 0;">❌ 重大风险</h4>' +
            '<ul style="margin: 0; padding-left: 18px; color: #333; font-size: 13px;">' +
            '<li>💸 我方承担所有费用</li>' +
            '<li>🏛️ 承担进口税风险</li>' +
            '<li>📋 复杂的税务问题</li>' +
            '<li>⚖️ 法律责任重大</li>' +
            '</ul>' +
            '</div>' +
            '</div>' +
            '<div style="background: #fff; padding: 12px; border-radius: 6px; border: 1px solid #ffccc7;">' +
            '<strong style="color: #ff4d4f;">🚨 警告：</strong> ' +
            '<span style="color: #666; font-size: 13px;">除非有特殊要求且利润丰厚，否则强烈不建议选择</span>' +
            '</div>' +
            '</div>' +

            // 总结建议
            '<div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px; text-align: center;">' +
            '<h3 style="margin: 0 0 15px 0; color: white;">🎯 选择建议</h3>' +
            '<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; text-align: left;">' +
            '<div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 6px;">' +
            '<h4 style="color: #52c41a; margin: 0 0 10px 0;">🥇 首选：FOB</h4>' +
            '<p style="margin: 0; font-size: 13px;">风险最小，责任清晰，适合大部分情况</p>' +
            '</div>' +
            '<div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 6px;">' +
            '<h4 style="color: #1890ff; margin: 0 0 10px 0;">🥈 备选：CIF</h4>' +
            '<p style="margin: 0; font-size: 13px;">客户接受度高，有利于成交，适合有运输优势时</p>' +
            '</div>' +
            '</div>' +
            '</div>' +

            '</div>';

        layer.open({
            type: 1,
            title: false,
            closeBtn: 1,
            area: ['800px', '90%'],
            content: guideHtml,
            skin: 'layui-layer-rim',
            shadeClose: true,
            maxmin: true
        });
    }
</script>
#end