#set(seoTitle="营销客户管理 " + (isAdd?"创建":"编辑"))
#@layout()
#define main()
#include("/_view/my/common/_my_menu_bar.html")

#define content()
<div class="mobile-container">
    <!-- 顶部操作栏 -->
    <div class="mobile-header">
        <div class="mobile-actions">
            <a href="/my/xjd/add?gsmc=#(yxkh.gsmc??)&khid=#(yxkh.id??)" class="layui-btn layui-btn-normal">
                <i class="layui-icon layui-icon-dollar"></i>新增询价
            </a>
            #if(!isAdd)
            <button type="button" class="layui-btn layui-btn-warm" onclick="showSurveyMenuMobile()">
                <i class="layui-icon layui-icon-form"></i>市场调研
            </button>
            #end
        </div>
        
        #if(!isAdd)
        <div class="mobile-survey-overview" id="mobileSurveyOverview">
            <div style="text-align: center; padding: 10px;">
                <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i>
                <span style="margin-left: 8px;">加载调研数据...</span>
            </div>
        </div>
        #end
    </div>

    <form class="layui-form" id="mobileForm" onsubmit="return false;">
        #if(!isAdd)
        <input type="hidden" name="yxkh.id" value="#(yxkh.id)">
        #end
        
        <!-- 基本信息 -->
        <div class="form-card">
            <h3>基本信息</h3>
            
            <div class="mobile-form-item">
                <label class="mobile-form-label required">公司名称</label>
                <input type="text" name="yxkh.gsmc" value="#(yxkh.gsmc??)" class="mobile-form-input" placeholder="请输入公司名称" required>
            </div>
            
            <div class="mobile-form-item">
                <label class="mobile-form-label required">决策者</label>
                <input type="text" name="yxkh.jcz" value="#(yxkh.jcz??)" class="mobile-form-input" placeholder="请输入决策者姓名" required>
            </div>

            <div class="mobile-form-item">
                <label class="mobile-form-label">网址</label>
                <input type="text" name="yxkh.wz" value="#(yxkh.wz??)" class="mobile-form-input" placeholder="请输入客户网址" required>
            </div>

            <div class="mobile-form-item">
                <label class="mobile-form-label">FACEBOOK</label>
                <input type="text" name="yxkh.facebook" value="#(yxkh.facebook??)" class="mobile-form-input" placeholder="请输入FACEBOOK链接" required>
            </div>

            <div class="mobile-form-item">
                <label class="mobile-form-label">决策者手机</label>
                <input type="tel" name="yxkh.jcsj" value="#(yxkh.jcsj??)" class="mobile-form-input" placeholder="请输入手机号码">
                <div class="contact-actions">
                    <button type="button" class="contact-btn call-btn" onclick="openCallModal()">
                        <i class="layui-icon layui-icon-cellphone"></i>拨打电话
                    </button>
                    <button type="button" class="contact-btn record-btn" onclick="openVoiceRecordsModal()">
                        <i class="layui-icon layui-icon-voice"></i>录音管理
                    </button>
                </div>
            </div>
            
            <div class="mobile-form-item">
                <label class="mobile-form-label">决策者邮箱</label>
                <input type="email" name="yxkh.jczyx" value="#(yxkh.jczyx??)" class="mobile-form-input" placeholder="请输入邮箱地址">
            </div>
        </div>
        
        <!-- 地址信息 -->
        <div class="form-card">
            <h3>地址信息</h3>
            
            <div class="mobile-form-item">
                <label class="mobile-form-label">国别</label>
                <input type="text" name="yxkh.gb" value="#(yxkh.gb??)" class="mobile-form-input" placeholder="如：美国">
            </div>

            <div class="mobile-form-item">
                <label class="mobile-form-label">州</label>
                <input type="text" name="yxkh.zhou" value="#(yxkh.zhou??)" class="mobile-form-input" placeholder="如：纽约州">
            </div>

            <div class="mobile-form-item">
                <label class="mobile-form-label">市</label>
                <input type="text" name="yxkh.shi" value="#(yxkh.shi??)" class="mobile-form-input" placeholder="如：纽约">
            </div>

            <div class="mobile-form-item">
                <label class="mobile-form-label">邮编</label>
                <input type="text" name="yxkh.yb" value="#(yxkh.yb??)" class="mobile-form-input" placeholder="如：10001">
            </div>
            
            <div class="mobile-form-item">
                <label class="mobile-form-label">地址</label>
                <textarea name="yxkh.dz" class="mobile-form-textarea" placeholder="请输入详细地址">#(yxkh.dz??)</textarea>
            </div>
        </div>
        
        <!-- 业务信息 -->
        <div class="form-card">
            <h3>业务信息</h3>
            
            <div class="mobile-form-item">
                <label class="mobile-form-label">
                    主营产品
                    <span class="product-tip" title="多个产品请用逗号分隔，销量大的产品写在前面">
                        <i class="layui-icon layui-icon-tips" style="color: #FF5722; font-size: 14px;"></i>
                    </span>
                </label>
                <input type="text" name="yxkh.zycp" value="#(yxkh.zycp??)" class="mobile-form-input" placeholder="如：花岗岩板材,大理石瓷砖,石英石台面">
                <div class="form-tip important">💡 多个产品用逗号分隔，<strong>销量大的写前面</strong></div>
            </div>
            
            <div class="mobile-form-item">
                <label class="mobile-form-label">
                    交货方式
                    <span class="delivery-tip" title="推荐：FOB（客户承担运费风险）或CIF（我方承担运费，客户承担进口税）">
                        <i class="layui-icon layui-icon-tips" style="color: #1890FF; font-size: 14px;"></i>
                    </span>
                </label>
                <select name="yxkh.cgjgfs" class="mobile-form-select">
                    <option value="">请选择交货方式</option>
                    <option value="FOB货到本地我方港口" #if(yxkh.cgjgfs?? == "FOB货到本地我方港口")selected#end>FOB货到本地我方港口</option>
                    <option value="CIF货到境外客户港口含海上保险" #if(yxkh.cgjgfs?? == "CIF货到境外客户港口含海上保险")selected#end>CIF货到境外客户港口含海上保险</option>
                    <option value="DDU送货上门不含境外客户进口税" #if(yxkh.cgjgfs?? == "DDU送货上门不含境外客户进口税")selected#end>DDU送货上门不含境外客户进口税</option>
                    <option value="DDP送货上门含境外客户进口税" #if(yxkh.cgjgfs?? == "DDP送货上门含境外客户进口税")selected#end>DDP送货上门含境外客户进口税</option>
                    <option value="其他" #if(yxkh.cgjgfs?? == "其他")selected#end>其他</option>
                </select>
                <div class="form-tip" style="color: #1890FF;">
                    <span onclick="showDeliveryGuideMobile()" style="cursor: pointer; text-decoration: underline;">
                        🚢 <strong>推荐FOB或CIF</strong>：降低我方风险，客户更易接受（点击查看详细对比）
                    </span>
                </div>
            </div>
            
            <div class="mobile-form-item">
                <label class="mobile-form-label">客户性质</label>
                <select name="yxkh.khxz" class="mobile-form-select">
                    <option value="">请选择客户性质</option>
                    <option value="批发商" #if(yxkh.khxz?? == "批发商")selected#end>批发商</option>
                    <option value="零售商" #if(yxkh.khxz?? == "零售商")selected#end>零售商</option>
                </select>
            </div>
        </div>
        
        <!-- 沟通记录 -->
        <div class="form-card">
            <h3>沟通记录</h3>
            
            <div class="mobile-form-item">
                <label class="mobile-form-label">沟通纪要</label>
                <textarea name="yxkh.jybz" class="mobile-form-textarea" placeholder="记录与客户的沟通内容、需求、进展等">#(yxkh.jybz??)</textarea>
            </div>
        </div>
    </form>
    
    <!-- 底部间距 -->
    <div class="mobile-bottom-space"></div>
    
    <!-- 保存按钮 -->
    <button type="button" class="mobile-save-btn" onclick="saveMobileForm()">
        <i class="layui-icon layui-icon-ok"></i> 保存客户信息
    </button>
</div>
#end
#end

#define css()
<style>
    /* 移动端专用样式 */
    body {
        background: #f5f5f5;
        font-size: 14px;
    }

    .mobile-container {
        max-width: 100%;
        padding: 10px;
    }

    /* 顶部操作栏 */
    .mobile-header {
        background: white;
        border-radius: 12px;
        padding: 15px;
        margin-bottom: 15px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .mobile-actions {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 10px;
        margin-bottom: 15px;
    }

    .mobile-actions .layui-btn {
        height: 44px;
        border-radius: 8px;
        font-size: 14px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 5px;
    }

    /* 表单卡片 */
    .form-card {
        background: white;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 15px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .form-card h3 {
        margin: 0 0 15px 0;
        color: #333;
        font-size: 16px;
        font-weight: 600;
        border-bottom: 2px solid #1890ff;
        padding-bottom: 8px;
    }

    /* 表单项 */
    .mobile-form-item {
        margin-bottom: 15px;
    }

    .mobile-form-item:last-child {
        margin-bottom: 0;
    }

    .mobile-form-label {
        display: block;
        margin-bottom: 8px;
        color: #333;
        font-weight: 500;
        font-size: 14px;
    }

    .mobile-form-label.required::after {
        content: " *";
        color: #ff4d4f;
    }

    .mobile-form-input {
        width: 100%;
        height: 44px;
        border: 1px solid #d9d9d9;
        border-radius: 8px;
        padding: 0 12px;
        font-size: 14px;
        background: #fff;
        box-sizing: border-box;
    }

    .mobile-form-input:focus {
        border-color: #1890ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
        outline: none;
    }

    .mobile-form-textarea {
        width: 100%;
        min-height: 80px;
        border: 1px solid #d9d9d9;
        border-radius: 8px;
        padding: 12px;
        font-size: 14px;
        background: #fff;
        box-sizing: border-box;
        resize: vertical;
    }

    .mobile-form-textarea:focus {
        border-color: #1890ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
        outline: none;
    }

    .mobile-form-select {
        width: 100%;
        height: 44px;
        border: 1px solid #d9d9d9;
        border-radius: 8px;
        padding: 0 12px;
        font-size: 14px;
        background: #fff;
        box-sizing: border-box;
    }

    /* 电话和录音区域 */
    .contact-actions {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 10px;
        margin-top: 10px;
    }

    .contact-btn {
        height: 40px;
        border-radius: 8px;
        border: none;
        font-size: 13px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 5px;
    }

    .call-btn {
        background: #52c41a;
        color: white;
    }

    .record-btn {
        background: #1890ff;
        color: white;
    }

    /* 保存按钮 */
    .mobile-save-btn {
        position: fixed;
        bottom: 20px;
        left: 20px;
        right: 20px;
        height: 50px;
        background: #1890ff;
        color: white;
        border: none;
        border-radius: 25px;
        font-size: 16px;
        font-weight: 600;
        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        z-index: 1000;
    }

    /* 底部间距 */
    .mobile-bottom-space {
        height: 80px;
    }

    /* 提示样式 */
    .form-tip {
        font-size: 12px;
        color: #666;
        margin-top: 5px;
        line-height: 1.4;
    }

    .form-tip.important {
        color: #ff4d4f;
        font-weight: 500;
    }

    /* 调研概览移动端样式 */
    .mobile-survey-overview {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 12px;
        padding: 15px;
        margin-bottom: 15px;
    }

    .survey-stats {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 10px;
        margin-bottom: 15px;
    }

    .survey-stat {
        text-align: center;
    }

    .survey-stat-number {
        font-size: 20px;
        font-weight: bold;
        margin-bottom: 4px;
    }

    .survey-stat-label {
        font-size: 11px;
        opacity: 0.9;
    }

    .survey-action-btn {
        width: 100%;
        height: 40px;
        background: rgba(255,255,255,0.2);
        border: 1px solid rgba(255,255,255,0.3);
        color: white;
        border-radius: 8px;
        font-size: 14px;
    }

    /* 移动端弹窗样式 */
    .mobile-modal {
        background: #fff;
        border-radius: 12px;
        overflow: hidden;
    }

    .mobile-modal h3 {
        background: linear-gradient(135deg, #1890ff, #36cfc9);
        color: white;
        margin: 0;
        padding: 15px;
        text-align: center;
        font-size: 16px;
    }

    .mobile-modal h4 {
        color: #333;
        font-size: 14px;
        margin: 0 0 10px 0;
        font-weight: 600;
    }

    /* 录音文件卡片样式 */
    .voice-record-card {
        background: #fff;
        border: 1px solid #e6e6e6;
        border-radius: 8px;
        margin-bottom: 10px;
        padding: 12px;
        transition: box-shadow 0.2s;
    }

    .voice-record-card:hover {
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .voice-record-card .file-name {
        font-weight: bold;
        color: #333;
        margin-bottom: 4px;
        display: flex;
        align-items: center;
        gap: 5px;
    }

    .voice-record-card .file-info {
        font-size: 12px;
        color: #666;
        margin-bottom: 2px;
    }

    .voice-record-card .file-actions {
        display: flex;
        gap: 8px;
        margin-top: 8px;
    }

    .voice-record-card .file-actions button {
        flex: 1;
        height: 32px;
        border-radius: 4px;
        font-size: 12px;
    }

    /* 电话号码选择区域 */
    .phone-numbers-area {
        max-height: 150px;
        overflow-y: auto;
        border: 1px solid #e6e6e6;
        border-radius: 4px;
        padding: 10px;
        background: #f8f8f8;
    }

    .phone-number-btn {
        width: 100%;
        margin-bottom: 8px;
        text-align: center;
        border-radius: 4px;
    }

    /* 二维码区域 */
    .qr-code-area {
        text-align: center;
        padding: 10px;
        background: #f8f8f8;
        border-radius: 4px;
        border: 1px solid #e6e6e6;
    }

    .qr-code-area img {
        border: 1px solid #ddd;
        border-radius: 4px;
        max-width: 100%;
        height: auto;
    }
</style>
<!-- 拨打电话弹出界面（移动版） -->
<div id="callModal" style="width: 100%; height: 100%; display: none; padding: 15px;">
    <div class="layui-row">
        <div class="layui-col-md12">
            <h3 style="margin-bottom: 15px; text-align: center;">
                <i class="layui-icon layui-icon-cellphone" style="color: #1E9FFF;"></i>
                拨打电话 - #(yxkh.gsmc??"客户")
            </h3>

            <!-- 电话号码选择区域 -->
            <div style="margin-bottom: 20px;">
                <h4 style="margin-bottom: 10px; color: #333;">
                    <i class="layui-icon layui-icon-list"></i> 可拨打号码
                </h4>
                <div id="phoneNumbers" style="max-height: 150px; overflow-y: auto; border: 1px solid #e6e6e6; border-radius: 4px; padding: 10px; background: #f8f8f8;">
                    <div style="text-align: center; color: #999; padding: 20px;">
                        <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop" style="font-size: 20px;"></i>
                        <p style="margin-top: 8px;">正在解析电话号码...</p>
                    </div>
                </div>
            </div>

            <!-- 拨打电话输入区域 -->
            <div class="layui-form">
                <div class="layui-form-item">
                    <label class="layui-form-label" style="width: 80px; font-size: 14px;">主叫号码</label>
                    <div class="layui-input-block" style="margin-left: 90px;">
                        <input type="text" id="caller" placeholder="请输入您的手机号码" class="layui-input call-modal-input" style="font-size: 14px;">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label" style="width: 80px; font-size: 14px;">被叫号码</label>
                    <div class="layui-input-block" style="margin-left: 90px;">
                        <input type="text" id="callees" placeholder="请输入被叫号码" class="layui-input call-modal-input" style="font-size: 14px;">
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-input-block" style="margin-left: 90px;">
                        <button type="button" class="layui-btn layui-btn-normal" onclick="call(event); return false;" style="width: 100%; height: 40px; font-size: 14px; border-radius: 4px;">
                            <i class="layui-icon layui-icon-cellphone" style="margin-right: 6px;"></i>
                            拨打电话
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 录音上传二维码区域 -->
    <div style="margin-top: 20px; border-top: 1px solid #e6e6e6; padding-top: 15px;">
        <h4 style="margin-bottom: 10px; color: #333; text-align: center;">
            <i class="layui-icon layui-icon-upload"></i> 录音上传二维码
        </h4>
        <div id="qrcode" style="text-align: center; padding: 10px; background: #f8f8f8; border-radius: 4px;">
            <!-- 二维码将显示在这里 -->
        </div>
    </div>
</div>

<!-- 录音文件列表弹出界面（移动版） -->
<div id="voiceRecordsModal" style="width: 100%; height: 100%; display: none; padding: 15px;">
    <div class="layui-row">
        <div class="layui-col-md12">
            <h3 style="margin-bottom: 15px; text-align: center;">
                <i class="layui-icon layui-icon-voice" style="color: #1E9FFF;"></i>
                客户录音文件 - #(yxkh.gsmc??"客户")
            </h3>

            <!-- 录音文件列表 -->
            <div id="voiceRecordsList" style="max-height: 400px; overflow-y: auto; border: 1px solid #e6e6e6; border-radius: 4px; background: #fff;">
                <div style="text-align: center; color: #999; padding: 30px;">
                    <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop" style="font-size: 24px;"></i>
                    <p style="margin-top: 10px;">正在加载录音文件...</p>
                </div>
            </div>
        </div>
    </div>
</div>

#end

#define js()
<script>
    // 打开拨打电话弹出界面（移动版）
    function openCallModal() {
        console.log('openCallModal 函数被调用（移动版）');
        try {
            var phoneField = document.querySelector('input[name="yxkh.jcsj"]').value;
            console.log('获取到的电话号码：', phoneField);

            if (!phoneField || phoneField.trim() === '') {
                layer.msg('请先填写决策手机号码', {icon: 2});
                return;
            }

            // 解析电话号码
            parsePhoneNumbers(phoneField);

            // 生成二维码
            generateQRCode();

            // 显示弹出界面（移动端适配）
            layer.open({
                type: 1,
                title: "拨打电话",
                content: $('#callModal'),
                area: ['95%', '80%'],
                success: function () {
                    // 弹窗打开后自动加载保存的主叫号码
                    loadSavedCallerNumber();
                },
                end: function () {
                    document.getElementById("callModal").style.display = "none";
                }
            });
        } catch (e) {
            console.error('openCallModal 函数执行出错：', e);
            layer.msg('打开拨打电话界面时出错：' + e.message, {icon: 2});
        }
    }

    // 解析电话号码（移动版）
    function parsePhoneNumbers(phoneString) {
        console.log('解析电话号码：', phoneString);
        try {
            // 清空之前的内容
            $('#phoneNumbers').empty();

            if (!phoneString) {
                $('#phoneNumbers').html('<p style="text-align: center; color: #999;">没有找到电话号码</p>');
                return;
            }

            // 使用分号分割电话号码
            var phones = phoneString.split(';');
            var phoneHtml = '';

            phones.forEach(function (phone) {
                phone = phone.trim();
                if (phone !== '') {
                    phoneHtml += '<div style="margin-bottom: 8px;">';
                    phoneHtml += '<a href="javascript:void(0);" onclick="selectPhone(\'' + phone + '\', event); return false;" class="layui-btn layui-btn-sm layui-btn-normal" style="width: 100%; text-align: center;">';
                    phoneHtml += '<i class="layui-icon layui-icon-cellphone"></i> ' + phone + '</a>';
                    phoneHtml += '</div>';
                }
            });

            if (phoneHtml === '') {
                phoneHtml = '<p style="text-align: center; color: #999;">没有找到有效的电话号码</p>';
            }

            $('#phoneNumbers').html(phoneHtml);
        } catch (e) {
            console.error('解析电话号码出错：', e);
            $('#phoneNumbers').html('<p style="text-align: center; color: #ff5722;">解析电话号码失败</p>');
        }
    }

    // 选择电话号码（移动版）
    function selectPhone(phone, event) {
        // 阻止事件冒泡
        if (event) {
            event.preventDefault();
            event.stopPropagation();
        }

        console.log('选择电话号码：', phone);
        document.getElementById('callees').value = phone;
        layer.msg('已选择号码：' + phone, {icon: 1, time: 1000});
        return false;
    }

    // 生成二维码（移动版）
    function generateQRCode() {
        var customerId = '#(yxkh.id??)';
        var qrUrl = 'http://360.theolympiastone.com/my/yxkh/uploadVoiceRecord?id=' + customerId;

        // 清空之前的二维码
        $('#qrcode').empty();

        // 使用在线二维码生成服务
        var qrCodeImg = '<img src="https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=' +
            encodeURIComponent(qrUrl) + '" alt="二维码" style="border: 1px solid #ddd; border-radius: 4px;">';
        $('#qrcode').html(qrCodeImg);

        // 添加URL文本显示（移动端简化）
        $('#qrcode').append('<div style="margin-top: 8px; font-size: 11px; color: #666; word-break: break-all; text-align: center;">扫码上传录音</div>');
    }

    // 拨打电话（移动版）
    function call(event) {
        // 强制阻止任何可能的表单提交和事件冒泡
        if (event) {
            event.preventDefault();
            event.stopPropagation();
            event.stopImmediatePropagation();
        }

        console.log('call函数被调用，阻止表单提交');

        var caller = $("#caller").val();
        var callees = $("#callees").val();

        if (!caller || caller.trim() === '') {
            layer.msg('请输入主叫号码', {icon: 2});
            return false;
        }

        if (!callees || callees.trim() === '') {
            layer.msg('请输入被叫号码', {icon: 2});
            return false;
        }

        caller = caller.replace(/\s/gi, '');
        callees = callees.replace(/\s/gi, '');

        // 保存主叫号码
        saveCaller(caller);

        var param = "caller=" + caller + "&callees=" + callees;

        // 显示加载提示
        var loadingIndex = layer.load(2, {shade: [0.3, '#000']});

        // 使用后端代理接口，避免跨域问题
        $.ajax({
            type: "POST",
            url: "/my/yxkh/callProxy",
            data: {
                'caller': caller,
                'callees': callees
            },
            cache: false,
            dataType: 'json',
            timeout: 20000, // 20秒超时
            success: function (result) {
                layer.close(loadingIndex);
                console.log('电话拨打响应：', result);
                if (result.state === 'ok') {
                    layer.msg(result.msg || "拨打成功，请接听系统来电", {icon: 1, time: 3000});
                } else {
                    layer.msg(result.msg || "拨打失败，请稍后重试", {icon: 2, time: 3000});
                }
            },
            error: function (xhr, status, error) {
                layer.close(loadingIndex);
                console.log('电话拨打请求出错：', status, error);
                if (status === 'timeout') {
                    layer.msg("请求超时，请稍后重试", {icon: 2});
                } else if (xhr.status >= 500) {
                    layer.msg("服务器内部错误，请稍后重试", {icon: 2});
                } else if (xhr.status === 404) {
                    layer.msg("电话服务接口未找到，请联系管理员", {icon: 2});
                } else {
                    layer.msg("网络异常(状态码:" + xhr.status + ")，请稍后重试", {icon: 2});
                }
            }
        });

        return false; // 确保不触发表单提交
    }

    // 保存主叫号码到本地存储（移动版）
    function saveCaller(caller) {
        try {
            localStorage.setItem('saved_caller_number', caller);
            console.log('主叫号码已保存：', caller);
        } catch (e) {
            console.log('保存主叫号码失败：', e);
        }
    }

    // 加载保存的主叫号码（移动版）
    function loadSavedCallerNumber() {
        try {
            var savedCaller = localStorage.getItem('saved_caller_number');
            if (savedCaller) {
                document.getElementById('caller').value = savedCaller;
                console.log('已加载保存的主叫号码：', savedCaller);
            }
        } catch (e) {
            console.log('加载保存的主叫号码失败：', e);
        }
    }

    // 打开录音文件列表弹出界面（移动版）
    function openVoiceRecordsModal() {
        console.log('打开录音文件列表（移动版）');
        try {
            // 显示弹出界面（移动端适配）
            layer.open({
                type: 1,
                title: "录音文件管理",
                content: $('#voiceRecordsModal'),
                area: ['95%', '80%'],
                success: function () {
                    // 弹窗打开后加载录音文件列表
                    loadVoiceRecords();
                },
                end: function () {
                    document.getElementById("voiceRecordsModal").style.display = "none";
                }
            });
        } catch (e) {
            console.error('打开录音文件列表出错：', e);
            layer.msg('打开录音文件列表时出错：' + e.message, {icon: 2});
        }
    }

    // 加载录音文件列表（移动版）
    function loadVoiceRecords() {
        var customerId = '#(yxkh.id??)';
        if (!customerId) {
            $('#voiceRecordsList').html('<div style="text-align: center; color: #999; padding: 30px;"><p>客户ID不存在</p></div>');
            return;
        }

        // 显示加载状态
        $('#voiceRecordsList').html('<div style="text-align: center; color: #999; padding: 30px;">' +
            '<i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop" style="font-size: 24px;"></i>' +
            '<p style="margin-top: 10px;">正在加载录音文件...</p>' +
            '</div>');

        $.ajax({
            url: '/my/yxkh/getVoiceRecords',
            type: 'POST',
            data: {
                'customerId': customerId
            },
            cache: false,
            dataType: 'json',
            success: function (data) {
                console.log('录音文件数据：', data);
                displayVoiceRecords(data);
            },
            error: function () {
                $('#voiceRecordsList').html('<div style="text-align: center; color: #ff5722; padding: 30px;"><p>加载录音文件失败，请稍后重试</p></div>');
            }
        });
    }

    // 显示录音文件列表（移动版）
    function displayVoiceRecords(voiceRecords) {
        var listHtml = '';

        if (!voiceRecords || voiceRecords.length === 0) {
            listHtml = '<div style="text-align: center; color: #999; padding: 30px;">' +
                '<i class="layui-icon layui-icon-file" style="font-size: 48px; color: #d2d2d2;"></i>' +
                '<p style="margin-top: 15px; font-size: 14px;">暂无录音文件</p>' +
                '<p style="margin-top: 10px; font-size: 12px; color: #999;">点击下方按钮上传录音</p>' +
                '</div>';
        } else {
            listHtml = '<div style="padding: 10px;">';

            for (var i = 0; i < voiceRecords.length; i++) {
                var record = voiceRecords[i];
                var fileName = record.fileName || '未知文件';
                var uploadTime = record.uploadTime || '';
                var remark = record.remark || '';
                var uploader = record.uploader || '';
                var recordId = record.id;

                // 移动端卡片式布局
                listHtml += '<div class="voice-record-card" style="background: #fff; border: 1px solid #e6e6e6; border-radius: 8px; margin-bottom: 10px; padding: 12px;">';

                // 文件信息
                listHtml += '<div style="margin-bottom: 8px;">';
                listHtml += '<div style="font-weight: bold; color: #333; margin-bottom: 4px;">';
                listHtml += '<i class="layui-icon layui-icon-file" style="color: #1E9FFF; margin-right: 5px;"></i>';
                listHtml += fileName;
                listHtml += '</div>';

                if (uploadTime) {
                    listHtml += '<div style="font-size: 12px; color: #666; margin-bottom: 2px;">上传时间：' + uploadTime + '</div>';
                }

                if (uploader) {
                    listHtml += '<div style="font-size: 12px; color: #666; margin-bottom: 2px;">上传者：' + uploader + '</div>';
                }

                if (remark) {
                    listHtml += '<div style="font-size: 12px; color: #666; margin-bottom: 8px;">备注：' + remark + '</div>';
                }
                listHtml += '</div>';

                // 操作按钮（移动端适配）
                listHtml += '<div style="display: flex; gap: 8px;">';
                listHtml += '<button class="layui-btn layui-btn-xs layui-btn-normal" onclick="playVoiceRecord(\'' + fileName + '\')" style="flex: 1;">';
                listHtml += '<i class="layui-icon layui-icon-play"></i> 播放';
                listHtml += '</button>';
                listHtml += '<button class="layui-btn layui-btn-xs layui-btn-danger" onclick="deleteVoiceRecord(' + recordId + ', \'' + fileName + '\')" style="flex: 1;">';
                listHtml += '<i class="layui-icon layui-icon-delete"></i> 删除';
                listHtml += '</button>';
                listHtml += '</div>';

                listHtml += '</div>';
            }

            listHtml += '</div>';
        }

        // 添加操作按钮区域
        listHtml += '<div style="padding: 15px; border-top: 1px solid #e6e6e6; background: #f8f8f8;">';
        listHtml += '<div style="display: flex; gap: 10px;">';
        listHtml += '<button class="layui-btn layui-btn-normal" onclick="uploadNewVoice()" style="flex: 1;">';
        listHtml += '<i class="layui-icon layui-icon-upload"></i> 上传录音';
        listHtml += '</button>';
        listHtml += '<button class="layui-btn layui-btn-primary" onclick="refreshVoiceList()" style="flex: 1;">';
        listHtml += '<i class="layui-icon layui-icon-refresh"></i> 刷新';
        listHtml += '</button>';
        listHtml += '</div>';
        listHtml += '</div>';

        $('#voiceRecordsList').html(listHtml);
    }

    // 播放录音文件（移动版）
    function playVoiceRecord(fileName) {
        var customerId = '#(yxkh.id??)';
        var audioUrl = '/upload/voice/' + customerId + '/' + fileName;

        // 移动端使用新窗口打开音频文件
        window.open(audioUrl, '_blank');
    }

    // 删除录音文件（移动版）
    function deleteVoiceRecord(recordId, fileName) {
        layer.confirm('确定要删除录音文件 "' + fileName + '" 吗？', {
            icon: 3,
            title: '删除确认',
            btn: ['确定删除', '取消']
        }, function (index) {
            // 显示加载提示
            var loadingIndex = layer.load(2, {shade: [0.3, '#000']});

            $.ajax({
                url: '/my/yxkh/deleteVoiceRecord',
                type: 'POST',
                data: {
                    'id': recordId
                },
                cache: false,
                dataType: 'json',
                success: function (result) {
                    layer.close(loadingIndex);
                    layer.close(index);

                    if (result.state === 'ok') {
                        layer.msg('删除成功', {icon: 1});
                        // 刷新列表
                        loadVoiceRecords();
                    } else {
                        layer.msg(result.msg || '删除失败', {icon: 2});
                    }
                },
                error: function () {
                    layer.close(loadingIndex);
                    layer.close(index);
                    layer.msg('删除失败，请稍后重试', {icon: 2});
                }
            });
        });
    }

    // 上传新录音（移动版）
    function uploadNewVoice() {
        var customerId = '#(yxkh.id??)';
        var uploadUrl = '/my/yxkh/uploadVoiceRecord?id=' + customerId;

        layer.confirm('是否前往录音上传页面？', {
            icon: 3,
            title: '跳转确认',
            btn: ['新窗口打开', '当前窗口打开', '取消']
        }, function (index) {
            // 新窗口打开
            window.open(uploadUrl, '_blank');
            layer.close(index);
        }, function (index) {
            // 当前窗口打开
            window.location.href = uploadUrl;
            layer.close(index);
        });
    }

    // 刷新录音列表（移动版）
    function refreshVoiceList() {
        $('#voiceRecordsList').html('<div style="text-align: center; color: #999; padding: 30px;">' +
            '<i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop" style="font-size: 24px;"></i>' +
            '<p style="margin-top: 10px;">正在刷新录音文件...</p>' +
            '</div>');
        loadVoiceRecords();
    }
    
    // 显示交货方式指南（移动端版本）
    function showDeliveryGuideMobile() {
        // 简化版的交货方式指南
        var guideHtml = '<div style="padding: 20px;">' +
            '<h3 style="text-align: center; margin-bottom: 20px; color: #1890ff;">交货方式选择指南</h3>' +

            '<div style="background: #f6ffed; border: 1px solid #b7eb8f; border-radius: 8px; padding: 15px; margin-bottom: 15px;">' +
            '<h4 style="color: #52c41a; margin: 0 0 10px 0;">🥇 FOB - 最推荐</h4>' +
            '<p style="margin: 0; font-size: 13px; color: #333;">客户承担运费，我方风险最小，责任界限清晰</p>' +
            '</div>' +

            '<div style="background: #f0f9ff; border: 1px solid #91d5ff; border-radius: 8px; padding: 15px; margin-bottom: 15px;">' +
            '<h4 style="color: #1890ff; margin: 0 0 10px 0;">🥈 CIF - 推荐</h4>' +
            '<p style="margin: 0; font-size: 13px; color: #333;">包含运费保险，客户接受度高，有利成交</p>' +
            '</div>' +

            '<div style="background: #fff7e6; border: 1px solid #ffd591; border-radius: 8px; padding: 15px; margin-bottom: 15px;">' +
            '<h4 style="color: #fa8c16; margin: 0 0 10px 0;">⚠️ DDU - 谨慎选择</h4>' +
            '<p style="margin: 0; font-size: 13px; color: #333;">我方承担运费，清关风险大</p>' +
            '</div>' +

            '<div style="background: #fff1f0; border: 1px solid #ffccc7; border-radius: 8px; padding: 15px;">' +
            '<h4 style="color: #ff4d4f; margin: 0 0 10px 0;">❌ DDP - 不推荐</h4>' +
            '<p style="margin: 0; font-size: 13px; color: #333;">我方承担所有费用和税务，风险极高</p>' +
            '</div>' +

            '</div>';

        layer.open({
            type: 1,
            title: false,
            closeBtn: 1,
            area: ['90%', 'auto'],
            content: guideHtml,
            skin: 'layui-layer-rim'
        });
    }
    
    // 显示调研菜单（移动端）
    function showSurveyMenuMobile() {
        var customerId = '#(yxkh.id??)';
        var customerName = '#(yxkh.gsmc??)';

        if (!customerId) {
            layer.msg('请先保存客户信息', {icon: 2});
            return;
        }
        
        var menuHtml = '<div style="padding: 20px; text-align: center;">' +
            '<h3 style="margin-bottom: 15px; color: #333;">市场调研</h3>' +
            '<p style="margin-bottom: 15px; color: #666; font-size: 14px;">客户：' + customerName + '</p>' +
            '<div>' +
            '<button onclick="openSurveyMobile(' + customerId + ')" class="layui-btn layui-btn-normal layui-btn-fluid" style="margin: 8px 0;">填写调研</button>' +
            '<button onclick="viewMySurveysMobile(' + customerId + ')" class="layui-btn layui-btn-primary layui-btn-fluid" style="margin: 8px 0;">查看我的调研</button>';

        #role("权限管理员", "超级管理员", "总经理")
            menuHtml += '<button onclick="viewAllSurveysMobile(' + customerId + ')" class="layui-btn layui-btn-warm layui-btn-fluid" style="margin: 8px 0;">查看所有调研</button>';
        #end
        
        menuHtml += '</div></div>';
        
        layer.open({
            type: 1,
            title: false,
            closeBtn: 1,
            area: ['90%', 'auto'],
            content: menuHtml,
            skin: 'layui-layer-rim'
        });
    }
    
    function openSurveyMobile(customerId) {
        layer.closeAll();
        window.open('/my/marketresearch/survey?customerId=' + customerId);
    }
    
    function viewMySurveysMobile(customerId) {
        layer.closeAll();
        window.open('/my/marketresearch/viewAnswers?customerId=' + customerId);
    }
    
    function viewAllSurveysMobile(customerId) {
        layer.closeAll();
        window.open('/my/marketresearch/viewAllAnswers?customerId=' + customerId);
    }
    
    // 保存状态标志
    var isMobileSaving = false;

    // 保存表单
    function saveMobileForm() {
        // 防止重复点击
        if (isMobileSaving) {
            layer.msg('正在保存中，请稍候...', {icon: 16});
            return;
        }

        var formData = $('#mobileForm').serialize();

        // 设置保存状态
        isMobileSaving = true;

        // 更新保存按钮状态
        var $saveBtn = $('.mobile-save-btn');
        var originalText = $saveBtn.html();
        $saveBtn.html('<i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i> 保存中...').prop('disabled', true);

        // 显示保存中的提示
        var loadingIndex = layer.load(2, {
            shade: [0.3, '#000'],
            content: '正在保存客户信息...',
            success: function(layero) {
                layero.find('.layui-layer-content').css({
                    'padding-top': '40px',
                    'width': '200px'
                });
            }
        });

        $.ajax({
            url: '/my/yxkh/update',
            type: 'POST',
            data: formData,
            success: function(ret) {
                // 关闭加载提示
                layer.close(loadingIndex);

                if (ret.state === 'ok') {
                    layer.msg('保存成功！', {
                        icon: 1,
                        time: 2000
                    }, function() {
                        history.back();
                    });
                } else {
                    layer.msg(ret.msg || '保存失败', {icon: 2});
                    // 恢复按钮状态
                    $saveBtn.html(originalText).prop('disabled', false);
                    isMobileSaving = false;
                }
            },
            error: function() {
                // 关闭加载提示
                layer.close(loadingIndex);
                layer.msg('网络错误，请重试', {icon: 2});

                // 恢复按钮状态
                $saveBtn.html(originalText).prop('disabled', false);
                isMobileSaving = false;
            }
        });
    }
    
    // 加载调研概览（移动端版本）
    function loadMobileSurveyOverview() {
        var customerId = '#(yxkh.id??)';
        if (!customerId) {
            $('#mobileSurveyOverview').html('<div style="text-align: center; padding: 15px; color: rgba(255,255,255,0.8);">请先保存客户信息</div>');
            return;
        }
        
        $.ajax({
            url: '/my/marketresearch/getSurveyOverview',
            type: 'GET',
            data: { customerId: customerId },
            success: function(ret) {
                if (ret.state === 'ok') {
                    renderMobileSurveyOverview(ret.data);
                } else {
                    $('#mobileSurveyOverview').html('<div style="text-align: center; padding: 15px; color: rgba(255,255,255,0.8);">加载失败</div>');
                }
            },
            error: function() {
                $('#mobileSurveyOverview').html('<div style="text-align: center; padding: 15px; color: rgba(255,255,255,0.8);">网络错误</div>');
            }
        });
    }
    
    // 渲染移动端调研概览
    function renderMobileSurveyOverview(data) {
        var html = '<div class="survey-stats">' +
            '<div class="survey-stat"><div class="survey-stat-number">' + (data.totalQuestions || 0) + '</div><div class="survey-stat-label">总问题</div></div>' +
            '<div class="survey-stat"><div class="survey-stat-number">' + (data.answeredQuestions || 0) + '</div><div class="survey-stat-label">已回答</div></div>' +
            '<div class="survey-stat"><div class="survey-stat-number">' + (data.surveyorCount || 0) + '</div><div class="survey-stat-label">调研员</div></div>' +
            '<div class="survey-stat"><div class="survey-stat-number">' + (data.completionRate || 0) + '%</div><div class="survey-stat-label">完成率</div></div>' +
            '</div>' +
            '<button class="survey-action-btn" onclick="showSurveyMenuMobile()">调研操作</button>';
        
        $('#mobileSurveyOverview').html(html);
    }
    
    // 防止回车键触发表单提交
    function preventEnterSubmit(event) {
        if (event.keyCode === 13) {
            event.preventDefault();
            return false;
        }
    }

    // 页面加载完成
    $(document).ready(function() {
        #if(!isAdd)
        loadMobileSurveyOverview();
        #end

        // 调试：监听所有表单提交事件
        $(document).on('submit', 'form', function(event) {
            console.log('检测到表单提交事件:', this);
            console.log('表单ID:', $(this).attr('id'));
            console.log('表单类名:', $(this).attr('class'));

            // 如果是主表单之外的表单，阻止提交
            if ($(this).attr('id') !== 'mobileForm') {
                console.log('阻止非主表单的提交');
                event.preventDefault();
                event.stopPropagation();
                return false;
            }
        });

        // 为弹窗中的输入框添加回车键防护
        $(document).on('keypress', '.call-modal-input', function(event) {
            if (event.keyCode === 13) {
                event.preventDefault();
                event.stopPropagation();
                // 如果在被叫号码输入框按回车，直接调用拨打电话
                if ($(this).attr('id') === 'callees') {
                    call(event);
                }
                return false;
            }
        });

        // 阻止弹窗中的任何表单提交行为
        $(document).on('submit', '#callModal form, #callModal .layui-form', function(event) {
            event.preventDefault();
            event.stopPropagation();
            console.log('阻止了弹窗中的表单提交');
            return false;
        });

        // 为弹窗中的按钮添加额外的事件阻止
        $(document).on('click', '#callModal button', function(event) {
            event.stopPropagation();
            // 不阻止默认行为，让onclick正常执行
        });
    });
</script>
#end
