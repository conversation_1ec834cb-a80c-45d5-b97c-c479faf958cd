#set(seoTitle="资产负债管理 "+ (isAdd ? "创建" : "编辑"))
#@layout()
#define main()
#include("/_view/my/common/_my_menu_bar.html")

#define content()
<div>
    <div class="jf-breadcrumb-box">
        <ol class="jf-breadcrumb">
            <li><a href="/">首页</a></li>
            <li id="aolinReportModule" class="clickable-menu-item"><i class="layui-icon layui-icon-chart"></i><button class="layui-btn layui-btn-sm" onclick="openModuleModal('澳林报表')">澳林报表</button></li>
            <li><a href="/my/zcfz">资产负债</a></li>
            <li class="active">#(isAdd ? "创建资产负债" : "编辑资产负债")</li>
        </ol>
    </div>
    <div class="layui-card-body">
        <div class="layui-row">
            <div class="layui-col-md6">
                <form id="mainForm" class="layui-form" style="margin-top: 30px;white-space:nowrap!important;" action="/my/zcfz/#(isAdd ? 'save' : 'update')" method="post">
                    <input type="hidden" name="zcfz.id" value="#(zcfz.id??)">
                    <input type="hidden" name="zcfz.cjr" value="#(zcfz.cjr??)">
                    <input type="hidden" name="zcfz.cjsj" value="#(zcfz.cjsj??)">

                    <div class="layui-form-item">
                        <div class="layui-input-inline">
                            <button class="layui-btn layui-btn-sm" lay-submit lay-filter="component-form-element">保存</button>
                            <button type="reset" class="layui-btn layui-btn-sm layui-btn-primary">重置</button>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="form-inline">
                            <label class="layui-form-label">上级:</label>
                            <div class="layui-input-inline" style="width: 300px;">
                                <input type="text" readonly id="zcfzPidName" value="#(zcfz.pname??pname??)" lay-verify="required" placeholder="请选择上级" autocomplete="off" class="layui-input">
                                <input type="hidden" name="zcfz.pid" id="zcfzPid" value="#(zcfz.pid??pid??)">
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="form-inline">
                            <label class="layui-form-label">编号:</label>
                            <div class="layui-input-inline" style="width: 300px;">
                                <input type="text" name="zcfz.bh" value="#(zcfz.bh??xh??)" lay-verify="required" placeholder="请输入编号" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="form-inline">
                            <label class="layui-form-label">资产名称:</label>
                            <div class="layui-input-inline" style="width: 300px;">
                                <input type="text" name="zcfz.zcmc" value="#(zcfz.zcmc??)" lay-verify="required" placeholder="请输入资产名称" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="form-inline">
                            <label class="layui-form-label">排序:</label>
                            <div class="layui-input-inline" style="width: 300px;">
                                <input type="text" name="zcfz.xh" value="#(zcfz.xh??xh??)" lay-verify="required" placeholder="请输入排序" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="form-inline">
                            <label class="layui-form-label">期初余额:</label>
                            <div class="layui-input-inline" style="width: 300px;">
                                <input type="text" name="zcfz.qcyy" value="#(zcfz.qcyy??)" placeholder="请输入期初余额" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="form-inline">
                            <label class="layui-form-label">期末余额:</label>
                            <div class="layui-input-inline" style="width: 300px;">
                                <input type="text" name="zcfz.qmyy" value="#(zcfz.qmyy??)" placeholder="请输入期末余额" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="form-inline">
                            <label class="layui-form-label">备注:</label>
                            <div class="layui-input-inline" style="width: 300px;">
                                <textarea placeholder="请输入备注" name="zcfz.bz" style="width: 100%; height: 80px;">#(zcfz.bz??)</textarea>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="layui-col-md6">
                <div class="layui-card">
                    <div class="layui-card-header">请选择上级[名称 :排序]</div>
                    <div class="layui-card-body"><div id="treeNode"></div></div>
                </div>
            </div>
        </div>
    </div>
</div>
#end
#end

#define js()
<script type="text/javascript">
    layui.tree.render({
        id: 'treeNode',
        elem: '#treeNode',
        data: #(data??),
        click: function(obj) {
            console.log(JSON.stringify(obj.data));
            document.getElementById("zcfzPidName").value = obj.data.title;
            document.getElementById("zcfzPid").value = obj.data.id;
        $.ajax({
            url: "/my/zcfz/getXh",
            async: true,
            type: "POST",
            cache: false,
            data: {
                id: obj.data.id
            },
            success: function (ret) {
                layer_alert_with_callback(ret.msg, ret.state, "/my/zcfz/edit?id="+ret.id);
            },
            error: function (data) {
                layer_alert("网络操作失败，请咨询老柳! QQ75272683");
            },
            dataType: "json"
        });
        }
    });
    layui.util.event('lay-tree-chose', {
        getChecked: function(othis) {
            var checkedData = layui.tree.getChecked('treeNode'); //获取选中节点的数据
            layer.layer_alert(JSON.stringify(checkedData), {
                shade: 0
            });
            console.log(checkedData);
        }
    });
    $(document).ready(function () {
        $("form").ajaxForm({
            dataType: "json",
            before: function () {
                layer.load(0, {shade: false});
            },
            success: function (ret) {
                layer_alert_with_callback(ret.msg, ret.state, "/my/zcfz/edit?id="+ret.id);
            }, error: function (data) {
                layer_alert("网络操作失败，请咨询老柳! QQ75272683");
            }
        });
    });
</script>
#end
