#set(seoTitle="客户维护管理")
#@layout()
#define main()
#include("/_view/my/common/_my_menu_bar.html")

#define content()
<div>
    <div class="jf-breadcrumb-box">
        <ol class="jf-breadcrumb">
            <li><a href="/">首页</a></li>
            <li id="businessRelated" class="clickable-menu-item"><i class="layui-icon layui-icon-form"></i><button class="layui-btn layui-btn-sm" onclick="openModuleModal('业务相关')">业务相关</button></li>
            <li class="active">客户维护</li>
        </ol>
        <div class="layui-col8">
            <form method="post" action="/my/zdhf">
                <input type="text" name="query" placeholder="名称" size="10">
                #@select("queryKh","kh","id","jc"," ",queryKh??)
                #@select("queryYj","yj","id","mc"," and fl='固定邮件' and zt<>'禁用' ",queryYj??)
                <input type="text" name="queryKhddh" placeholder="客户订单号" size="10">
                <input type="text" name="queryHgbh" placeholder="货柜编号" size="10">
                <input type="text" id="queryKsrq" name="queryKsrq" placeholder="开始日期" autocomplete="off" size="12">
                <input type="text" id="queryJsrq" name="queryJsrq" placeholder="结束日期" autocomplete="off" size="12">
                <select lay-verify="required" lay-search="" name="queryPx">
                    <option value=" order by cjsj desc">创建时间逆序</option>
                    <option value=" order by cjsj asc">创建时间顺序</option>
                </select>
                <input type="submit" class="layui-btn layui-btn-sm" value="查找">
                <input type="button" class="layui-btn layui-btn-sm" onclick="sendTest()" value="测试发送">
                <input type="button" class="layui-btn layui-btn-sm" onclick="batchSend()" value="批量发送">
            </form>
        </div>
        <div class="jf-btn-box">
            <a class="layui-btn layui-btn-sm" href="/my/zdhf/add">创&nbsp;&nbsp;建</a>
            <a class="layui-btn layui-btn-sm" href="/my/zdhf/batchadd">批量创建</a>
        </div>
    </div>
    <br>
    <table class="layui-table" lay-size="sm">
        <thead>
        <tr>
            <th style="width: 1%"><input lay-skin="primary" type="checkbox" name="allCheckBox" id="allCheckBox" lay-filter="allCheckBox" onclick="checkAllCheckBox()" title="全选"></th>
            <th style="width: 240px;">操作</th>
            <th style="width: 120px">创建时间</th>
            <th style="width: 80px">客户</th>
            <th style="width: 120px">货号/分店</th>
            <th style="width: 160px">邮件</th>
            <!--            <th>客户订单名</th>-->
            <th style="width: 120px">客户订单名</th>
            <th style="width: 120px">货柜编号</th>
            <th style="width: 120px">附加邮件标题</th>
            <th style="width: 310px">附件</th>
            <th style="width: 100px">价格</th>
            <th style="width: 120px">中文石种</th>
            <th style="width: 120px">中文加工</th>
            <th style="width: 60px">审核</th>
            <th style="width: 60px">发送</th>
            #role("权限管理员", "超级管理员", "总经理")
            <th style="width: 120px">创建人</th>
            #end
        </tr>
        </thead>
        <tbody>
        #for(x : page.list)
        <tr>
            <td><input lay-skin="primary" type="checkbox" name="checkbox" lay-filter="checkBox" value="#(x.id??)"></td>
            <td>
                <div class="layui-btn-group">
                    <a class="layui-btn layui-btn-sm" href="/my/zdhf/edit?id=#(x.id)">编辑</a>&nbsp;&nbsp;
                    #if(x.shzt??=="通过")
                    <span class="layui-btn layui-btn-sm" onclick="confirmAjax('确定要发送 #escape(x.khhh??)？', '/my/zdhf/send?id=#(x.id)');">发送</span>
                    #end
                    <span class="layui-btn layui-btn-sm" onclick="confirmAjaxLayer('删除 #escape(x.khhh??) 后无法恢复，确定要删除？', '/my/zdhf/delete?id=#(x.id)', this);">删除</span>
                    #if(x.status=="已打开")
                    <i class="fa fa-book">阅</i>
                    #end
                </div>
            </td>
            #set(cjsj=x.cjsj??)
            #set(cjsjgsh=cjsj.replace("-", ""))
            #if(cjsjgsh.length()>14)
            #set(cjsjgsh=cjsjgsh.substring(0, 14))
            #end
            <td>#(cjsjgsh??)</td>
            <td>#(x.jc??)</td>
            <td>#(x.khhh??)</td>
            <td>#(x.yjmc??)</td>
            <!--            <td>#(x.khddh??)</td>-->
            <td>#(x.gcbh??)</td>
            <td>#(x.ddbh??)</td>
            <td>#(x.bt??)</td>
            <td>
                #if(x.fj??)
                #set(fjAttr=(x.fj).split(";"))
                #for(fj : fjAttr)
                <a href="/upload/zdhf/#(x.id??)/#(fj??)" title="#(fj??)" target="_blank">#if(fj?? && fj.length()>8) #(fj.substring(0, 8)) #else #(fj??) #end</a>&nbsp;
                #end
                #end
            </td>
            <td>#if(x.jg??) #(x.jg??)#(x.hb??) #end</td>
            <td>#(x.szmc??)</td>
            <td>#(x.jgmc??)</td>
            <td>
                <input data-id="#(x.id)" data-nr="#(x.shzt??)" #if(x.shzt??=="通过") checked #end type="checkbox" name="mgcCheck" class="mgc-switch mgc-tiny">
            </td>
            <td>#(x.fszt??)</td>
            #role("权限管理员", "超级管理员", "总经理")
            <td>#(x.cjr??)</td>
            #end
        </tr>
        #end
        </tbody>
    </table>
    #@paginate(page.pageNumber, page.totalPage, "/my/zdhf?query=" + query + "&p=")
</div>
#end
#end

<div id="defaultModal" style="display: none;">
    <select id="testSelect">
        <option>;老柳;<EMAIL></option>
        <option>;阿秋;<EMAIL></option>
        <option>;Lin;<EMAIL></option>
        <option>;静静;<EMAIL></option>
        <option>;Hannah;<EMAIL></option>
        <option>;阿旭;<EMAIL></option>
        <option>;秋燕;<EMAIL></option>
        <option>;Winnie;<EMAIL></option>
        <option>;小陈;<EMAIL></option>
    </select>
    <input type="button" class="layui-btn btn-large" name="addHfBtn" onclick="send()" value="发送">
</div>

#define js()
<script type="text/javascript">
    layui.use('laydate', function () {
        var laydate = layui.laydate;

        laydate.render({
            elem: '#queryKsrq'
        });
        laydate.render({
            elem: '#queryJsrq'
        });
    });

    // layui.form.on('checkbox(allCheckBox)', function(data){
    // });

    function checkAllCheckBox() {
        if ($("#allCheckBox").is(':checked')) {
            $.each($('input:checkbox'), function () {
                if ($(this).attr("name") !== "mgcCheck") {
                    $(this).prop("checked", true);
                }
            });
        } else {
            $.each($('input:checkbox:checked'), function () {
                if ($(this).attr("name") !== "mgcCheck") {
                    $(this).prop("checked", false);
                }
            });
        }
    }

    function sendTest() {
        if ($('input[type=checkbox]:checked').not("#allCheckBox").length === 0) {
            layer_alert("没有选中的要测试的客户邮件");
        } else {
            layer.open({
                type: 1,
                title: "选择收件人",
                content: $('#defaultModal'),
                end: function (){
                    document.getElementById("defaultModal").style.display="none";
                }
            });
        }
    }

    function send() {
        let ids = $.map($('input[name="checkbox"]:checked').not("#allCheckBox"), e => $(e).val()).join(';');
        $.ajax({
            url: "/my/zdhf/sendTest",
            async: true,
            type: "POST",
            cache: false,
            data: {
                ids: ids,
                sjr: $('#testSelect').val()
            },
            success: function (data) {
                layer_alert("发送成功");
                layer.closeAll();
            },
            error: function (data) {
                layer_alert(data.msg);
            },
            dataType: "json"
        });
    }

    function batchSend() {
        let ids = $.map($('input[name="checkbox"]:checked').not("#allCheckBox"), e => $(e).val()).join(';');
        $.ajax({
            url: "/my/zdhf/batchSend",
            async: true,
            type: "POST",
            cache: false,
            data: {
                ids: ids
            },
            success: function (data) {
                layer_alert("发送成功");
            },
            error: function (data) {
                layer_alert(data.msg);
            },
            dataType: "json"
        });
    }

    $(document).ready(function () {
        initMagicInput(prepareAction);
    });

    function prepareAction($this) {
        return {
            url: "/my/zdhf/lock",
            data: {
                id: $this.attr("data-id"),
                shzt: $this.attr("data-nr")
            }
        }
    }
</script>
#end