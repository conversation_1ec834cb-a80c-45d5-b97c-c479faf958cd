#set(seoTitle=share.title??)
#@layout()
#define main()
<!-- 内容容器 -->
<div class="jf-panel-box jf-pull-left">

	<!-- 分享详情 -->
	<div class="jf-article">
		<h1 class="jf-article-title">#(share.title)</h1>
		<div class="jf-article-meta">
			<a href="/user/#(share.accountId)">
				<img src="/upload/avatar/#(share.avatar)">
				<span>#(share.nickName)</span>
			</a>
			<span class="jf-article-create-at">#date(share.createAt, "yyyy-MM-dd HH:mm")</span>
			<span class="jf-report" onclick="report();" style="display: none;">举报</span>
		</div>
		<div class="jf-article-content">
			#(share.content)
		</div><!-- end of jf-article-content -->

		<div class="jf-article-footer clearfix">
			#if(project)
				<span class="jf-tag-label">项目:</span>
				<a class="jf-tag" href="/project/#(project.id)" target="_blank">#(project.name)</a>
			#end
			<div class="layui-inline">
				<div class="layui-input-inline">
					<div id="multipleSelect" style="width: 500px;"></div>
				</div>
				<div class="layui-input-inline">
					<input type="button" class="layui-btn layui-btn-sm" value="分享" id="shareButton" onclick="shareFriends('share', '#(share.id)', '#(share.title)')">
				</div>
			</div>
            <div class="jf-article-btn">
                <i title="收藏" class="iconfont icon-favorite #(ret.isFavoriteActive)"></i><span>#(ret.favoriteCount)</span>
                <i title="点赞" class="iconfont icon-like #(ret.isLikeActive)"></i><span>#(ret.likeCount)</span>
            </div>
		</div>

		<div class="jf-article-footer clearfix">
			#if(viewList?? && viewList.size()>0)
			<table class="layui-table" lay-size="sm">
				<tr><th>昵称</th><th>翻阅次数</th></tr>
				#for( x : viewList )
				<tr><td>#(x.nickName??)</td><td>#(x.visitCount??)</td></tr>
				#end
			</table>
			#end
		</div>
	</div><!-- end of jf-article -->

	#include("_reply.html")


</div><!-- end of jf-panel-box -->

<!-- 包含侧边栏文件 -->
#include("_sidebar.html")
#include("../common/_footer_music_player.html")
#end

#define css()
	<link type="text/css" rel="stylesheet" href="/assets/prettify/prettify.css" />
	<link type="text/css" rel="stylesheet" href="/assets/footerMusicPlayer/audio.css" />
	<link type="text/css" rel="stylesheet" href="/assets/css/os-v1.0.css?v=1.2.2" />
	<link type="text/css" rel="stylesheet" href="/assets/css/os-my-space-v1.0.css" />
#end
#define js()
	<script type="text/javascript" src="/assets/footerMusicPlayer/audio.js"></script>
	<script type="text/javascript" src="/assets/prettify/prettify.js"></script>
	<script type="text/javascript">
		var multipleSelect = xmSelect.render({
			el: '#multipleSelect',
			tips: '请选择业务员?',
			filterable: true,
			filterMethod: filterFn,
			data: $.parseJSON('#(accountList??"[]")')
		});
		$(document).ready(function() {
			$("pre").addClass("prettyprint linenums");
			prettyPrint();

			var map = {
				isLoading: false
				, submit_btn: $("#submit_btn")
				, submit_loading: $("#submit_loading")
			};

			$("#submit_btn").bind("click", function() {
				reply("/share/saveReply", "#(share.id)", map);
			});

			$("#replyContent").bind("keydown", function(e) {
				if ((e.ctrlKey || e.metaKey) && e.keyCode==13) {    // ctrl + 回车发送消息
					reply("/share/saveReply", "#(share.id)", map);   // $("#submit_btn").trigger("click");   // 简写 $("#submit_btn").click();
				}
			});

			// 用于显示/隐藏 reply item 中的删除链接
			$(".jf-reply-list").on("mouseover mouseout", "li", function(event) {
				if (event.type == "mouseover") {
					$(this).addClass("hover");
				} else if (event.type == "mouseout") {
					$(this).removeClass("hover");
				}
			});

            // 绑定收藏事件
            var aMap = {
                btn: $("i.icon-favorite")
                , isLoading: false
                , isAdd: #(ret.isFavoriteAdd)
            };
            aMap.btn.bind("click", function() {
                favorite('share', #(share.id),  aMap);
            });

            // 绑定点赞事件
            var bMap = {
                btn: $("i.icon-like")
                , isLoading: false
                , isAdd: #(ret.isLikeAdd)
            };
            bMap.btn.bind("click", function() {
                like('share', #(share.id), bMap);
            });


		#if(mp3Json?? && (mp3Json!="[]"))
			var audioFn = audioPlay({
				song : #(mp3Json??),
				autoPlay : true
			});
		#end
		});
	</script>
#end
