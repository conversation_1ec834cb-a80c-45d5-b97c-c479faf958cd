@charset "UTF-8";

/* bootstrap 的样式 */
* {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

*:before,
*:after {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

.clearfix:before,
.clearfix:after {
    display: table;
    content: " ";
}

.clearfix:after {
    clear: both;
}

h1,
h2,
h3,
h4,
h5,
h6,
.h1,
.h2,
.h3,
.h4,
.h5,
.h6 {
    font-family: inherit;
    font-weight: 500;
    line-height: 1.1;
    color: inherit;
}

h1,
.h1 {
    font-size: 36px;
}

h2,
.h2 {
    font-size: 30px;
}

h3,
.h3 {
    font-size: 24px;
}

h4,
.h4 {
    font-size: 18px;
}

h5,
.h5 {
    font-size: 14px;
}

h6,
.h6 {
    font-size: 12px;
}

.btn-primary {
    color: #fff;
    background-color: #337ab7;
    border-color: #2e6da4;
}

.btn {
    display: inline-block;
    padding: 6px 12px;
    margin-bottom: 0;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.42857143;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    -ms-touch-action: manipulation;
    touch-action: manipulation;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background-image: none;
    border: 1px solid transparent;
    border-radius: 4px;
}

.btn-lg, .btn-group-lg > .btn {
    padding: 10px 16px;
    font-size: 18px;
    line-height: 1.3333333;
    border-radius: 6px;
}

/* bootstrap 的 a 设置 */
a {
    color: #337ab7;
    text-decoration: none;
}

a:hover,
a:focus {
    /* bootstrap 为 #23527c，太暗 */
/ / color: #23527c;
    color: #0044cc;
/ / text-decoration: underline;
}

a:focus {
    outline: thin dotted;
    outline: 5px auto -webkit-focus-ring-color;
    outline-offset: -2px;
}

img {
    vertical-align: middle;
}

input,
button,
select,
textarea {
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
}

button,
input,
optgroup,
select,
textarea {
    margin: 0;
    font: inherit;
    color: inherit;
}

button {
    overflow: visible;
}

button,
select {
    text-transform: none;
}

button,
html input[type="button"],
input[type="reset"],
input[type="submit"] {
    -webkit-appearance: button;
    cursor: pointer;
}

button[disabled],
html input[disabled] {
    cursor: default;
}

button::-moz-focus-inner,
input::-moz-focus-inner {
    padding: 0;
    border: 0;
}

input {
    line-height: normal;
}

table {
    background-color: transparent;
    border-spacing: 0;
    border-collapse: collapse;
    width: 100%;
    max-width: 100%;
    word-break: break-all;
    word-wrap: break-word;
}

td,
th {
    padding: 0;
    text-align: left;
}

caption {
    padding-top: 8px;
    padding-bottom: 8px;
    color: #777;
    text-align: left;
}

pre {
    word-break: break-all;
    word-wrap: break-word;
    border: 1px solid #ccc;
}

/* 全局字体 */
body, div, span, a, p, pre, code, button, input, select, textarea, h1, h2, h3, h4, h5, h6, ul, ol, li, dl, dt, dd, form, table, th, td {
    font-family: "Microsoft YaHei", 微软雅黑, "Helvetica Neue", Arial, "\5b8b\4f53", sans-serif;
}

/* 全局margin padding */
body, div, p, ul, ol, li, dl, dt, dd, h1, h2, h3, h4, h5, h6, pre, code, form, fieldset, legend, input, button, textarea, blockquote, th, td, img {
    margin: 0;
    padding: 0;
/ / color: #000000;
}

h1, h2, h3, h4, h5, h6, .h1, .h2, .h3, .h4, .h5, .h6 {
    line-height: 1.1;
}

/* 全局背景色
 常用灰色到黑色值: cccccc 999999 666666 333333 000000 bebebe
 */
body {
    color: #333333;

    /* facebook */
    background-color: #e9ebee;

    /* e.tech.163.com */
    /*background-color: #E8E8E8;*/
    /* weibo.com */
    /*background-color: #d4d5e0;*/
    /* 微信公众平台 */
    /*background-color: #E7E8EB;*/
    /*background-color: #EBEBEB;*/
    /*background-color: #EEEEEE;*/
    /*background-color: #EFEFEF;*/
    /*background-color: #f0f0f0;*/
    /*background-color: #f3f3f3;*/
    /*background-color: #f5f5f5;*/
    /*background-color: #f7f7f7;*/
    /*background-color: #f8f8f8;*/
    /*background-color: #f9f9f9;*/
    /* 以上由深到浅，以供将来调整;*/

    /* facebook.com 首页 */
    /*background-color: #e9eaed;*/

    /*http://blog.163.com/*/
    /*background-color: #dddddd;*/
    /*http://zhidao.baidu.com/daily/view?id=9539*/
    /*http://zhidao.baidu.com/daily*/
    /*background-color: #f3f3f3;*/

    /* http://www.w3school.com.cn/css/ */
    /*background-color: #eeeeee;*/
    /* OSC*/
    /*background-color: #f9f9f9; 个人空间 f9f9f9，其它 f5f5f5*/
    /* toutiao.com */
    /*background-color: #f8f8f8;*/

    /*background-color: #e7e7e7;*/
    /*background-color: #f5f5f5;*/
    /*line-height: 1.5;*/
    /*font-size: 18px;*/;
}

body,
div,
p {
    word-wrap: break-word;
}

/* 全局 p */
p {
    /*font-size: 18px;*/
    /*line-height: 30px;*/

    /*line-height: 1.5;*/
    /*text-indent: 2em;*/
}

.jf-pull-left {
    float: left !important;
}

.jf-pull-right {
    float: right !important;
}

/* 去掉border圆角 */
.jf-border-radius-0 {
    border-radius: 0;
}

/* 用于将 btn label 样式修饰的 btn label a 的组件变成撑满空间的可点击的区块 */
.jf-display-block {
    display: block;
}

/* 去掉btn 与 label 的 border圆角，通过覆盖原有样式达到定制 bootstrap的目的 */
.btn, .label {
    border-radius: 0;
}

.jf-btn {
    padding: 3px 0.7em;
    margin-right: 5px;
    font-size: 18px;
    color: #fff;
    border-radius: 3px;
}

.jf-btn:hover,
.jf-btn:active,
.btn:hover,
.btn:active {
    color: #fff;
    background-color: #286090;
}

/* facebook.com btn */
.facebook-btn {
    background-color: #4267b2;
    border-color: #4267b2;
}

.facebook-btn:hover {
    background-color: #365899;
    border-color: #365899;
}

.facebook-nav {
    background-color: #3B5998;
}

.facebook-tip {
    background: #4080ff;
}

/* facebook 人名上链接上的颜色 */
.facebook-hyper-link {
    color: #365899;
}

/* weixin 相关颜色 */
.weixin-list-title {
    color: #576B95;
}

/* 头部 */
.jf-header-box {
    position: relative;
    width: 1300px;
    height: 50px;
    line-height: 1.5;
    margin: 0 auto 20px auto;
    background-color: #3a5795;
/ / background-color: #47639E;
    color: #ffffff;
    box-shadow: 0 0 7px 2px #dcdcdc;
    border-radius: 2px;

/ / border-bottom: 1 px solid #103480;
}

.jf-header-box a {
    /* color: #ffffff; */
    /* color: #fefefe; */
    color: #eeeeee;
    text-decoration: none;
}

/* 导航 logo */
.jf-logo-box {
    float: left;
    margin-left: 20px;
}

.jf-logo-box > a {
    line-height: 50px;
    font-size: 30px;
    display: block;
    font-weight: 400;
}

/* 导航主菜单 */
.jf-nav-menu-box {
    float: left;
    margin-left: 110px;
    list-style-type: none;
}

/* 导航主菜单项之间的左间距 */
.jf-nav-menu-box > li {
    float: left;
}

.jf-nav-menu-box > li > a {
    display: block;
    text-align: center;
    margin-right: 14px;
    width: 80px;
    height: 50px;
/ / font-size: 21 px;
    font-size: 18px;
/ / font-size: 22 px;
    line-height: 56px;
    text-decoration: none;
}

.jf-nav-menu-box > li > a:hover {
    background-color: #000000;
}

.jf-nav-menu-current {
    background-color: #000000;
}

/* 登录用户工具栏容器 */
.jf-user-toolbar-box {
    float: right;
}

/* 登录用户图像 */
.jf-login-user-img {
    float: left;
    margin: 6px 20px 0 0;
}

.jf-login-user-img > img {
    height: 40px;
    width: 40px
}

/* 用户下拉菜单小三角变小 */
.fa-caret-down {
    font-size: 14px;
}

/* 用 css 模拟出小三角 */
.jf-caret-down {
    position: relative;
    display: inline-block;
    font-size: 0;
    line-height: 0;
    height: 10px;
    width: 10px;
}

.jf-caret-down:before {
    position: absolute;
    top: 2px;
    left: 1px;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid #fff;
    font-size: 0;
    line-height: 0;
    content: "";
}

/* 鼠标悬停时箭头向上 */
.jf-login-user-dropdown-menu:hover .jf-caret-down:before {
    top: -3px;
    border-top: 5px solid transparent;
    border-bottom: 5px solid #fff;
}

/* 登录用户名与下拉菜单 */
.jf-login-user-dropdown-menu {
    float: left;
    position: relative;
}

.jf-login-user-dropdown-menu > a {
    display: block;
    padding-right: 20px;
    height: 50px;
}

.jf-login-user-dropdown-menu > a > span {
    margin-right: 10px;
    font-size: 16px;
    line-height: 55px;
    color: #fefefe;
}

.jf-login-user-dropdown-menu-content {
    display: none;
    position: absolute;
    z-index: 999;
    list-style-type: none;
    top: 100%;
    right: 0;
    border: 1px solid rgba(0, 0, 0, .15);
    box-shadow: 0 6px 12px rgba(0, 0, 0, .175);
}

.jf-login-user-dropdown-menu-content > li > a {
    display: block;
    padding: 8px 0 6px 18px;
    width: 100px;
    font-size: 14px;
    color: black;
    background-color: #ffffff;
}

/* 鼠标悬停打开下拉菜单 */
.jf-login-user-dropdown-menu:hover > .jf-login-user-dropdown-menu-content {
    display: block;
/ / background-color: #2e6da4;
/ / #337ab7
}

.jf-login-user-dropdown-menu-content > li > a:hover {
    color: red;
    font-weight: bold;
    background-color: #E0E0E0;
/ / background-color: #f5f5f5;
/ / background-color: #E8E8E8;
}

/* 消息提醒弹出层 */
.remind-layer {
    text-align: center;
    min-width: 190px;
    position: absolute;
    padding: 8px 10px;
    z-index: 99;
    top: 56px;
    right: 0;
    background-color: #fff9c9;
    border: 1px solid #c7bf93;
    border-radius: 5px;
}

.remind-layer a {
    /*color: #337ab7;*/
    color: #000;
    font-size: 18px;
    display: block;
    padding: 3px 0;
    text-decoration: underline;
    line-height: 20px;
}

.remind-layer-caret-up {
    position: absolute;
    z-index: 100;
    top: -7px;
    right: 26px;
    font-size: 0;
    line-height: 0;
    height: 10px;
    width: 10px;
}

.remind-layer-caret-up:before {
    position: absolute;
    border-left: 7px solid transparent;
    border-right: 7px solid transparent;
    border-bottom: 7px solid #c7bf93;
    font-size: 0;
    line-height: 0;
    content: "";
}


/* 未登录用户显示登录/注册链接 */
.jf-no-login {
    float: right;
    margin: 16px 20px 0 0;
}

.jf-no-login span {
    margin-left: 10px;
    font-size: 18px;
}


/* 中部容器 */
.jf-body-box {
    width: 96vw;
    /*min-height: 800px;*/
    margin: 20px auto;
    /*background-color: #ffffff;*/
}

/* 脚部容器 */
.jf-footer-box {
    font-size: 14px;
    margin: 20px auto 20px auto;
    padding: 10px 0;
    width: 96vw;
    height: 50px;
    text-align: center;
    border-top: 1px solid #999999;
    /*border-top: 1px  #737373 solid;*/
}

.jf-footer-box ul {
    /* ul 的 inline-block 极度关键，这样能配合父元素的 text-align:center 使整个 ul 居中显示 */
    display: inline-block;
    list-style-type: none;
}

.jf-footer-box li {
    float: left;
    margin: 0 30px 0 0;
}

.jf-footer-box li a {
    display: block;
    /* 让 a 元素更大些，利于点击 */
    padding: 0.5em;
    text-decoration: none;
    color: #333333;
}

.jf-border-red {
    border: 1px solid red;
}

.jf-border-black {
    border: 1px solid black;
}

.jf-border-blue {
    border: 1px solid blue;
}

.jf-border-green {
    border: 1px solid green;
}

.jf-border-yellow {
    border: 1px solid yellow;
}

/* ----------------------------------------------------------------------------------- */
/* 面板容器 */
.jf-panel-box {
    width: 100%;
    line-height: 1.5;
    font-size: 18px;
}

/* 面板 */
.jf-panel,
.jf-sidebar,
.jf-article,
.jf-reply,
.jf-my-panel {
    /* background-color: #ffffff; */
    background-color: #fefefe;
}

.jf-panel,
.jf-my-panel,
.jf-doc-panel {
    margin: 0 0 20px 0;
    padding: 0 30px;
    box-shadow: 0 0 7px 2px #dcdcdc;
    border: 1px solid #cccccc;
    /*border: 1px solid #bebebe;*/
    border-radius: 2px;
}

/* 面板名称 */
.jf-panel-name,
.jf-my-panel-name {
    padding: 9px 0;
    color: #23527c;
    font-size: 28px;
/ / border-bottom: 1 px solid #a8a8a8;
/ / font-weight: bold;
}

.jf-panel-name > a,
.jf-my-panel-name > a {
    color: #23527c;
/ / color: #337ab7;
    text-decoration: none;
}

/* 首页 panel 上的更多链接 */
.jf-panel-more {
    float: right;
    margin: -35px 12px 0 0;
    font-size: 20px;
    color: #23527C;
}

/* 面板内部列表 */
.jf-panel-list {
    list-style-type: none;
    overflow: hidden;
}

.jf-panel-list > li {
    padding: 15px 0;
    min-height: 90px;
/ / border-bottom: 1 px dashed #a8a8a8;
/ / border-bottom: 1 px solid #ccc;
    border-bottom: 1px solid #ddd;
/ / border-bottom: 1 px solid #eee;
}

.jf-panel-list > li:last-child {
    border-bottom-width: 0;
}

/* 面板内部左侧图像 */
.jf-panel-img {
    float: left;
    margin-top: 3px;
}

.jf-panel-img img {
    width: 60px;
    height: 60px;
    border-radius: 5px;
}

.jf-panel-item {
    margin-left: 80px;
    /*width: 658px;*/
}

.jf-panel-item > h3 {
    font-size: 18px;
    margin-bottom: 6px;
}

.jf-panel-item > h3 > a {
    color: #337ab7;
    text-decoration: none;
}

.jf-panel-item > h3 > a:hover {
/ / text-decoration: underline;
    /* 这里应该稍亮的颜色，或者就为 #337ab7，然后原来的颜色改深 */
    color: #365899;
/ / color: #23527c;
/ / color: #576B95;
}

.jf-panel-item p {
    font-size: 16px;
/ / line-height: 1.5;
    line-height: 28px;
    color: #252525;
}


/* ----------------------------------------------------------------------------------- */
/* 侧边栏容器 */
.jf-sidebar-box {
    width: 250px;
    line-height: 1.2;
    font-size: 16px;
}

/* 侧边栏 */
.jf-sidebar {
    margin: 0 0 20px 0;
    box-shadow: 0 0 7px 2px #dcdcdc;
    /*min-height: 100px;*/
    border: 1px solid #cccccc;
    border-radius: 2px;
}

/* 侧边栏列表项文件字降低灰度，以便突出 panel 内容 */
.jf-sidebar li,
.jf-sidebar li a {
    color: #666666;
    text-decoration: none;
}

/* 完美长度的链接，不换行，超出边界长度用省略号省略 */
.jf-perfect-length-title {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

/* 侧边栏按钮 */
.jf-sidebar-btn {
    display: block;
    border: 0;
    border-radius: 2px;
}

.jf-sidebar-btn:hover {
    color: #fff;
    background-color: #286090;
    border-color: #204d74;
}

.jf-weixin-qr {
    display: block;
    margin: 18px 0 18px 40px;
    width: 210px;
    height: 210px;
    text-align: center;
}

/* 侧边栏名称 */
.jf-sidebar-name {
/ / margin: 0 15 px;
    margin: 10px 15px 10px 15px;
    color: #23527c;
    text-decoration: none;
    font-size: 22px;
    line-height: 22px;
/ / font-weight: bold;

/ / border-bottom: 1 px solid #a8a8a8;
}

.jf-sidebar-name a {

    color: #23527c;
/ / color: #337ab7;
    text-decoration: none;
}

.jf-sidebar-hot-list {
    list-style-type: none;
    overflow: hidden;
    padding: 0 20px 10px 20px;
    clear: both;
}

.padding-left-25 {
    padding-left: 25px;
}

.jf-sidebar-hot-list > li {
/ / border-bottom: 1 px dashed #a8a8a8;
/ / border-bottom: 1 px solid #e8e8e8;
/ / padding: 8 px 0 6 px 0;
    padding: 5px 0;
}

.jf-sidebar-hot-list > li:last-child {
    border-bottom-width: 0;
}

.jf-sidebar-donate-money,
.jf-sidebar-donate-fans {
    font-size: 18px;
    color: #666;
}

.jf-sidebar-donate-money {
    float: left;
    width: 110px;
}


/* ----------------------------------------------------------------------------------- */
/* 项目页面 */
/* 热门列表 */
.jf-sidebar-hot-list li a {
    display: block;
    text-decoration: none;
    color: #2d64b3;
    font-size: 16px;
/ / line-height: 36 px;
    /* 以下三行实现超出部分使用省略号的效果 */
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.jf-sidebar-hot-list li a:hover {
/ / color: #365899;
/ / color: #23527c;
/ / color: #576B95;
}

/* 分页 */
.jf-paginate {
    text-align: center;
    list-style-type: none;
    margin: 20px 0;
}

.jf-paginate li {
    display: inline;
}

.jf-paginate a {
    display: inline;
    margin-right: 2px;
/ / font-family: Courier New, Arial;
    font-size: 16px;
    color: #58595b;
    background-color: #f0f0f0;
    padding: 4px 8px;
    text-decoration: none;
}

.jf-paginate a.current-page {
    background-color: #6e7b8b;
    color: #ffffff;
    font-weight: bold;
}

.jf-paginate a.pre-page,
.jf-paginate a.next-page {
    background-color: #cccccc;
    font-weight: bold;
}

.jf-paginate a:hover {
    background-color: #6e7b8b;
    color: #ffffff;
}


/* ----------------------------------------------------------------------------------- */
/* tech.163.com 的样式 */
/*.jf-article-detail {*/
/*font-size: 18px;*/
/*// font-family: 'Microsoft YaHei';*/
/*line-height: 30px;*/
/*text-indent: 2em;*/
/*margin: 26px 0;*/
/*}*/

/* 文章、回复 */
.jf-article,
.jf-reply {
    margin: 0 0 20px 0;
    padding: 0 30px 0 30px;
    box-shadow: 0 0 7px 2px #dcdcdc;
    border: 1px solid #cccccc;
    border-radius: 2px;
}

.jf-article {
    min-height: 330px;
}

/* 文章标题 */
.jf-article-title {
    text-align: center;
    padding: 20px 0 5px 0;
    font-size: 30px;
    letter-spacing: 1px;
    color: #404040;
/ / font-weight: bold;
}

/* 文章发布者图相、名字、日期 */
.jf-article-meta {
    color: #888888;
    border-bottom: 1px solid #b0b0b0;
    padding-bottom: 10px;
    margin-bottom: 12px;
}

.jf-article-create-at {
    font-size: 16px;
    color: #999999;
}

.jf-report {
    font-size: 14px;
    color: #999999;
}

.jf-report:hover {
    cursor: pointer;
}

.jf-article-meta .jf-report {
    float: right;
    margin: 12px 10px 0 0;
}

.jf-article-meta img {
    display: inline-block;
    height: 40px;
    width: 40px;
    border-radius: 40px;
    margin-right: 4px;

}

.jf-article-meta span {
    display: inline-block;
    margin-right: 10px;
    vertical-align: middle;
}

/* 文件章内容 */
.jf-article-content {
    font-size: 18px;
    line-height: 30px;
    margin: 12px 0 15px 0;
/ / border: 2 px solid red;
    overflow: hidden;
}

/* 防止图片超出父标签所能容许的最大范围 */
.jf-article-content img {
    max-width: 100%
}

.jf-article-content p,
.jf-article-content ul,
.jf-article-content ol,
.jf-article-content dl {
    font-size: 18px;
/ / margin-bottom: 14 px;
    margin-bottom: 16px;
}

/* ueditor 会自动在 li 标签内生成 p 标签，故复位其 margin */
.jf-article-content li > p {
    margin: 0;
}

.jf-article-content pre,
.jf-article-content code {
    font-size: 16px;
    line-height: 1.42;
    margin-bottom: 14px;
    border-radius: 2px;
}

.jf-article-content ul,
.jf-article-content ol,
.jf-article-content dl {
    /*padding-left: 25px;*/
    padding-left: 30px;
}

.jf-article-content h1,
.jf-article-content h2,
.jf-article-content h3,
.jf-article-content h4,
.jf-article-content h5,
.jf-article-content h6 {
    font-weight: normal;
    color: #222222;
}

.jf-article-content h1 {
/ / font-size: 28 px;
    font-size: 1.8em;
    margin: 0.67em 0;
}

.jf-article-content h2 {
/ / font-size: 27 px;
    font-size: 1.5em;
    margin: 0.83em 0;
}

.jf-article-content h3 {
/ / font-size: 25 px;
    font-size: 1.17em;
    margin: 1em 0;
}

.jf-article-content h4,
.jf-article-content h5,
.jf-article-content h6 {
/ / font-size: 23 px;
    font-size: 1em;
    margin: 1.6em 0 1em 0;
}

.jf-article-content h6 {
    font-weight: 500;
}

.jf-article-footer {
/ / height: 20 px;
    text-align: left;
    margin-bottom: 20px;
}

/* 点赞、收藏、分享、举报功能按钮 */
.jf-article-btn {
    float: right;
    margin: -5px 10px 0 0;
    color: #666;
}

.jf-article-btn i {
    margin-left: 20px;
}

.jf-article-btn i:hover {
    cursor: pointer;
    color: #f90;
}

.jf-article-btn i.active {
    color: #f90;
}

.jf-article-btn span {
    position: relative;
    bottom: 1px;
    left: 5px;
}

.jf-tag-label {
    /*font-size: 18px;*/
    /*font-weight: bold;*/
    font-size: 22px;
    color: #23527c;
}

.jf-tag {
    display: inline-block;
/ / color: #fff;
/ / background-color: #6e7b8b;
    background-color: #337ab7;
/ / color: #58595b;
    color: #fff;
    padding: 2px 8px;
    margin: 0 0 10px 5px;
}

.jf-tag:visited {
    color: #fff;
}

.jf-tag:hover {
    color: #fff;
    background-color: #286090;
    border-color: #204d74;
}

/* 文章回复 */
.jf-reply > h2 {
    font-size: 28px;
    color: #23527c;
/ / font-weight: bold;
/ / font-size: 24 px;
    padding: 12px 0;
    position: relative;
    border-bottom: 1px solid #b0b0b0;
}

.jf-reply-list {
    list-style-type: none;
}

.jf-reply-list > li {
    padding: 15px 0;
    overflow: hidden;
    zoom: 1;
/ / border-bottom: 1 px solid #e0e0e0;
    border-bottom: 1px solid #e8e8e8;
/ / border-bottom: 1 px dashed #b0b0b0;
}

.jf-reply-list > li:last-child {
    border-bottom: none;
}

.jf-reply-user-img {
    float: left;
    margin-top: 5px;
}

.jf-reply-user-img img {
    height: 40px;
    width: 40px;
    border-radius: 40px;
}

.jf-reply-item {
    margin-left: 55px;
}

.jf-reply-user-name {
    font-size: 16px;
    margin-bottom: 2px;
    display: inline-block;
}

.jf-reply-time {
    float: right;
    color: #999999;
    font-size: 14px;
    line-height: 14px;
    padding-top: 6px;
}

.jf-reply-content {
/ / margin-bottom: 5 px;
    font-size: 16px;
/ / line-height: 1.72;
    line-height: 28px;
}

.jf-reply-and-delete {
    float: right;
/ / min-width: 86 px;
}

.jf-reply-link,
.jf-reply-delete {
    float: right;
    color: #2d64b6;
    font-size: 16px;
    margin-left: 10px;
}

.jf-reply-delete {
    visibility: hidden;
}

.jf-reply-list li.hover .jf-reply-delete {
    visibility: visible;
}

/* 用于容纳输入框与提交按钮 */
.jf-reply-input-box {
    position: relative;
    margin-top: 10px;
    left: -6px;
/ / border: 2 px solid red;
}

.jf-reply-mini-editor {
    display: block;
    word-wrap: break-word;
    word-break: break-all;

    /* 去掉div获取焦点的时候的虚框效果 */
    outline: none;
    -moz-outline: 0;
    resize: none;
    /* 去掉x y 轴两个滚动条 */
    overflow: hidden;

    border: none;
    border-bottom: 1px solid #333;
    width: 615px;
    height: 30px;
    font-size: 16px;
    /* 上下方向的 padding 值合适，才能避免在输入时高度不合理减少 */
    padding: 3px 6px;
    background-color: #ffffff;
}

.jf-reply-submit {
    position: absolute;
    right: -6px;
    bottom: 3px;
    padding: 2px 6px 3px 6px;
    background-color: #4682b4;
/ / background-color: #204d74;
    color: #ffffff;
    font-size: 16px;
    border-radius: 3px;
}

.jf-reply-submit:hover {
    color: #ffffff;
    cursor: pointer;
    background-color: #204d74;
}

.jf-reply-loading {
    position: absolute;
    right: -3px;
    bottom: 3px;
    display: none;
}

/* 捐助 */
.jf-donate-panel-name {
    border-bottom: 1px solid #999999;
}

.jf-donate-table-box {
    padding: 5px 10px;
}

.jf-donate-table {
    text-align: left;
    font-size: 16px;
    line-height: 1.5;
/ / color: #252525;
    color: #333;
    width: 100%;
    word-break: break-all;
    word-wrap: break-word;
}

.jf-donate-table tr {
    border-bottom: 1px dashed #ddd;
/ / border-bottom: 1 px solid #f0f0f0;
}

.jf-donate-table tr:last-child {
/ / border-bottom: none;
}

.jf-donate-table td {
    padding: 8px 0;
}

.jf-donate-money {
    color: #3366CC;
}

.jf-donate-username {
    color: #333;
}

.jf-donate-message {
    color: #a1a1a1;
}

.jf-donate-time {
    text-align: right;
    color: #a1a1a1;
}

.jf-donate-top-btn {
    margin: 5px 15px 5px 15px;
}

.jf-donate-bottom-btn {
    margin: 10px 15px 15px 15px;
}

.jf-donate-btn {
    font-size: 18px;
    border-radius: 3px;
    color: white;
}

.jf-donate-btn:hover {
    color: white;
    background-color: #286090;
}

.jf-donate-qr {
    margin: 10px 0 10px 48px;
}

.jf-donate-qr > img {
    width: 198px;
    height: 198px;
}

.jf-set-relative-position {
    position: relative;
}

.jf-sidebar #jf-cash-select {
    position: absolute;
    top: 8px;
    right: 10px;
    outline: none;

    color: #a0a0a0;
    border-color: #d0d0d0;
    border-radius: 3px;
}

/* 登录与注册 */
.jf-reg-box {
    width: 1300px;
    min-height: 420px;
}

.jf-reg-panel {
    margin-top: 30px;
}

.jf-reg-panel-name {
    margin-left: -15px;
    margin-top: 30px;
    text-align: center;
    color: #23527c;
}

.jf-reg-panel-content {
    margin: 30px 0;
    height: 300px;
}

.jf-reg-table {
    width: 100%;
}

.jf-reg-table th,
.jf-reg-table td {
/ / border: 1 px solid blue;
}

.jf-reg-table th {
    width: 37%;
    text-align: right;
    line-height: 20px;
    font-size: 20px;
    font-weight: normal;
}

.jf-reg-table td {
    padding: 8px 0 8px 20px;
}

.jf-reg-table input.TEXT {
    height: 35px;
    width: 260px;
    padding: 5px 5px;
}

.jf-reg-table input.SUBMIT {
    margin-left: 168px;
    padding: 3px 8px;
    font-weight: bold;
    cursor: pointer;
}

.jf-reg-table .captcha {
    height: 31px;
    width: 95px;
    vertical-align: bottom;
    margin-right: 2px;
    cursor: pointer;
}

.jf-reg-table span {
    margin-left: 10px;
    font-size: 16px;
    color: red;
}

.jf-reg-table .jump-link {
    text-align: center;
    font-size: 16px;
}

.jf-reg-table .jump-link a {
    text-decoration: underline;
}

#jf_reg_msg_ok_panel {
    display: none;
}

#jf_reg_msg_ok_panel #reg_ok_msg {
    margin-top: 80px;
    color: #23527c;
    font-size: 22px;
    text-align: center;
}

#jf-resend-activate-email {
    margin-top: 150px;
    text-align: center;
    font-size: 20px;
}

#jf-resend-activate-email input.TEXT {
    margin-left: 10px;
    padding: 5px 5px;
    width: 260px;
}

#jf-resend-activate-email input.BUTTON {
    padding: 4px 8px;
    margin-left: 15px;
}

.jf-donate-box {
    margin-top: 55px;
}

.jf-wx-donate-box {
    float: left;
    margin-left: 250px;
}

.jf-ali-donate-box {
    float: left;
    margin-left: 165px;
}

.jf-donate-box span {
    display: block;
    font-size: 16px;
/ / font-weight: bold;
    margin: 3px 0 0 60px;
}

.jf-donate-cash-select-box {
    margin-top: 0;
    text-align: center;
}

.jf-donate-cash-select-box > select {
    color: #a0a0a0;
    border-color: #d0d0d0;
    outline: none;
    border-radius: 3px;
}

.jf-download-link {
    text-align: center;
    margin-top: 45px;
    font-size: 14px;
}

.jf-download-link a:hover {
    color: #337ab7;
    text-decoration: underline;
/ / color: #0088cc;
/ / boostrap 超链接默认色
}

/* 文档频道 */
.jf-doc-box {
    width: 100%;
    line-height: 1.5;
    font-size: 18px;
}

.jf-doc-panel {
    position: relative;
    /* 文章详情页背景色 */
/ / background-color: #fefefe;
/ / background-color: #fdfdfd;
    background-color: #fcfcfc;
/ / background-color: #fafafa;
    padding: 0 0;
    min-height: 475px;
}

.doc-menu-box {
    position: absolute;
    height: 100%;
    overflow-x: hidden;
    overflow-y: auto;
/ / top: 0;
/ / bottom: 0;
    width: 280px;
    padding: 10px 20px;
/ / background-color: #f3f3f3;
    background-color: #f5f5f5;

/ / border-right: 1 px solid #999;
/ / border-right: 1 px solid #a0a0a0;
/ / border-right: 1 px solid #a8a8a8;
/ / border-right: 1 px solid #b0b0b0;
    border-right: 1px solid #b8b8b8;
}

.doc-menu-box::-webkit-scrollbar {
    width: 7px;
    height: 7px;
}

.doc-menu-box::-webkit-scrollbar-thumb {
    /*background-color: #a9a9aa; 默认背景色*/
/ / background-color: #d3d6dc;
/ / background-color: #d8d8d8;
    background-color: #e0e0e0;
    border-radius: 3px
}

.doc-menu-box::-webkit-scrollbar-thumb:hover {
    background-color: #a9a9aa;
}

.doc-menu-box ul {
    list-style-type: none;
}

.doc-main-menu {
    font-size: 16px;
    line-height: 1.6;
    font-weight: 600;
}

.doc-main-menu a,
.doc-sub-menu a {
    color: #333;
    display: block;
}

.doc-main-menu a {
    padding-left: 10px;
}

.doc-sub-menu li a {
    padding-left: 20px;
}

.doc-sub-menu {
    font-size: 14px;
    line-height: 24px;
    font-weight: 400;
}

.doc-main-menu.active,
.doc-main-menu:hover,
.doc-sub-menu li.active,
.doc-sub-menu li:hover {
/ / background-color: #c0c0c0;
    background-color: #c9c9c9;
}

.doc-content-box {
    float: left;
    width: 750px;
    margin: 15px 0 15px 315px;

/ / border: 1 px solid blue;
}

/* 文档标题 */
.doc-title {
    font-size: 30px;
    line-height: 1.2;
    border-bottom: 1px solid #b0b0b0;
    padding-bottom: 8px;
    margin-bottom: 16px;
}

/* 文档内容 */
.doc-content {
    font-size: 16px;
/ / line-height: 26 px;
/ / line-height: 24 px;
    line-height: 1.5;
    margin: 12px 0 15px 0;
    overflow: hidden;

    color: #222;
}

/* 防止图片超出父标签所能容许的最大范围 */
.doc-content img {
    max-width: 100%
}

.doc-content p,
.doc-content ul,
.doc-content ol,
.doc-content dl {
    font-size: 16px;
    margin-bottom: 10px;
}

.doc-content ol li {
    padding-left: 6px;
}

/* ueditor 会自动在 li 标签内生成 p 标签，故复位其 margin */
.doc-content li > p {
    margin: 0;
    line-height: 26px;
}

.doc-content pre,
.doc-content code {
    font-size: 16px;
    line-height: 1.42;
    margin-bottom: 14px;
    border-radius: 2px;
}

.doc-content ul,
.doc-content ol,
.doc-content dl {
    /*padding-left: 25px;*/
    padding-left: 30px;
}

.doc-content h1,
.doc-content h2,
.doc-content h3,
.doc-content h4,
.doc-content h5,
.doc-content h6 {
    font-weight: normal;
    color: #222222;
}

.doc-content h1 {
/ / font-size: 28 px;
/ / font-size: 1.8 em;
    font-size: 24px;
    margin: 14px 0 10px 0;
}

.doc-content h2 {
/ / font-size: 27 px;
/ / font-size: 1.5 em;
    font-size: 22px;
    margin: 14px 0 10px 0;
}

.doc-content h3 {
/ / font-size: 25 px;
/ / font-size: 1.17 em;
    font-size: 20px;
    margin: 12px 0 10px 0;
}

.doc-content h4,
.doc-content h5,
.doc-content h6 {
/ / font-size: 23 px;
/ / font-size: 1 em;
    font-size: 18px;
    margin: 12px 0 10px 0;
}

.doc-content h6 {
    font-weight: 500;
}

.doc-content-footer {
/ / height: 20 px;
    text-align: left;
    margin-bottom: 20px;
}

.ov {
    width: 120px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

#myModal .modal-dialog {
    width: 50%;
    height: 300px;
}

.fancyTable td, .fancyTable th {
    /* appearance */
    border: 1px solid #778899;

    /* size */
    padding: 5px;
}

.fancyTable {
    /* text */
    font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

.fancyTable tbody tr td {
    /* appearance */
    background-color: #eef2f9;
    background-image: -moz-linear-gradient(
            top,
            rgba(255, 255, 255, 0.4) 0%,
            rgba(255, 255, 255, 0.2) 50%,
            rgba(255, 255, 255, 0.1) 51%,
            rgba(255, 255, 255, 0.0) 100%);

    background-image: -webkit-gradient(
            linear, left top, left bottom,
            color-stop(0%, rgba(255, 255, 255, 0.4)),
            color-stop(50%, rgba(255, 255, 255, 0.2)),
            color-stop(51%, rgba(255, 255, 255, 0.1)),
            color-stop(100%, rgba(255, 255, 255, 0.0)));

    /* text */
    color: #262c31;
    font-size: 11px;
}

.fancyTable tbody tr.odd td {
    /* appearance */
    background-color: #d6e0ef;
    background-image: -moz-linear-gradient(
            top,
            rgba(255, 255, 255, 0.4) 0%,
            rgba(255, 255, 255, 0.2) 50%,
            rgba(255, 255, 255, 0.1) 51%,
            rgba(255, 255, 255, 0.0) 100%);

    background-image: -webkit-gradient(
            linear, left top, left bottom,
            color-stop(0%, rgba(255, 255, 255, 0.4)),
            color-stop(50%, rgba(255, 255, 255, 0.2)),
            color-stop(51%, rgba(255, 255, 255, 0.1)),
            color-stop(100%, rgba(255, 255, 255, 0.0)));
}

.fancyTable thead tr th,
.fancyTable thead tr td,
.fancyTable tfoot tr th,
.fancyTable tfoot tr td {
    /* appearance */
    background-color: #8ca9cf;
    background-image: -moz-linear-gradient(
            top,
            rgba(255, 255, 255, 0.4) 0%,
            rgba(255, 255, 255, 0.2) 50%,
            rgba(255, 255, 255, 0.1) 51%,
            rgba(255, 255, 255, 0.0) 100%);

    background-image: -webkit-gradient(
            linear, left top, left bottom,
            color-stop(0%, rgba(255, 255, 255, 0.4)),
            color-stop(50%, rgba(255, 255, 255, 0.2)),
            color-stop(51%, rgba(255, 255, 255, 0.1)),
            color-stop(100%, rgba(255, 255, 255, 0.0)));

    /* text */
    color: #121517;
    font-size: 12px;
    font-weight: bold;
    text-shadow: 0 1px 1px #e8ebee;
}

form label{
    white-space: nowrap;
    width: 160px;
}

.table_sticky-header {
    position: sticky;
    top: 0; /* 表头将粘在顶部 */
    background-color: #f1f1f1; /* 可以根据需求设置背景颜色 */
}