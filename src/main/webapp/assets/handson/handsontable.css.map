{"version": 3, "sources": ["webpack://Handsontable/./src/css/bootstrap.css", "webpack://Handsontable/./src/3rdparty/walkontable/css/walkontable.css", "webpack://Handsontable/./src/css/handsontable.css", "webpack://Handsontable/./src/css/mobile.handsontable.css", "webpack://Handsontable/./src/plugins/comments/comments.css", "webpack://Handsontable/./src/plugins/contextMenu/contextMenu.css", "webpack://Handsontable/./src/plugins/copyPaste/copyPaste.css", "webpack://Handsontable/./src/plugins/manualColumnFreeze/manualColumnFreeze.css", "webpack://Handsontable/./src/plugins/manualColumnMove/manualColumnMove.css", "webpack://Handsontable/./src/plugins/manualRowMove/manualRowMove.css", "webpack://Handsontable/./src/plugins/mergeCells/mergeCells.css", "webpack://Handsontable/./src/plugins/multiColumnSorting/multiColumnSorting.css", "webpack://Handsontable/./src/plugins/dropdownMenu/dropdownMenu.css", "webpack://Handsontable/./src/plugins/filters/filters.css", "webpack://Handsontable/./src/plugins/nestedHeaders/nestedHeaders.css", "webpack://Handsontable/./src/plugins/nestedRows/nestedRows.css", "webpack://Handsontable/./src/plugins/hiddenColumns/hiddenColumns.css", "webpack://Handsontable/./src/plugins/hiddenRows/hiddenRows.css"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AChEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,WAAW;AACX;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AC/PA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,uCAAuC;AACvC;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA,mBAAmB;AACnB;;AAEA,oBAAoB;AACpB;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,cAAc,iCAAiC;AAC/C,wCAAwC;AACxC;;AAEA;AACA,gBAAgB,iCAAiC;AACjD,wCAAwC;AACxC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AC7ZA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,qBAAqB;AACrB,sBAAsB;AACtB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,qBAAqB;AACrB,sBAAsB;AACtB;AACA;AACA;AACA;;AAEA;AACA,kCAAkC;AAClC;AACA;AACA;AACA,qBAAqB;AACrB,sBAAsB;AACtB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,qBAAqB;AACrB,sBAAsB;AACtB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;ACtNA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;ACtCA;AACA;AACA;;AAEA;AACA;AACA;AACA,gBAAgB;AAChB;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;ACrFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;ACPA;AACA;AACA;AACA;;ACHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AC1CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AC1CA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;ACtCA;AACA;AACA;AACA;AACA,mBAAmB;AACnB;;AAEA;AACA;AACA,oBAAoB,EAAE;;AAEtB;AACA;AACA;AACA,6BAA6B;AAC7B;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,0FAA0F;AAC1F;AACA;AACA;;AC3DA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,gBAAgB;AAChB;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;ACxGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AC3RA;AACA;AACA;;ACFA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;ACtDA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AC1BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "handsontable.css", "sourcesContent": ["/**\n * Fix for bootstrap styles\n */\n.handsontable .table th, .handsontable .table td {\n  border-top: none;\n}\n\n.handsontable tr {\n  background: #fff;\n}\n\n.handsontable td {\n  background-color: inherit;\n}\n\n.handsontable .table caption + thead tr:first-child th,\n.handsontable .table caption + thead tr:first-child td,\n.handsontable .table colgroup + thead tr:first-child th,\n.handsontable .table colgroup + thead tr:first-child td,\n.handsontable .table thead:first-child tr:first-child th,\n.handsontable .table thead:first-child tr:first-child td {\n  border-top: 1px solid #CCCCCC;\n}\n\n/* table-bordered */\n.handsontable .table-bordered {\n  border: 0;\n  border-collapse: separate;\n}\n\n.handsontable .table-bordered th,\n.handsontable .table-bordered td {\n  border-left: none;\n}\n\n.handsontable .table-bordered th:first-child,\n.handsontable .table-bordered td:first-child {\n  border-left: 1px solid #CCCCCC;\n}\n\n.handsontable .table > tbody > tr > td,\n.handsontable .table > tbody > tr > th,\n.handsontable .table > tfoot > tr > td,\n.handsontable .table > tfoot > tr > th,\n.handsontable .table > thead > tr > td,\n.handsontable .table > thead > tr > th {\n  line-height: 21px;\n  padding: 0 4px;\n}\n\n.col-lg-1.handsontable, .col-lg-10.handsontable, .col-lg-11.handsontable, .col-lg-12.handsontable,\n.col-lg-2.handsontable, .col-lg-3.handsontable, .col-lg-4.handsontable, .col-lg-5.handsontable, .col-lg-6.handsontable, .col-lg-7.handsontable, .col-lg-8.handsontable, .col-lg-9.handsontable,\n.col-md-1.handsontable, .col-md-10.handsontable, .col-md-11.handsontable, .col-md-12.handsontable,\n.col-md-2.handsontable, .col-md-3.handsontable, .col-md-4.handsontable, .col-md-5.handsontable, .col-md-6.handsontable, .col-md-7.handsontable, .col-md-8.handsontable, .col-md-9.handsontable\n.col-sm-1.handsontable, .col-sm-10.handsontable, .col-sm-11.handsontable, .col-sm-12.handsontable,\n.col-sm-2.handsontable, .col-sm-3.handsontable, .col-sm-4.handsontable, .col-sm-5.handsontable, .col-sm-6.handsontable, .col-sm-7.handsontable, .col-sm-8.handsontable, .col-sm-9.handsontable\n.col-xs-1.handsontable, .col-xs-10.handsontable, .col-xs-11.handsontable, .col-xs-12.handsontable,\n.col-xs-2.handsontable, .col-xs-3.handsontable, .col-xs-4.handsontable, .col-xs-5.handsontable, .col-xs-6.handsontable, .col-xs-7.handsontable, .col-xs-8.handsontable, .col-xs-9.handsontable {\n  padding-left: 0;\n  padding-right: 0;\n}\n\n.handsontable .table-striped > tbody > tr:nth-of-type(even) {\n  background-color: #FFF;\n}\n", ".handsontable {\n  position: relative;\n}\n\n.handsontable .hide {\n  display: none;\n}\n\n.handsontable .relative {\n  position: relative;\n}\n\n.handsontable .wtHider {\n  width: 0;\n}\n\n.handsontable .wtSpreader {\n  position: relative;\n  width: 0; /*must be 0, otherwise blank space appears in scroll demo after scrolling max to the right */\n  height: auto;\n}\n\n.handsontable table,\n.handsontable tbody,\n.handsontable thead,\n.handsontable td,\n.handsontable th,\n.handsontable input,\n.handsontable textarea,\n.handsontable div {\n  box-sizing: content-box;\n  -webkit-box-sizing: content-box;\n  -moz-box-sizing: content-box;\n}\n\n.handsontable input,\n.handsontable textarea {\n  min-height: initial;\n}\n\n.handsontable table.htCore {\n  border-collapse: separate;\n  /* it must be separate, otherwise there are offset miscalculations in WebKit: http://stackoverflow.com/questions/2655987/border-collapse-differences-in-ff-and-webkit */\n  /* this actually only changes appearance of user selection - does not make text unselectable */\n  /* -webkit-user-select: none;\n    -khtml-user-select: none;\n    -moz-user-select: none;\n    -o-user-select: none;\n    -ms-user-select: none;\n    user-select: none; */ /* no browser supports unprefixed version */\n  border-spacing: 0;\n  margin: 0;\n  border-width: 0;\n  table-layout: fixed;\n  width: 0;\n  outline-width: 0;\n  cursor: default;\n  /* reset bootstrap table style. for more info see: https://github.com/handsontable/handsontable/issues/224 */\n  max-width: none;\n  max-height: none;\n}\n\n.handsontable col {\n  width: 50px;\n}\n\n.handsontable col.rowHeader {\n  width: 50px;\n}\n\n.handsontable th,\n.handsontable td {\n  border-top-width: 0;\n  border-left-width: 0;\n  border-right: 1px solid #ccc;\n  border-bottom: 1px solid #ccc;\n  height: 22px;\n  empty-cells: show;\n  line-height: 21px;\n  padding: 0 4px 0 4px;\n  /* top, bottom padding different than 0 is handled poorly by FF with HTML5 doctype */\n  background-color: #fff;\n  vertical-align: top;\n  overflow: hidden;\n  outline-width: 0;\n  white-space: pre-line;\n  /* preserve new line character in cell */\n}\n\n.handsontable th:last-child {\n  /*Foundation framework fix*/\n  border-right: 1px solid #ccc;\n  border-bottom: 1px solid #ccc;\n}\n\n.handsontable th:first-child,\n.handsontable th:nth-child(2),\n.handsontable td:first-of-type {\n  border-left: 1px solid #ccc;\n}\n\n.handsontable.htRowHeaders thead tr th:nth-child(2) {\n  border-left: 1px solid #ccc;\n}\n\n.handsontable tr:first-child th,\n.handsontable tr:first-child td {\n  border-top: 1px solid #ccc;\n}\n\n.ht_master:not(.innerBorderLeft):not(.emptyColumns) ~ .handsontable tbody tr th,\n.ht_master:not(.innerBorderLeft):not(.emptyColumns) ~ .handsontable:not(.ht_clone_top) thead tr th:first-child {\n  border-right-width: 0;\n}\n\n/*\ninnerBorderTop - Property controlled by top overlay\ninnerBorderBottom - Property controlled by bottom overlay\n */\n.ht_master:not(.innerBorderTop):not(.innerBorderBottom) thead tr:last-child th,\n.ht_master:not(.innerBorderTop):not(.innerBorderBottom) ~ .handsontable thead tr:last-child th,\n.ht_master:not(.innerBorderTop):not(.innerBorderBottom) thead tr.lastChild th,\n.ht_master:not(.innerBorderTop):not(.innerBorderBottom) ~ .handsontable thead tr.lastChild th {\n  border-bottom-width: 0;\n}\n\n.handsontable th {\n  background-color: #f0f0f0;\n  color: #222;\n  text-align: center;\n  font-weight: normal;\n  white-space: nowrap;\n}\n\n.handsontable thead th {\n  padding: 0;\n}\n\n.handsontable th.active {\n  background-color: #ccc;\n}\n.handsontable thead th .relative {\n  padding: 2px 4px;\n}\n\n.handsontable span.colHeader {\n  display: inline-block;\n  line-height: 1.1;\n}\n\n/* Selection */\n.handsontable .wtBorder {\n  position: absolute;\n  font-size: 0;\n}\n.handsontable .wtBorder.hidden {\n  display: none !important;\n}\n\n/* A layer order of the selection types */\n.handsontable .wtBorder.current {\n  z-index: 10;\n}\n.handsontable .wtBorder.area {\n  z-index: 8;\n}\n.handsontable .wtBorder.fill {\n  z-index: 6;\n}\n\n/* fill handle */\n\n.handsontable .wtBorder.corner {\n  font-size: 0;\n  cursor: crosshair;\n}\n\n.ht_clone_master {\n  z-index: 100;\n}\n\n.ht_clone_right {\n  z-index: 110;\n}\n\n.ht_clone_left {\n  z-index: 120;\n}\n\n.ht_clone_bottom {\n  z-index: 130;\n}\n\n.ht_clone_bottom_right_corner {\n  z-index: 140;\n}\n\n.ht_clone_bottom_left_corner {\n  z-index: 150;\n}\n\n.ht_clone_top {\n  z-index: 160;\n}\n\n.ht_clone_top_right_corner {\n  z-index: 170;\n}\n\n.ht_clone_top_left_corner {\n  z-index: 180;\n}\n\n/*\n  Cell borders\n  */\n.handsontable tbody tr th:nth-last-child(2) {\n  border-right: 1px solid #ccc;\n}\n\n.ht_clone_top_left_corner thead tr th:nth-last-child(2) {\n  border-right: 1px solid #ccc;\n}\n\n.handsontable col.hidden {\n  width: 0 !important;\n}\n\n.handsontable tr.hidden,\n.handsontable tr.hidden td,\n.handsontable tr.hidden th {\n  display: none;\n}\n\n.ht_master,\n.ht_clone_left,\n.ht_clone_top,\n.ht_clone_bottom {\n  overflow: hidden;\n}\n\n.ht_master .wtHolder {\n  overflow: auto;\n}\n\n.handsontable .ht_master thead,\n.handsontable .ht_master tr th,\n.handsontable .ht_clone_left thead {\n  visibility: hidden;\n}\n\n.ht_clone_top .wtHolder,\n.ht_clone_left .wtHolder,\n.ht_clone_bottom .wtHolder {\n  overflow: hidden;\n}\n", ".handsontable.htAutoSize {\n  visibility: hidden;\n  left: -99000px;\n  position: absolute;\n  top: -99000px;\n}\n\n.handsontable td.htInvalid {\n  background-color: #ff4c42 !important; /*gives priority over td.area selection background*/\n}\n\n.handsontable td.htNoWrap {\n  white-space: nowrap;\n}\n\n#hot-display-license-info {\n  font-size: 10px;\n  color: #323232 ;\n  padding: 5px 0 3px 0;\n  font-family: Helvetica, Arial, sans-serif;\n  text-align: left;\n}\n\n#hot-display-license-info a {\n  font-size: 10px;\n}\n\n/* plugins */\n\n/* row + column resizer*/\n.handsontable .manualColumnResizer {\n  position: absolute;\n  top: 0;\n  cursor: col-resize;\n  z-index: 210;\n  width: 5px;\n  height: 25px;\n}\n\n.handsontable .manualRowResizer {\n  position: absolute;\n  left: 0;\n  cursor: row-resize;\n  z-index: 210;\n  height: 5px;\n  width: 50px;\n}\n\n.handsontable .manualColumnResizer:hover,\n.handsontable .manualColumnResizer.active,\n.handsontable .manualRowResizer:hover,\n.handsontable .manualRowResizer.active {\n  background-color: #34a9db;\n}\n\n.handsontable .manualColumnResizerGuide {\n  position: absolute;\n  right: 0;\n  top: 0;\n  background-color: #34a9db;\n  display: none;\n  width: 0;\n  border-right: 1px dashed #777;\n  margin-left: 5px;\n}\n\n.handsontable .manualRowResizerGuide {\n  position: absolute;\n  left: 0;\n  bottom: 0;\n  background-color: #34a9db;\n  display: none;\n  height: 0;\n  border-bottom: 1px dashed #777;\n  margin-top: 5px;\n}\n\n.handsontable .manualColumnResizerGuide.active,\n.handsontable .manualRowResizerGuide.active {\n  display: block;\n  z-index: 209;\n}\n\n.handsontable .columnSorting {\n  position: relative;\n}\n\n.handsontable .columnSorting.sortAction:hover {\n  text-decoration: underline;\n  cursor: pointer;\n}/* Arrow position */\n.handsontable span.colHeader.columnSorting::before {\n  /* Centering start */\n  top: 50%;\n  margin-top: -6px; /* One extra pixel for purpose of proper positioning of sorting arrow, when `font-size` set to default */\n  /* Centering end */\n\n  padding-left: 8px; /* For purpose of continuous mouse over experience, when moving between the `span` and the `::before` elements */\n  position: absolute;\n  right: -9px;\n\n  content: '';\n  height: 10px;\n  width: 5px;\n  background-size: contain;\n  background-repeat: no-repeat;\n  background-position-x: right;\n}\n\n.handsontable span.colHeader.columnSorting.ascending::before {\n  /* arrow up; 20 x 40 px, scaled to 5 x 10 px; base64 size: 0.3kB */\n  background-image: url(\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAoCAMAAADJ7yrpAAAAKlBMVEUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAKE86IAAAADXRSTlMABBEmRGprlJW72e77tTkTKwAAAFNJREFUeAHtzjkSgCAUBNHPgsoy97+ulGXRqJE5L+xkxoYt2UdsLb5bqFINz+aLuuLn5rIu2RkO3fZpWENimNgiw6iBYRTPMLJjGFxQZ1hxxb/xBI1qC8k39CdKAAAAAElFTkSuQmCC\");\n}\n\n.handsontable span.colHeader.columnSorting.descending::before {\n  /* arrow down; 20 x 40 px, scaled to 5 x 10 px; base64 size: 0.3kB */\n  background-image: url(\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAoCAMAAADJ7yrpAAAAKlBMVEUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAKE86IAAAADXRSTlMABBEmRGprlJW72e77tTkTKwAAAFJJREFUeAHtzjkSgCAQRNFmQYUZ7n9dKUvru0TmvPAn3br0QfgdZ5xx6x+rQn23GqTYnq1FDcnuzZIO2WmedVqIRVxgGKEyjNgYRjKGkZ1hFIZ3I70LyM0VtU8AAAAASUVORK5CYII=\");\n}\n\n.htGhostTable .htCore span.colHeader.columnSorting:not(.indicatorDisabled)::after {\n  content: '*';\n  display: inline-block;\n  position: relative;\n  /* The multi-line header and header with longer text need more padding to not hide arrow,\n  we make header wider in `GhostTable` to make some space for arrow which is positioned absolutely in the main table */\n  padding-right: 20px;\n}\n\n.handsontable td.area,\n.handsontable td.area-1,\n.handsontable td.area-2,\n.handsontable td.area-3,\n.handsontable td.area-4,\n.handsontable td.area-5,\n.handsontable td.area-6,\n.handsontable td.area-7 {\n  position: relative;\n}\n\n.handsontable td.area:before,\n.handsontable td.area-1:before,\n.handsontable td.area-2:before,\n.handsontable td.area-3:before,\n.handsontable td.area-4:before,\n.handsontable td.area-5:before,\n.handsontable td.area-6:before,\n.handsontable td.area-7:before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  bottom: -100%\\9; /* Fix for IE9 to spread the \":before\" pseudo element to 100% height of the parent element */\n  background: #005eff;\n}\n\n/* Fix for IE10 and IE11 to spread the \":before\" pseudo element to 100% height of the parent element */\n@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {\n  .handsontable td.area:before,\n  .handsontable td.area-1:before,\n  .handsontable td.area-2:before,\n  .handsontable td.area-3:before,\n  .handsontable td.area-4:before,\n  .handsontable td.area-5:before,\n  .handsontable td.area-6:before,\n  .handsontable td.area-7:before {\n    bottom: -100%;\n  }\n}\n\n.handsontable td.area:before {\n  opacity: 0.1;\n}\n.handsontable td.area-1:before {\n  opacity: 0.2;\n}\n.handsontable td.area-2:before {\n  opacity: 0.27;\n}\n.handsontable td.area-3:before {\n  opacity: 0.35;\n}\n.handsontable td.area-4:before {\n  opacity: 0.41;\n}\n.handsontable td.area-5:before {\n  opacity: 0.47;\n}\n.handsontable td.area-6:before {\n  opacity: 0.54;\n}\n.handsontable td.area-7:before {\n  opacity: 0.58;\n}\n\n.handsontable tbody th.ht__highlight,\n.handsontable thead th.ht__highlight {\n  background-color: #dcdcdc;\n}\n\n.handsontable tbody th.ht__active_highlight,\n.handsontable thead th.ht__active_highlight {\n  background-color: #8eb0e7;\n  color: #000;\n}\n\n.handsontableInput {\n  border: none;\n  outline-width: 0;\n  margin: 0;\n  padding: 1px 5px 0 5px;\n  font-family: inherit;\n  line-height: 21px;\n  font-size: inherit;\n  box-shadow: 0 0 0 2px #5292F7 inset;\n  resize: none;\n  /*below are needed to overwrite stuff added by jQuery UI Bootstrap theme*/\n  display: block;\n  color: #000;\n  border-radius: 0;\n  background-color: #FFF;\n  /*overwrite styles potentionally made by a framework*/\n}\n\n.handsontableInput:focus {\n  outline: none;\n}\n\n.handsontableInputHolder {\n  position: absolute;\n  top: 0;\n  left: 0;\n}\n\n.htSelectEditor {\n  -webkit-appearance: menulist-button !important;\n  position: absolute;\n  width: auto;\n}\n\n.htSelectEditor:focus {\n  outline: none;\n}\n\n/*\nTextRenderer readOnly cell\n*/\n\n.handsontable .htDimmed {\n  color: #777;\n}\n\n.handsontable .htSubmenu {\n  position: relative;\n}\n\n.handsontable .htSubmenu :after{\n  content: '\\25B6';\n  color: #777;\n  position: absolute;\n  right: 5px;\n  font-size: 9px;\n}\n\n\n/*\nTextRenderer horizontal alignment\n*/\n.handsontable .htLeft{\n  text-align: left;\n}\n.handsontable .htCenter{\n  text-align: center;\n}\n.handsontable .htRight{\n  text-align: right;\n}\n.handsontable .htJustify{\n  text-align: justify;\n}\n/*\nTextRenderer vertical alignment\n*/\n.handsontable .htTop{\n  vertical-align: top;\n}\n.handsontable .htMiddle{\n  vertical-align: middle;\n}\n.handsontable .htBottom{\n  vertical-align: bottom;\n}\n\n/*\nTextRenderer placeholder value\n*/\n\n.handsontable .htPlaceholder {\n  color: #999;\n}\n\n/*\nAutocompleteRenderer down arrow\n*/\n\n.handsontable .htAutocompleteArrow {\n  float: right;\n  font-size: 10px;\n  color: #EEE;\n  cursor: default;\n  width: 16px;\n  text-align: center;\n}\n\n.handsontable td .htAutocompleteArrow:hover {\n  color: #777;\n}\n\n.handsontable td.area .htAutocompleteArrow {\n  color: #d3d3d3;\n}\n\n/*\nCheckboxRenderer\n*/\n.handsontable .htCheckboxRendererInput {\n  display: inline-block;\n}\n.handsontable .htCheckboxRendererInput.noValue {\n  opacity: 0.5;\n}\n.handsontable .htCheckboxRendererLabel {\n  font-size: inherit;\n  vertical-align: middle;\n  cursor: pointer;\n  display: inline-block;\n  width: 100%;\n}\n\n/**\n * Handsontable listbox theme\n */\n\n.handsontable.listbox {\n  margin: 0;\n}\n\n.handsontable.listbox .ht_master table {\n  border: 1px solid #ccc;\n  border-collapse: separate;\n  background: white;\n}\n\n.handsontable.listbox th,\n.handsontable.listbox tr:first-child th,\n.handsontable.listbox tr:last-child th,\n.handsontable.listbox tr:first-child td,\n.handsontable.listbox td {\n  border-color: transparent;\n}\n\n.handsontable.listbox th,\n.handsontable.listbox td {\n  white-space: nowrap;\n  text-overflow: ellipsis;\n}\n\n.handsontable.listbox td.htDimmed {\n  cursor: default;\n  color: inherit;\n  font-style: inherit;\n}\n\n.handsontable.listbox .wtBorder {\n  visibility: hidden;\n}\n\n.handsontable.listbox tr td.current,\n.handsontable.listbox tr:hover td {\n  background: #eee;\n}\n\n.ht_editor_hidden {\n  z-index: -1;\n}\n\n.ht_editor_visible {\n  z-index: 200;\n}\n\n.handsontable td.htSearchResult {\n  background: #fcedd9;\n  color: #583707;\n}\n\n.collapsibleIndicator {\n  position: absolute;\n  top: 50%;\n  transform: translate(0% ,-50%);\n  right: 5px;\n  border: 1px solid #A6A6A6;\n  line-height: 10px;\n  color: #222;\n  border-radius: 10px;\n  font-size: 10px;\n  width: 10px;\n  height: 10px;\n  cursor: pointer;\n  -webkit-box-shadow: 0 0 0 6px rgba(238,238,238,1);\n  -moz-box-shadow: 0 0 0 6px rgba(238,238,238,1);\n  box-shadow: 0 0 0 6px rgba(238,238,238,1);\n  background: #eee;\n}\n", "/*\n\n Handsontable Mobile Text Editor stylesheet\n\n */\n\n.handsontable.mobile,\n.handsontable.mobile .wtHolder {\n  -webkit-touch-callout:none;\n  -webkit-user-select:none;\n  -khtml-user-select:none;\n  -moz-user-select:none;\n  -ms-user-select:none;\n  user-select:none;\n  -webkit-tap-highlight-color:rgba(0,0,0,0);\n  -webkit-overflow-scrolling: touch;\n}\n\n.htMobileEditorContainer {\n  display: none;\n  position: absolute;\n  top: 0;\n  width: 70%;\n  height: 54pt;\n  background: #f8f8f8;\n  border-radius: 20px;\n  border: 1px solid #ebebeb;\n  z-index: 999;\n  box-sizing: border-box;\n  -webkit-box-sizing: border-box;\n  -webkit-text-size-adjust: none;\n}\n\n.topLeftSelectionHandle:not(.ht_master .topLeftSelectionHandle),\n.topLeftSelectionHandle-HitArea:not(.ht_master .topLeftSelectionHandle-HitArea) {\n  z-index: 9999;\n}\n\n/* Initial left/top coordinates - overwritten when actual position is set */\n.topLeftSelectionHandle,\n.topLeftSelectionHandle-HitArea,\n.bottomRightSelectionHandle,\n.bottomRightSelectionHandle-HitArea {\n  left: -10000px;\n  top: -10000px;\n}\n\n.htMobileEditorContainer.active {\n  display: block;\n}\n\n.htMobileEditorContainer .inputs {\n  position: absolute;\n  right: 210pt;\n  bottom: 10pt;\n  top: 10pt;\n  left: 14px;\n  height: 34pt;\n}\n\n.htMobileEditorContainer .inputs textarea {\n  font-size: 13pt;\n  border: 1px solid #a1a1a1;\n  -webkit-appearance: none;\n  -webkit-box-shadow: none;\n  -moz-box-shadow: none;\n  box-shadow: none;\n  position: absolute;\n  left: 14px;\n  right: 14px;\n  top: 0;\n  bottom: 0;\n  padding: 7pt;\n}\n\n.htMobileEditorContainer .cellPointer {\n  position: absolute;\n  top: -13pt;\n  height: 0;\n  width: 0;\n  left: 30px;\n\n  border-left: 13pt solid transparent;\n  border-right: 13pt solid transparent;\n  border-bottom: 13pt solid #ebebeb;\n}\n\n.htMobileEditorContainer .cellPointer.hidden {\n  display: none;\n}\n\n.htMobileEditorContainer .cellPointer:before {\n  content: '';\n  display: block;\n  position: absolute;\n  top: 2px;\n  height: 0;\n  width: 0;\n  left: -13pt;\n\n  border-left: 13pt solid transparent;\n  border-right: 13pt solid transparent;\n  border-bottom: 13pt solid #f8f8f8;\n}\n\n.htMobileEditorContainer .moveHandle {\n  position: absolute;\n  top: 10pt;\n  left: 5px;\n  width: 30px;\n  bottom: 0px;\n  cursor: move;\n  z-index: 9999;\n}\n\n.htMobileEditorContainer .moveHandle:after {\n  content: \"..\\a..\\a..\\a..\";\n  white-space: pre;\n  line-height: 10px;\n  font-size: 20pt;\n  display: inline-block;\n  margin-top: -8px;\n  color: #ebebeb;\n}\n\n.htMobileEditorContainer .positionControls {\n  width: 205pt;\n  position: absolute;\n  right: 5pt;\n  top: 0;\n  bottom: 0;\n}\n\n.htMobileEditorContainer .positionControls > div {\n  width: 50pt;\n  height: 100%;\n  float: left;\n}\n\n.htMobileEditorContainer .positionControls > div:after {\n  content: \" \";\n  display: block;\n  width: 15pt;\n  height: 15pt;\n  text-align: center;\n  line-height: 50pt;\n}\n\n.htMobileEditorContainer .leftButton:after,\n.htMobileEditorContainer .rightButton:after,\n.htMobileEditorContainer .upButton:after,\n.htMobileEditorContainer .downButton:after {\n  transform-origin: 5pt 5pt;\n  -webkit-transform-origin: 5pt 5pt;\n  margin: 21pt 0 0 21pt;\n}\n\n.htMobileEditorContainer .leftButton:after {\n  border-top: 2px solid #288ffe;\n  border-left: 2px solid #288ffe;\n  -webkit-transform: rotate(-45deg);\n  /*margin-top: 17pt;*/\n  /*margin-left: 20pt;*/\n}\n.htMobileEditorContainer .leftButton:active:after {\n  border-color: #cfcfcf;\n}\n\n.htMobileEditorContainer .rightButton:after {\n  border-top: 2px solid #288ffe;\n  border-left: 2px solid #288ffe;\n  -webkit-transform: rotate(135deg);\n  /*margin-top: 17pt;*/\n  /*margin-left: 10pt;*/\n}\n.htMobileEditorContainer .rightButton:active:after {\n  border-color: #cfcfcf;\n}\n\n.htMobileEditorContainer .upButton:after {\n  /*border-top: 2px solid #cfcfcf;*/\n  border-top: 2px solid #288ffe;\n  border-left: 2px solid #288ffe;\n  -webkit-transform: rotate(45deg);\n  /*margin-top: 22pt;*/\n  /*margin-left: 15pt;*/\n}\n.htMobileEditorContainer .upButton:active:after {\n  border-color: #cfcfcf;\n}\n\n.htMobileEditorContainer .downButton:after {\n  border-top: 2px solid #288ffe;\n  border-left: 2px solid #288ffe;\n  -webkit-transform: rotate(225deg);\n  /*margin-top: 15pt;*/\n  /*margin-left: 15pt;*/\n}\n.htMobileEditorContainer .downButton:active:after {\n  border-color: #cfcfcf;\n}\n\n.handsontable.hide-tween {\n  -webkit-animation: opacity-hide 0.3s;\n  animation: opacity-hide 0.3s;\n  animation-fill-mode: forwards;\n  -webkit-animation-fill-mode: forwards;\n}\n\n.handsontable.show-tween {\n  -webkit-animation: opacity-show 0.3s;\n  animation: opacity-show 0.3s;\n  animation-fill-mode: forwards;\n  -webkit-animation-fill-mode: forwards;\n}\n", ".htCommentCell {\n  position: relative;\n}\n\n.htCommentCell:after {\n  content: '';\n  position: absolute;\n  top: 0;\n  right: 0;\n  border-left: 6px solid transparent;\n  border-top: 6px solid black;\n}\n\n.htComments {\n  display: none;\n  z-index: 1059;\n  position: absolute;\n}\n\n.htCommentTextArea {\n  box-shadow: rgba(0, 0, 0, 0.117647) 0 1px 3px, rgba(0, 0, 0, 0.239216) 0 1px 2px;\n  -webkit-box-sizing: border-box;\n  -moz-box-sizing: border-box;\n  box-sizing: border-box;\n  border: none;\n  border-left: 3px solid #ccc;\n  background-color: #fff;\n  width: 215px;\n  height: 90px;\n  font-size: 12px;\n  padding: 5px;\n  outline: 0px !important;\n  -webkit-appearance: none;\n}\n\n.htCommentTextArea:focus {\n  box-shadow: rgba(0, 0, 0, 0.117647) 0 1px 3px, rgba(0, 0, 0, 0.239216) 0 1px 2px, inset 0 0 0 1px #5292f7;\n  border-left: 3px solid #5292f7;\n}\n", "/*!\n * Handsontable ContextMenu\n */\n\n.htContextMenu:not(.htGhostTable) {\n  display: none;\n  position: absolute;\n  z-index: 1060; /* needs to be higher than 1050 - z-index for Twitter Bootstrap modal (#1569) */\n}\n\n.htContextMenu .ht_clone_top,\n.htContextMenu .ht_clone_left,\n.htContextMenu .ht_clone_corner {\n  display: none;\n}\n\n.htContextMenu .ht_master table.htCore {\n  border: 1px solid #ccc;\n  border-bottom-width: 2px;\n  border-right-width: 2px;\n}\n\n.htContextMenu .wtBorder {\n  visibility: hidden;\n}\n\n.htContextMenu table tbody tr td {\n  background: white;\n  border-width: 0;\n  padding: 4px 6px 0 6px;\n  cursor: pointer;\n  overflow: hidden;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n}\n\n.htContextMenu table tbody tr td:first-child {\n  border: 0;\n}\n\n.htContextMenu table tbody tr td.htDimmed {\n  font-style: normal;\n  color: #323232;\n}\n\n.htContextMenu table tbody tr td.current,\n.htContextMenu table tbody tr td.zeroclipboard-is-hover {\n  background: #f3f3f3;\n}\n\n.htContextMenu table tbody tr td.htSeparator {\n  border-top: 1px solid #e6e6e6;\n  height: 0;\n  padding: 0;\n  cursor: default;\n}\n\n.htContextMenu table tbody tr td.htDisabled {\n  color: #999;\n  cursor: default;\n}\n\n.htContextMenu table tbody tr td.htDisabled:hover {\n  background: #fff;\n  color: #999;\n  cursor: default;\n}\n\n.htContextMenu table tbody tr.htHidden {\n  display: none;\n}\n\n.htContextMenu table tbody tr td .htItemWrapper {\n  margin-left: 10px;\n  margin-right: 6px;\n}\n\n.htContextMenu table tbody tr td div span.selected {\n  margin-top: -2px;\n  position: absolute;\n  left: 4px;\n}\n\n.htContextMenu .ht_master .wtHolder {\n  overflow: hidden;\n}\n", "textarea.HandsontableCopyPaste {\n  position: fixed !important;\n  top: 0 !important;\n  right: 100% !important;\n  overflow: hidden;\n  opacity: 0;\n  outline: 0 none !important;\n}\n", ".htRowHeaders .ht_master.innerBorderLeft ~ .ht_clone_top_left_corner th:nth-child(2),\n.htRowHeaders .ht_master.innerBorderLeft ~ .ht_clone_left td:first-of-type {\n  border-left: 0 none;\n}\n", ".handsontable .wtHider {\n  position: relative;\n}\n.handsontable.ht__manualColumnMove.after-selection--columns thead th.ht__highlight {\n  cursor: move;\n  cursor: -moz-grab;\n  cursor: -webkit-grab;\n  cursor: grab;\n}\n.handsontable.ht__manualColumnMove.on-moving--columns,\n.handsontable.ht__manualColumnMove.on-moving--columns thead th.ht__highlight {\n  cursor: move;\n  cursor: -moz-grabbing;\n  cursor: -webkit-grabbing;\n  cursor: grabbing;\n}\n.handsontable.ht__manualColumnMove.on-moving--columns .manualColumnResizer {\n  display: none;\n}\n.handsontable .ht__manualColumnMove--guideline,\n.handsontable .ht__manualColumnMove--backlight {\n  position: absolute;\n  height: 100%;\n  display: none;\n}\n.handsontable .ht__manualColumnMove--guideline {\n  background: #757575;\n  width: 2px;\n  top: 0;\n  margin-left: -1px;\n  z-index: 205;\n}\n.handsontable .ht__manualColumnMove--backlight {\n  background: #343434;\n  background: rgba(52, 52, 52, 0.25);\n  display: none;\n  z-index: 205;\n  pointer-events: none;\n}\n.handsontable.on-moving--columns.show-ui .ht__manualColumnMove--guideline,\n.handsontable.on-moving--columns .ht__manualColumnMove--backlight {\n  display: block;\n}\n", ".handsontable .wtHider {\n  position: relative;\n}\n.handsontable.ht__manualRowMove.after-selection--rows tbody th.ht__highlight {\n  cursor: move;\n  cursor: -moz-grab;\n  cursor: -webkit-grab;\n  cursor: grab;\n}\n.handsontable.ht__manualRowMove.on-moving--rows,\n.handsontable.ht__manualRowMove.on-moving--rows tbody th.ht__highlight {\n  cursor: move;\n  cursor: -moz-grabbing;\n  cursor: -webkit-grabbing;\n  cursor: grabbing;\n}\n.handsontable.ht__manualRowMove.on-moving--rows .manualRowResizer {\n  display: none;\n}\n.handsontable .ht__manualRowMove--guideline,\n.handsontable .ht__manualRowMove--backlight {\n  position: absolute;\n  width: 100%;\n  display: none;\n}\n.handsontable .ht__manualRowMove--guideline {\n  background: #757575;\n  height: 2px;\n  left: 0;\n  margin-top: -1px;\n  z-index: 205;\n}\n.handsontable .ht__manualRowMove--backlight {\n  background: #343434;\n  background: rgba(52, 52, 52, 0.25);\n  display: none;\n  z-index: 205;\n  pointer-events: none;\n}\n.handsontable.on-moving--rows.show-ui .ht__manualRowMove--guideline,\n.handsontable.on-moving--rows .ht__manualRowMove--backlight {\n  display: block;\n}\n", ".handsontable tbody td[rowspan][class*=\"area\"][class*=\"highlight\"]:not([class*=\"fullySelectedMergedCell\"]):before {\n  opacity: 0;\n}\n\n.handsontable tbody td[rowspan][class*=\"area\"][class*=\"highlight\"][class*=\"fullySelectedMergedCell-multiple\"]:before {\n  opacity: 0.1;\n}\n\n.handsontable tbody td[rowspan][class*=\"area\"][class*=\"highlight\"][class*=\"fullySelectedMergedCell-0\"]:before {\n  opacity: 0.1;\n}\n\n.handsontable tbody td[rowspan][class*=\"area\"][class*=\"highlight\"][class*=\"fullySelectedMergedCell-1\"]:before {\n  opacity: 0.2;\n}\n\n.handsontable tbody td[rowspan][class*=\"area\"][class*=\"highlight\"][class*=\"fullySelectedMergedCell-2\"]:before {\n  opacity: 0.27;\n}\n\n.handsontable tbody td[rowspan][class*=\"area\"][class*=\"highlight\"][class*=\"fullySelectedMergedCell-3\"]:before {\n  opacity: 0.35;\n}\n\n.handsontable tbody td[rowspan][class*=\"area\"][class*=\"highlight\"][class*=\"fullySelectedMergedCell-4\"]:before {\n  opacity: 0.41;\n}\n\n.handsontable tbody td[rowspan][class*=\"area\"][class*=\"highlight\"][class*=\"fullySelectedMergedCell-5\"]:before {\n  opacity: 0.47;\n}\n\n.handsontable tbody td[rowspan][class*=\"area\"][class*=\"highlight\"][class*=\"fullySelectedMergedCell-6\"]:before {\n  opacity: 0.54;\n}\n\n.handsontable tbody td[rowspan][class*=\"area\"][class*=\"highlight\"][class*=\"fullySelectedMergedCell-7\"]:before {\n  opacity: 0.58;\n}\n", "/* Column's number position */\n.handsontable span.colHeader.columnSorting::after {\n  /* Centering start */\n  top: 50%;\n  margin-top: -2px; /* Two extra pixels (-2 instead of -4) for purpose of proper positioning of numeric indicators, when `font-size` set to default */\n  /* Centering end */\n\n  position: absolute;\n  right: -15px;\n  padding-left: 5px; ; /* For purpose of continuous mouse over experience, when moving between the `::before` and the `::after` elements */\n\n  font-size: 8px;\n  height: 8px;\n  line-height: 1.1;\n  text-decoration: underline; /* Workaround for IE9 - IE11 */\n}\n\n/* Workaround for IE9 - IE11, https://stackoverflow.com/a/21902566, https://stackoverflow.com/a/32120247 */\n.handsontable span.colHeader.columnSorting::after {\n  text-decoration: none;\n}\n\n/* We support up to 7 numeric indicators, describing order of column in sorted columns queue */\n.handsontable span.colHeader.columnSorting[class^=\"sort-\"]::after,\n.handsontable span.colHeader.columnSorting[class*=\" sort-\"]::after {\n  content: \"+\"\n}\n\n.handsontable span.colHeader.columnSorting.sort-1::after {\n  content: '1';\n}\n\n.handsontable span.colHeader.columnSorting.sort-2::after {\n  content: '2';\n}\n\n.handsontable span.colHeader.columnSorting.sort-3::after {\n  content: '3';\n}\n\n.handsontable span.colHeader.columnSorting.sort-4::after {\n  content: '4';\n}\n\n.handsontable span.colHeader.columnSorting.sort-5::after {\n  content: '5';\n}\n\n.handsontable span.colHeader.columnSorting.sort-6::after {\n  content: '6';\n}\n\n.handsontable span.colHeader.columnSorting.sort-7::after {\n  content: '7';\n}\n\n/* Drop-down menu widens header by 5 pixels, sort sequence numbers won't overlap the icon; mainly for the IE9+ */\n.htGhostTable th div button.changeType + span.colHeader.columnSorting:not(.indicatorDisabled) {\n  padding-right: 5px;\n}\n", "/*!\n * Handsontable DropdownMenu\n */\n.handsontable .changeType {\n  background: #eee;\n  border-radius: 2px;\n  border: 1px solid #bbb;\n  color: #bbb;\n  font-size: 9px;\n  line-height: 9px;\n  padding: 2px;\n  margin: 3px 1px 0 5px;\n  float: right;\n}\n.handsontable .changeType:before {\n  content: '\\25BC\\ ';\n}\n\n.handsontable .changeType:hover {\n  border: 1px solid #777;\n  color: #777;\n  cursor: pointer;\n}\n\n.htDropdownMenu:not(.htGhostTable) {\n  display: none;\n  position: absolute;\n  z-index: 1060; /* needs to be higher than 1050 - z-index for Twitter Bootstrap modal (#1569) */\n}\n\n.htDropdownMenu .ht_clone_top,\n.htDropdownMenu .ht_clone_left,\n.htDropdownMenu .ht_clone_corner {\n  display: none;\n}\n\n.htDropdownMenu table.htCore {\n  border: 1px solid #bbb;\n  border-bottom-width: 2px;\n  border-right-width: 2px;\n}\n\n.htDropdownMenu .wtBorder {\n  visibility: hidden;\n}\n\n.htDropdownMenu table tbody tr td {\n  background: white;\n  border-width: 0;\n  padding: 4px 6px 0 6px;\n  cursor: pointer;\n  overflow: hidden;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n}\n\n.htDropdownMenu table tbody tr td:first-child {\n  border: 0;\n}\n\n.htDropdownMenu table tbody tr td.htDimmed {\n  font-style: normal;\n  color: #323232;\n}\n\n.htDropdownMenu table tbody tr td.current,\n.htDropdownMenu table tbody tr td.zeroclipboard-is-hover {\n  background: #e9e9e9;\n}\n\n.htDropdownMenu table tbody tr td.htSeparator {\n  border-top: 1px solid #e6e6e6;\n  height: 0;\n  padding: 0;\n  cursor: default;\n}\n\n.htDropdownMenu table tbody tr td.htDisabled {\n  color: #999;\n}\n\n.htDropdownMenu table tbody tr td.htDisabled:hover {\n  background: #fff;\n  color: #999;\n  cursor: default;\n}\n\n.htDropdownMenu:not(.htGhostTable) table tbody tr.htHidden {\n  display: none;\n}\n\n.htDropdownMenu table tbody tr td .htItemWrapper {\n  margin-left: 10px;\n  margin-right: 10px;\n}\n\n.htDropdownMenu table tbody tr td div span.selected {\n  margin-top: -2px;\n  position: absolute;\n  left: 4px;\n}\n\n.htDropdownMenu .ht_master .wtHolder {\n  overflow: hidden;\n}\n", "/*!\n * Handsontable Filters\n */\n\n/* Conditions menu */\n.htFiltersConditionsMenu:not(.htGhostTable) {\n  display: none;\n  position: absolute;\n  z-index: 1070;\n}\n\n.htFiltersConditionsMenu .ht_clone_top,\n.htFiltersConditionsMenu .ht_clone_left,\n.htFiltersConditionsMenu .ht_clone_corner {\n  display: none;\n}\n\n.htFiltersConditionsMenu table.htCore {\n  border: 1px solid #bbb;\n  border-bottom-width: 2px;\n  border-right-width: 2px;\n}\n\n.htFiltersConditionsMenu .wtBorder {\n  visibility: hidden;\n}\n\n.htFiltersConditionsMenu table tbody tr td {\n  background: white;\n  border-width: 0;\n  padding: 4px 6px 0 6px;\n  cursor: pointer;\n  overflow: hidden;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n}\n\n.htFiltersConditionsMenu table tbody tr td:first-child {\n  border: 0;\n}\n\n.htFiltersConditionsMenu table tbody tr td.htDimmed {\n  font-style: normal;\n  color: #323232;\n}\n\n.htFiltersConditionsMenu table tbody tr td.current,\n.htFiltersConditionsMenu table tbody tr td.zeroclipboard-is-hover {\n  background: #e9e9e9;\n}\n\n.htFiltersConditionsMenu table tbody tr td.htSeparator {\n  border-top: 1px solid #e6e6e6;\n  height: 0;\n  padding: 0;\n}\n\n.htFiltersConditionsMenu table tbody tr td.htDisabled {\n  color: #999;\n}\n\n.htFiltersConditionsMenu table tbody tr td.htDisabled:hover {\n  background: #fff;\n  color: #999;\n  cursor: default;\n}\n\n.htFiltersConditionsMenu table tbody tr td .htItemWrapper {\n  margin-left: 10px;\n  margin-right: 10px;\n}\n\n.htFiltersConditionsMenu table tbody tr td div span.selected {\n  margin-top: -2px;\n  position: absolute;\n  left: 4px;\n}\n\n.htFiltersConditionsMenu .ht_master .wtHolder {\n  overflow: hidden;\n}\n\n.handsontable .htMenuFiltering {\n  border-bottom: 1px dotted #ccc;\n  height: 135px;\n  overflow: hidden;\n}\n\n.handsontable .ht_master table td.htCustomMenuRenderer {\n  background-color: #fff;\n  cursor: auto;\n}\n\n/* Menu label */\n.handsontable .htFiltersMenuLabel {\n  font-size: 0.75em;\n}\n\n/* Component action bar */\n.handsontable .htFiltersMenuActionBar {\n  text-align: center;\n  padding-top: 10px;\n  padding-bottom: 3px;\n}\n\n/* Component filter by conditional */\n.handsontable .htFiltersMenuCondition.border {\n  border-bottom: 1px dotted #ccc !important;\n}\n.handsontable .htFiltersMenuCondition .htUIInput {\n  padding: 0 0 5px 0;\n}\n\n/* Component filter by value */\n.handsontable .htFiltersMenuValue {\n  border-bottom: 1px dotted #ccc !important;\n}\n.handsontable .htFiltersMenuValue .htUIMultipleSelectSearch {\n  padding: 0;\n}\n.handsontable .htFiltersMenuCondition .htUIInput input,\n.handsontable .htFiltersMenuValue .htUIMultipleSelectSearch input {\n  font-family: inherit;\n  font-size: 0.75em;\n  padding: 4px;\n  box-sizing: border-box;\n  width: 100%;\n}\n\n.htUIMultipleSelect .ht_master .wtHolder {\n  overflow-y: scroll;\n}\n\n.handsontable .htFiltersActive .changeType {\n  border: 1px solid #509272;\n  color: #18804e;\n  background-color: #d2e0d9;\n}\n\n.handsontable .htUISelectAll {\n  margin-right: 10px;\n}\n\n.handsontable .htUIClearAll, .handsontable .htUISelectAll {\n  display: inline-block;\n}\n\n.handsontable .htUIClearAll a, .handsontable .htUISelectAll a {\n  color: #3283D8;\n  font-size: 0.75em;\n}\n\n.handsontable .htUISelectionControls {\n  text-align: right;\n}\n\n.handsontable .htCheckboxRendererInput {\n  margin: 0 5px 0 0;\n  vertical-align: middle;\n  height: 1em;\n}\n\n/* UI elements */\n/* Input */\n.handsontable .htUIInput {\n  padding: 3px 0 7px 0;\n  position: relative;\n  text-align: center;\n}\n.handsontable .htUIInput input {\n  border-radius: 2px;\n  border: 1px solid #d2d1d1;\n}\n.handsontable .htUIInput input:focus {\n  outline: 0;\n}\n.handsontable .htUIInputIcon {\n  position: absolute;\n}\n\n/* Button */\n.handsontable .htUIInput.htUIButton {\n  cursor: pointer;\n  display: inline-block;\n}\n.handsontable .htUIInput.htUIButton input {\n  background-color: #eee;\n  color: #000;\n  cursor: pointer;\n  font-family: inherit;\n  font-size: 0.7em;\n  font-weight: bold;\n  height: 19px;\n  min-width: 64px;\n}\n.handsontable .htUIInput.htUIButton input:hover {\n  border-color: #b9b9b9;\n}\n\n.handsontable .htUIInput.htUIButtonOK {\n  margin-right: 10px;\n}\n\n.handsontable .htUIInput.htUIButtonOK input {\n  background-color: #0f9d58;\n  border-color: #18804e;\n  color: #fff;\n}\n.handsontable .htUIInput.htUIButtonOK input:hover {\n  border-color: #1a6f46;\n}\n\n/* Select */\n.handsontable .htUISelect {\n  cursor: pointer;\n  margin-bottom: 7px;\n  position: relative;\n}\n.handsontable .htUISelectCaption {\n  background-color: #e8e8e8;\n  border-radius: 2px;\n  border: 1px solid #d2d1d1;\n  font-family: inherit;\n  font-size: 0.7em;\n  font-weight: bold;\n  padding: 3px 20px 3px 10px;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  overflow: hidden;\n}\n.handsontable .htUISelectCaption:hover {\n  background-color: #e8e8e8;\n  border: 1px solid #b9b9b9;\n}\n.handsontable .htUISelectDropdown:after {\n  content: '\\25B2';\n  font-size: 7px;\n  position: absolute;\n  right: 10px;\n  top: 0;\n}\n.handsontable .htUISelectDropdown:before {\n  content: '\\25BC';\n  font-size: 7px;\n  position: absolute;\n  right: 10px;\n  top: 8px;\n}\n\n/* SelectMultiple */\n.handsontable .htUIMultipleSelect .handsontable .htCore {\n  border: none;\n}\n.handsontable .htUIMultipleSelect .handsontable .htCore td:hover {\n  background-color: #F5F5F5;\n}\n\n.handsontable .htUIMultipleSelectSearch input {\n  border-radius: 2px;\n  border: 1px solid #d2d1d1;\n  padding: 3px;\n}\n\n.handsontable .htUIRadio {\n  display: inline-block;\n  margin-right: 5px;\n  height: 100%;\n}\n\n.handsontable .htUIRadio:last-child {\n  margin-right: 0;\n}\n\n.handsontable .htUIRadio > input[type=radio] {\n  margin-right: 0.5ex;\n}\n\n.handsontable .htUIRadio label {\n  vertical-align: middle;\n}\n\n.handsontable .htFiltersMenuOperators {\n  padding-bottom: 5px;\n}\n", ".handsontable thead th.hiddenHeader:not(:first-of-type) {\n  display: none;\n}\n", ".handsontable th.ht_nestingLevels {\n  text-align: left;\n  padding-left: 7px;\n}\n\n.handsontable th div.ht_nestingLevels {\n  display: inline-block;\n  position: absolute;\n  left: 11px;\n}\n\n.handsontable.innerBorderLeft th div.ht_nestingLevels,\n.handsontable.innerBorderLeft ~ .handsontable th div.ht_nestingLevels {\n  right: 10px;\n}\n\n.handsontable th span.ht_nestingLevel {\n  display: inline-block;\n}\n\n.handsontable th span.ht_nestingLevel_empty {\n  display: inline-block;\n  width: 10px;\n  height: 1px;\n  float: left;\n}\n\n.handsontable th span.ht_nestingLevel::after {\n  content: \"\\2510\";\n  font-size: 9px;\n  display: inline-block;\n  position: relative;\n  bottom: 3px;\n}\n\n\n.handsontable th div.ht_nestingButton {\n  display: inline-block;\n  position: absolute;\n  right: -2px;\n  cursor: pointer;\n}\n\n.handsontable th div.ht_nestingButton.ht_nestingExpand::after {\n  content: \"\\002B\";\n}\n\n.handsontable th div.ht_nestingButton.ht_nestingCollapse::after {\n  content: \"\\002D\";\n}\n\n.handsontable.innerBorderLeft th div.ht_nestingButton,\n.handsontable.innerBorderLeft ~ .handsontable th div.ht_nestingButton {\n  right: 0;\n}\n", "/*\n * Handsontable HiddenColumns\n */\n.handsontable th.beforeHiddenColumn {\n  position: relative;\n}\n\n.handsontable th.beforeHiddenColumn::after,\n.handsontable th.afterHiddenColumn::before {\n  color: #bbb;\n  position: absolute;\n  top: 50%;\n  font-size: 5pt;\n  transform: translateY(-50%);\n}\n\n.handsontable th.afterHiddenColumn {\n  position: relative;\n}\n.handsontable th.beforeHiddenColumn::after {\n  right: 1px;\n  content: '\\25C0';\n}\n.handsontable th.afterHiddenColumn::before {\n  left: 1px;\n  content: '\\25B6';\n}\n", "/*!\n * Handsontable HiddenRows\n */\n.handsontable th.beforeHiddenRow::before,\n.handsontable th.afterHiddenRow::after {\n  color: #bbb;\n  font-size: 6pt;\n  line-height: 6pt;\n  position: absolute;\n  left: 2px;\n}\n\n.handsontable th.beforeHiddenRow,\n.handsontable th.afterHiddenRow {\n  position: relative;\n}\n\n.handsontable th.beforeHiddenRow::before {\n  content: '\\25B2';\n  bottom: 2px;\n}\n\n.handsontable th.afterHiddenRow::after {\n  content: '\\25BC';\n  top: 2px;\n}\n.handsontable.ht__selection--rows tbody th.beforeHiddenRow.ht__highlight:before,\n.handsontable.ht__selection--rows tbody th.afterHiddenRow.ht__highlight:after {\n  color: #eee;\n}\n.handsontable td.afterHiddenRow.firstVisibleRow,\n.handsontable th.afterHiddenRow.firstVisibleRow {\n  border-top: 1px solid #CCC;\n}\n"], "sourceRoot": ""}