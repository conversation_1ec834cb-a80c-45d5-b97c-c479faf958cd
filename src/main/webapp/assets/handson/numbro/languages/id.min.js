!function(e){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{var n;((n="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).numbro||(n.numbro={})).id=e()}}(function(){return function t(f,u,d){function l(n,e){if(!u[n]){if(!f[n]){var o="function"==typeof require&&require;if(!e&&o)return o(n,!0);if(s)return s(n,!0);var r=new Error("Cannot find module '"+n+"'");throw r.code="MODULE_NOT_FOUND",r}var i=u[n]={exports:{}};f[n][0].call(i.exports,function(e){return l(f[n][1][e]||e)},i,i.exports,t,f,u,d)}return u[n].exports}for(var s="function"==typeof require&&require,e=0;e<d.length;e++)l(d[e]);return l}({1:[function(e,n,o){"use strict";n.exports={languageTag:"id",delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"r",million:"j",billion:"m",trillion:"t"},ordinal:function(){return"."},currency:{symbol:"Rp",code:"IDR"}}},{}]},{},[1])(1)});
//# sourceMappingURL=id.min.js.map
