<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>wPaint 共享工具栏演示</title>
    
    <!-- wPaint CSS -->
    <link rel="stylesheet" type="text/css" href="wPaint.min.css"/>
    
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .demo-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .canvas-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 100px; /* 为工具栏留出空间 */
        }
        
        .canvas-item {
            background: white;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .canvas-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        
        .canvas-wrapper {
            border: 2px solid transparent;
            border-radius: 8px;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        /* 共享工具栏样式 */
        .wPaint-shared-toolbar-container {
            background: rgba(255, 255, 255, 0.98) !important;
            backdrop-filter: blur(10px);
            border: 1px solid #e0e0e0 !important;
            border-radius: 12px !important;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
            padding: 15px !important;
            min-width: 300px;
            max-width: 90vw;
        }
        
        .canvas-selector-wrapper {
            margin-bottom: 12px !important;
            padding-bottom: 10px;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .canvas-selector-wrapper label {
            font-weight: 600;
            color: #333;
            margin-right: 8px;
            font-size: 13px;
        }
        
        .canvas-selector {
            padding: 6px 12px !important;
            border: 1px solid #ddd !important;
            border-radius: 6px !important;
            background: white !important;
            font-size: 13px !important;
            min-width: 150px;
            cursor: pointer;
        }
        
        .wPaint-active-canvas {
            border-color: #007cba !important;
            opacity: 1 !important;
        }
        
        /* 移动端优化 */
        @media screen and (max-width: 768px) {
            .canvas-grid {
                grid-template-columns: 1fr;
                margin-top: 140px;
            }
            
            .wPaint-shared-toolbar-container {
                position: fixed !important;
                top: 10px !important;
                left: 10px !important;
                right: 10px !important;
                width: auto !important;
                z-index: 10000 !important;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1>wPaint 共享工具栏演示</h1>
            <p>这个演示展示了如何使用一个共享工具栏控制多个画布</p>
        </div>
        
        <div class="canvas-grid">
            <div class="canvas-item">
                <div class="canvas-title">画布 1</div>
                <div class="canvas-wrapper">
                    <div id="canvas1" style="width: 100%; height: 300px;"></div>
                </div>
            </div>
            
            <div class="canvas-item">
                <div class="canvas-title">画布 2</div>
                <div class="canvas-wrapper">
                    <div id="canvas2" style="width: 100%; height: 300px;"></div>
                </div>
            </div>
            
            <div class="canvas-item">
                <div class="canvas-title">画布 3</div>
                <div class="canvas-wrapper">
                    <div id="canvas3" style="width: 100%; height: 300px;"></div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- wPaint JavaScript -->
    <script type="text/javascript" src="wPaint.min.js"></script>
    <script type="text/javascript" src="plugins/main/wPaint.menu.main.min.js"></script>
    <script type="text/javascript" src="plugins/text/wPaint.menu.text.js"></script>
    <script type="text/javascript" src="plugins/shapes/wPaint.menu.main.shapes.min.js"></script>
    <script type="text/javascript" src="plugins/file/wPaint.menu.main.file.min.js"></script>
    
    <!-- wPaint 扩展 -->
    <script type="text/javascript" src="wPaint.toolbar.config.js"></script>
    <script type="text/javascript" src="wPaint.doubleclick.save.js"></script>
    <script type="text/javascript" src="wPaint.shared.toolbar.js"></script>
    
    <script>
        $(document).ready(function() {
            try {
                // 配置工具栏
                WPaintToolbarConfig.presets.full();
                
                // 初始化共享工具栏
                var sharedToolbar = WPaintSharedToolbar.init({
                    toolbarId: 'demo-shared-toolbar',
                    canvasSelectorId: 'demo-canvas-selector'
                });
                
                // 画布配置
                var canvasConfigs = [
                    { id: 'canvas1', name: '画布 1 - 绘图区域' },
                    { id: 'canvas2', name: '画布 2 - 设计区域' },
                    { id: 'canvas3', name: '画布 3 - 标注区域' }
                ];
                
                // 初始化所有画布
                canvasConfigs.forEach(function(config, index) {
                    var $canvas = $('#' + config.id);
                    
                    var canvasOptions = {
                        saveImg: function(imageData) {
                            // 演示保存功能
                            console.log('保存画布:', config.name);
                            alert('保存成功: ' + config.name);
                        },
                        menuHandle: false // 禁用独立工具栏
                    };
                    
                    // 添加到共享工具栏管理
                    sharedToolbar.addCanvas($canvas, canvasOptions, config.name);
                    
                    // 添加双击保存功能
                    WPaintDoubleClickSave.addToCanvas($canvas, {
                        imageName: config.name,
                        canvasIndex: index,
                        confirmMessage: '是否保存 ' + config.name + '？',
                        customSaveFunction: function(imageData, $canvas, saveConfig) {
                            alert('双击保存: ' + saveConfig.imageName);
                        }
                    });
                });
                
                console.log('共享工具栏演示初始化完成');
                
            } catch (error) {
                console.error('初始化失败:', error);
                alert('初始化失败，请检查控制台错误信息');
            }
        });
    </script>
</body>
</html>
