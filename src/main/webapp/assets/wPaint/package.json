{"name": "w<PERSON><PERSON><PERSON>", "title": "w<PERSON><PERSON>t j<PERSON><PERSON><PERSON> Plugin", "version": "2.5.0", "description": "A jQuery paint plugin for a simple drawing surface that you can easily pop into your pages, similar to the basic windows paint program.", "main": "wPaint.js", "repository": {"type": "git", "url": "https://github.com/websanova/wPaint"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://websanova.com"}, "homepage": "http://wpaint.websanova.com", "license": "MIT, GPL", "dependencies": {"grunt-contrib-concat": "", "grunt-contrib-jshint": "", "grunt-contrib-stylus": "", "grunt-contrib-uglify": "", "grunt-contrib-watch": "", "wPaint": "file:"}}