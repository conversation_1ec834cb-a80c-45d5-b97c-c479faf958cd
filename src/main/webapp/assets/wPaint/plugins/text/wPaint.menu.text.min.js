/*! wPaint - v2.5.0 - 2025-07-15 */
(e=>{e.fn.wPaint.menus.text={img:"//360.theolympiastone.com/assets/wPaint/plugins/text/img/icons-menu-text.png",items:{bold:{icon:"toggle",title:"Bold",index:0,callback:function(t){this.setFontBold(t)}},italic:{icon:"toggle",title:"Italic",index:1,callback:function(t){this.setFontItalic(t)}},fontSize:{title:"Font Size",icon:"select",range:[8,9,10,12,14,16,20,24,30],value:12,callback:function(t){this.setFontSize(t)}},fontFamily:{icon:"select",title:"Font Family",range:["Arial","Courier","Times","Verdana"],useRange:!0,value:"Arial",callback:function(t){this.setFontFamily(t)}}}},e.fn.wPaint.menus.main.items.text={icon:"menu",after:"pencil",title:"Text",index:7,callback:function(){this.setMode("text")}},e.extend(e.fn.wPaint.defaults,{fontSize:"12",fontFamily:"Arial",fontBold:!1,fontItalic:!1,fontUnderline:!1}),e.fn.wPaint.extend({generate:function(){this.$textCalc=e("<div></div>").hide(),this.$textInput=e('<textarea class="wPaint-text-input" spellcheck="false"></textarea>').on("mousedown",this._stopPropagation).css({position:"absolute"}).hide(),e("body").append(this.$textCalc),this.$el.append(this.$textInput),this.menus.all.text=this._createMenu("text")},_init:function(){var t,i=this;function n(){i._drawTextIfNotEmpty(),i.$textInput.hide(),i.$canvasTemp.hide()}for(t in this.menus.all)this.menus.all[t].$menu.on("click",n).on("mousedown",this._stopPropagation);e(document).on("mousedown",n)},setFillStyle:function(t){this.$textInput.css("color",t)},setFontSize:function(t){this.options.fontSize=parseInt(t,10),this._setFont({fontSize:t+"px",lineHeight:t+"px"}),this.menus.all.text._setSelectValue("fontSize",t)},setFontFamily:function(t){this.options.fontFamily=t,this._setFont({fontFamily:t}),this.menus.all.text._setSelectValue("fontFamily",t)},setFontBold:function(t){this.options.fontBold=t,this._setFont({fontWeight:t?"bold":""})},setFontItalic:function(t){this.options.fontItalic=t,this._setFont({fontStyle:t?"italic":""})},setFontUnderline:function(t){this.options.fontUnderline=t,this._setFont({fontWeight:t?"underline":""})},_setFont:function(t){this.$textInput.css(t),this.$textCalc.css(t)},_drawTextDown:function(t){this._drawTextIfNotEmpty(),this._drawShapeDown(t,1),this.$textInput.css({left:t.pageX-1,top:t.pageY-1,width:0,height:0}).show().focus()},_drawTextMove:function(t){this._drawShapeMove(t,1),this.$textInput.css({left:t.left-1,top:t.top-1,width:t.width,height:t.height})},_drawTextIfNotEmpty:function(){""!==this.$textInput.val()&&this._drawText()},_drawText:function(){var t,i,n="",e=this.$textInput.val().split("\n"),o=(this.$textInput.width(),this.$textInput.position()),s=o.left+1,l=o.top+1,a=0;for(this.options.fontItalic&&(n+="italic "),this.options.fontBold&&(n+="bold "),n+=this.options.fontSize+"px "+this.options.fontFamily,t=0,i=e.length;t<i;t++){var h=this.$textCalc.text(e[t]).width();a<h&&(a=h)}for(this.$textInput.width(a+2),t=0,i=e.length;t<i;t++)this.ctx.fillStyle=this.options.fillStyle,this.ctx.textBaseline="top",this.ctx.font=n,this.ctx.fillText(e[t],s,l),l+=this.options.fontSize;this.$textInput.val(""),this._addUndo()}})})(jQuery);