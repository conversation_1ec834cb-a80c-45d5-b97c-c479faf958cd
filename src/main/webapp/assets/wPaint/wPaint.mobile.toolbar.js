/**
 * wPaint 移动端工具栏管理器
 * 提供移动端专用的工具栏交互和布局优化
 */

(function($) {
    'use strict';
    
    window.WPaintMobileToolbar = {
        
        config: {
            isCollapsed: false,
            autoCollapseDelay: 3000,
            collapseTimer: null,
            $toolbar: null,
            $toggleButton: null
        },
        
        /**
         * 初始化移动端工具栏
         */
        init: function() {
            this._createToggleButton();
            this._bindEvents();
            this._setupAutoCollapse();
            return this;
        },
        
        /**
         * 创建工具栏切换按钮
         */
        _createToggleButton: function() {
            var self = this;
            
            // 创建浮动切换按钮
            this.config.$toggleButton = $('<div class="mobile-toolbar-toggle">')
                .html('<i class="layui-icon layui-icon-more-vertical"></i>')
                .css({
                    'position': 'fixed',
                    'left': '10px',
                    'top': '50%',
                    'transform': 'translateY(-50%)',
                    'width': '40px',
                    'height': '40px',
                    'background': 'rgba(0, 123, 186, 0.9)',
                    'color': 'white',
                    'border-radius': '50%',
                    'display': 'flex',
                    'align-items': 'center',
                    'justify-content': 'center',
                    'z-index': '9999',
                    'box-shadow': '0 2px 8px rgba(0,0,0,0.2)',
                    'cursor': 'pointer',
                    'transition': 'all 0.3s ease'
                })
                .appendTo('body');
                
            // 点击切换工具栏显示/隐藏
            this.config.$toggleButton.on('click touchstart', function(e) {
                e.preventDefault();
                e.stopPropagation();
                self.toggle();
            });
        },
        
        /**
         * 绑定事件
         */
        _bindEvents: function() {
            var self = this;
            
            // 监听工具栏创建
            $(document).on('wPaint:toolbarCreated', function(e, $toolbar) {
                self.config.$toolbar = $toolbar;
                self._setupToolbar($toolbar);
            });
            
            // 监听画布点击
            $(document).on('touchstart', '.wPaint-canvas', function() {
                self._startAutoCollapseTimer();
            });
            
            // 监听工具栏点击
            $(document).on('touchstart', '.wPaint-menu', function(e) {
                e.stopPropagation();
                self._cancelAutoCollapseTimer();
            });
        },
        
        /**
         * 设置工具栏
         */
        _setupToolbar: function($toolbar) {
            var self = this;
            
            // 初始状态隐藏工具栏
            $toolbar.css({
                'transform': 'translateX(-100%)',
                'transition': 'transform 0.3s ease'
            });
            
            this.config.isCollapsed = true;
            this._updateToggleButton();
        },
        
        /**
         * 切换工具栏显示状态
         */
        toggle: function() {
            if (this.config.isCollapsed) {
                this.show();
            } else {
                this.hide();
            }
        },
        
        /**
         * 显示工具栏
         */
        show: function() {
            if (this.config.$toolbar) {
                this.config.$toolbar.css('transform', 'translateX(0)');
                this.config.isCollapsed = false;
                this._updateToggleButton();
                this._startAutoCollapseTimer();
            }
        },
        
        /**
         * 隐藏工具栏
         */
        hide: function() {
            if (this.config.$toolbar) {
                this.config.$toolbar.css('transform', 'translateX(-100%)');
                this.config.isCollapsed = true;
                this._updateToggleButton();
                this._cancelAutoCollapseTimer();
            }
        },
        
        /**
         * 更新切换按钮状态
         */
        _updateToggleButton: function() {
            if (this.config.$toggleButton) {
                var icon = this.config.isCollapsed ? 'layui-icon-more-vertical' : 'layui-icon-close';
                this.config.$toggleButton.find('i')
                    .removeClass('layui-icon-more-vertical layui-icon-close')
                    .addClass(icon);
                    
                // 调整按钮位置
                var left = this.config.isCollapsed ? '10px' : '70px';
                this.config.$toggleButton.css('left', left);
            }
        },
        
        /**
         * 设置自动收起
         */
        _setupAutoCollapse: function() {
            // 在移动端启用自动收起
            if (this._isMobile()) {
                this._startAutoCollapseTimer();
            }
        },
        
        /**
         * 开始自动收起计时器
         */
        _startAutoCollapseTimer: function() {
            var self = this;
            this._cancelAutoCollapseTimer();
            
            this.config.collapseTimer = setTimeout(function() {
                if (!self.config.isCollapsed) {
                    self.hide();
                }
            }, this.config.autoCollapseDelay);
        },
        
        /**
         * 取消自动收起计时器
         */
        _cancelAutoCollapseTimer: function() {
            if (this.config.collapseTimer) {
                clearTimeout(this.config.collapseTimer);
                this.config.collapseTimer = null;
            }
        },
        
        /**
         * 检测是否为移动设备
         */
        _isMobile: function() {
            return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
                   'ontouchstart' in window || navigator.maxTouchPoints > 0;
        },
        
        /**
         * 添加快捷工具按钮
         */
        addQuickTools: function() {
            var self = this;
            var $quickTools = $('<div class="mobile-quick-tools">')
                .css({
                    'position': 'fixed',
                    'bottom': '20px',
                    'right': '20px',
                    'display': 'flex',
                    'flex-direction': 'column',
                    'gap': '10px',
                    'z-index': '9998'
                })
                .appendTo('body');
                
            // 撤销按钮
            $('<button class="quick-tool-btn" data-tool="undo">')
                .html('<i class="layui-icon layui-icon-return"></i>')
                .css(this._getQuickToolStyle())
                .appendTo($quickTools);
                
            // 重做按钮
            $('<button class="quick-tool-btn" data-tool="redo">')
                .html('<i class="layui-icon layui-icon-right"></i>')
                .css(this._getQuickToolStyle())
                .appendTo($quickTools);
                
            // 保存按钮
            $('<button class="quick-tool-btn" data-tool="save">')
                .html('<i class="layui-icon layui-icon-ok"></i>')
                .css(this._getQuickToolStyle('#4CAF50'))
                .appendTo($quickTools);
                
            // 绑定快捷工具事件
            $quickTools.on('click', '.quick-tool-btn', function(e) {
                e.preventDefault();
                var tool = $(this).data('tool');
                self._executeQuickTool(tool);
            });
        },
        
        /**
         * 获取快捷工具样式
         */
        _getQuickToolStyle: function(bgColor) {
            return {
                'width': '50px',
                'height': '50px',
                'border': 'none',
                'border-radius': '50%',
                'background': bgColor || 'rgba(0, 123, 186, 0.9)',
                'color': 'white',
                'display': 'flex',
                'align-items': 'center',
                'justify-content': 'center',
                'box-shadow': '0 2px 8px rgba(0,0,0,0.2)',
                'cursor': 'pointer',
                'transition': 'all 0.2s ease',
                'font-size': '18px'
            };
        },
        
        /**
         * 执行快捷工具操作
         */
        _executeQuickTool: function(tool) {
            // 查找当前活动的画布
            var $activeCanvas = $('.wPaint-canvas').first();
            var wPaintInstance = $activeCanvas.parent().data('wPaint');
            
            if (wPaintInstance) {
                switch (tool) {
                    case 'undo':
                        if (wPaintInstance.undo) {
                            wPaintInstance.undo();
                        }
                        break;
                    case 'redo':
                        if (wPaintInstance.redo) {
                            wPaintInstance.redo();
                        }
                        break;
                    case 'save':
                        // 触发双击保存
                        $activeCanvas.trigger('dblclick');
                        break;
                }
            }
        },
        
        /**
         * 销毁移动端工具栏
         */
        destroy: function() {
            this._cancelAutoCollapseTimer();
            if (this.config.$toggleButton) {
                this.config.$toggleButton.remove();
            }
            $('.mobile-quick-tools').remove();
        }
    };
    
    // 自动初始化 - 暂时禁用，避免与原有工具栏冲突
    // $(document).ready(function() {
    //     if (WPaintMobileToolbar._isMobile()) {
    //         WPaintMobileToolbar.init();
    //         WPaintMobileToolbar.addQuickTools();
    //     }
    // });
    
})(jQuery);
