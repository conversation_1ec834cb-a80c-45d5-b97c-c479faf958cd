/**
 * wPaint 工具栏自定义配置
 * 用于在页面中灵活配置要显示的工具
 */

(function($) {
    'use strict';
    
    // 工具栏配置管理器
    window.WPaintToolbarConfig = {
        
        /**
         * 自定义工具栏
         * @param {Object} options 配置选项
         * @param {Array} options.hiddenTools 要隐藏的工具列表
         * @param {Object} options.customTools 自定义工具
         * @param {Array} options.onlyShow 只显示指定的工具（会隐藏其他所有工具）
         */
        customize: function(options) {
            options = options || {};
            
            // 如果指定了只显示某些工具
            if (options.onlyShow && Array.isArray(options.onlyShow)) {
                this.showOnlyTools(options.onlyShow);
                return;
            }
            
            // 默认隐藏的工具
            var defaultHiddenTools = ['clear', 'eraser', 'bucket', 'loadBg', 'loadFg'];
            var hiddenTools = options.hiddenTools || defaultHiddenTools;
            
            // 删除指定的工具
            this.hideTools(hiddenTools);
            
            // 如果需要，可以添加自定义工具
            if (options.customTools) {
                this.addCustomTools(options.customTools);
            }
        },
        
        /**
         * 隐藏指定的工具
         * @param {Array} tools 要隐藏的工具列表
         */
        hideTools: function(tools) {
            if (!Array.isArray(tools)) return;
            
            tools.forEach(function(tool) {
                if ($.fn.wPaint.menus.main.items[tool]) {
                    delete $.fn.wPaint.menus.main.items[tool];
                }
            });
        },
        
        /**
         * 只显示指定的工具（隐藏其他所有工具）
         * @param {Array} tools 要显示的工具列表
         */
        showOnlyTools: function(tools) {
            if (!Array.isArray(tools)) return;
            
            var allTools = Object.keys($.fn.wPaint.menus.main.items);
            var toolsToHide = allTools.filter(function(tool) {
                return tools.indexOf(tool) === -1;
            });
            
            this.hideTools(toolsToHide);
        },
        
        /**
         * 添加自定义工具
         * @param {Object} customTools 自定义工具对象
         */
        addCustomTools: function(customTools) {
            if (typeof customTools === 'object') {
                $.extend($.fn.wPaint.menus.main.items, customTools);
            }
        },
        
        /**
         * 预设配置
         */
        presets: {
            // 基础绘图工具
            basic: function() {
                WPaintToolbarConfig.showOnlyTools([
                    'undo', 'redo', 'clear', 
                    'pencil', 'line', 
                    'strokeStyle', 'lineWidth'
                ]);
            },
            
            // 形状绘制工具
            shapes: function() {
                WPaintToolbarConfig.showOnlyTools([
                    'undo', 'redo', 'clear',
                    'rectangle', 'ellipse', 'line',
                    'strokeStyle', 'lineWidth'
                ]);
            },
            
            // 文本编辑工具
            text: function() {
                WPaintToolbarConfig.showOnlyTools([
                    'undo', 'redo', 'clear',
                    'text', 'pencil',
                    'strokeStyle', 'fillStyle', 'lineWidth'
                ]);
            },
            
            // 完整工具（除了橡皮擦、油漆桶、加载）
            full: function() {
                WPaintToolbarConfig.hideTools(['clear', 'eraser', 'bucket', 'loadBg', 'loadFg']);
            },
            
            // 最小工具集
            minimal: function() {
                WPaintToolbarConfig.showOnlyTools([
                    'undo', 'redo', 'pencil', 'strokeStyle'
                ]);
            }
        }
    };
    
    // 便捷函数
    window.customizeWPaintToolbar = function(options) {
        WPaintToolbarConfig.customize(options);
    };
    
})(jQuery);
