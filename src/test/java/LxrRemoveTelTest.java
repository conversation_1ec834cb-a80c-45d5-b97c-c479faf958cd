import com.google.common.collect.Lists;
import com.jfinal.plugin.activerecord.ActiveRecordPlugin;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import com.jfinal.plugin.druid.DruidPlugin;
import com.theolympiastone.club.common.OSClubConfig;
import com.theolympiastone.club.common.model._MappingKit;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class LxrRemoveTelTest {
    static {
        DruidPlugin druidPlugin = OSClubConfig.getDruidPlugin();
        ActiveRecordPlugin arp = new ActiveRecordPlugin("mysql", druidPlugin);
        _MappingKit.mapping(arp);
        druidPlugin.start();
        arp.start();
    }

    public static void main(String[] args) {
        List<Record> recordList = Db.find("select * from lxr where yx is not null");
        String emailRegex = "\\b[A-Za-z0-9._%+-]+(@[A-Za-z0-9.-]+)+\\.[A-Z|a-z]{2,}(\\.[A-Z|a-z]{2,})?\\b";
        Pattern pattern = Pattern.compile(emailRegex);
        for (Record record : recordList) {
            String yx = record.getStr("yx");
            int id = record.getInt("id");
            String yxNoBlank = yx.replace(" ", "");
            Matcher matcher = pattern.matcher(yxNoBlank);
            List<String> emailList = Lists.newArrayList();
            while (matcher.find()) {
                String email = matcher.group();
                if (email.indexOf('@') != email.lastIndexOf('@')) {
                    emailList.add(email);
//                    System.out.println("id: " + id + ", email: " + email + " 待处理\n");
                } else {
                    if (!email.endsWith(".jpg") && !email.endsWith(".png")) {
                        emailList.add(email);
                    }
                }
            }
            String newJczyx = StringUtils.join(emailList, ";");
            if (!yx.equalsIgnoreCase(newJczyx)) {
                if (StringUtils.isEmpty(newJczyx)) {
                    System.out.println("delete from lxr where id=" + id + ";");
                } else {
                    System.out.format("update lxr set yx='%s' where id=%d;\n", newJczyx, id);
                }
//                System.out.format("%s\n%s\n\n", yx, newJczyx);
            } else if ("oxter(at)westnet.com.au".equalsIgnoreCase(yx)) {
                System.out.format("update lxr set yx='%s' where id=%d;\n", yx.replace("(at)", "@"), id);
            } else if ("info(@monuments.ie".equalsIgnoreCase(yx)) {
                System.out.format("update lxr set yx='%s' where id=%d;\n", yx.replace("(", ""), id);
            } else if ("631-957-0700/+17188964400;公司：************************个人：<EMAIL>".equalsIgnoreCase(yx)) {
                System.out.format("update lxr set yx='%s' where id=%d;\n", "<EMAIL>;<EMAIL>", id);
            } else if ("<EMAIL>.Morton由SilverStripe提供支持".equalsIgnoreCase(yx)) {
                System.out.format("update lxr set yx='%s' where id=%d;\n", "<EMAIL>", id);
            }
        }
    }
}
