import com.arronlong.httpclientutil.HttpClientUtil;
import com.arronlong.httpclientutil.common.HttpConfig;
import com.arronlong.httpclientutil.common.HttpHeader;
import com.arronlong.httpclientutil.exception.HttpProcessException;
import com.google.common.collect.Lists;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.Header;
import org.apache.http.HttpHost;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;

import javax.net.ssl.SSLContext;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.List;

import static com.theolympiastone.club.common.kit.HttpsKit.createIgnoreVerifySSL;
import static com.theolympiastone.club.common.kit.RandomUserAgent.getRandomUserAgent;
import static com.theolympiastone.club.common.kit.UrlKit.getTypeByExtenssion;
import static net.m3u8.utils.Constant.FILESEPARATOR;

public class QzxXetXzTest {
    public static HttpConfig httpConfig;
    public static HttpClientBuilder httpClientBuilder;
    public static Header[] headers = HttpHeader.custom()
            .connection("keep-alive")
            .host("encrypt-k-vod.xet.tech")
            .referer("https://apphfuydjku5721.pc.xiaoe-tech.com/")
            .userAgent("Mozilla/5.0 (Macintosh; Intel Mac OS X 10_16_0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Safari/537.36")
            .build();

    static {
        SSLContext sslcontext = createIgnoreVerifySSL("TLSv1.2");
        Registry<ConnectionSocketFactory> socketFactoryRegistry = RegistryBuilder.<ConnectionSocketFactory>create()
                .register("http", PlainConnectionSocketFactory.INSTANCE)
                .register("https", new SSLConnectionSocketFactory(sslcontext))
                .build();
        PoolingHttpClientConnectionManager connManager = new PoolingHttpClientConnectionManager(socketFactoryRegistry);
        httpClientBuilder = HttpClients.custom().setConnectionManager(connManager);
        httpConfig = HttpConfig.custom().client(httpClientBuilder.build()).headers(headers).timeout(20000);
    }

    public static void main(String[] args) throws IOException {
        String dir = "D:\\data\\Videos\\m3u8";
        String urlFilePath = "D:\\source\\osclub\\src\\test\\java\\temp.txt";
        String fileName = "A 准备篇 01 - 为什么外贸人一定要做海外电话销售.mp4";
        getVedio(dir, urlFilePath, fileName);
    }

    public static void downloadUrlFileRandomHeader(String url, File file) {
        String typeByExtenssion = getTypeByExtenssion(url);
        if (StringUtils.isEmpty(typeByExtenssion)) {
            return;
        }
        Header[] headers = HttpHeader.custom()
                .host("encrypt-k-vod.xet.tech")
                .referer("https://apphfuydjku5721.pc.xiaoe-tech.com/")
                .build();
        try (FileOutputStream out = new FileOutputStream(file)) {
            try {
                httpConfig.url(url).headers(headers).out(out);
                System.out.println("start download: " + url + ", out: " + file.getAbsolutePath());
                HttpClientUtil.down(httpConfig);
            } catch (HttpProcessException e) {
                reBuildHttpClient();
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private static void reBuildHttpClient() {
        SSLContext sslcontext = createIgnoreVerifySSL("TLSv1.2");
        Registry<ConnectionSocketFactory> socketFactoryRegistry = RegistryBuilder.<ConnectionSocketFactory>create()
                .register("http", PlainConnectionSocketFactory.INSTANCE)
                .register("https", new SSLConnectionSocketFactory(sslcontext))
                .build();
        PoolingHttpClientConnectionManager connManager = new PoolingHttpClientConnectionManager(socketFactoryRegistry);
        HttpHost proxy = new HttpHost("127.0.0.1", 7890);
        httpClientBuilder = HttpClients.custom().setConnectionManager(connManager).setProxy(proxy);
        Header[] headers = HttpHeader.custom()
                .connection("keep-alive")
                .userAgent(getRandomUserAgent())
                .referer("www.google.com")
                .build();
        httpConfig = HttpConfig.custom().client(httpClientBuilder.build()).headers(headers).timeout(20000);
    }

    public static void getVedio(String dir, String urlFilePath, String fileName) throws IOException {
        File urlFile = new File(urlFilePath);
        String content = FileUtils.readFileToString(urlFile, "UTF-8");
        String[] split = content.split("\\n");
        List<File> files = Lists.newArrayList();
        //将ts片段链接加入set集合
        for (int i = 0; i < split.length; i++) {
            String s = split[i].replace("\\r", "");
//            String[] urlSplit = s.split("\\?");
//            String url = urlSplit[0] + "?" + URLEncoder.encode(urlSplit[1], "UTF-8");
            File file = new File(dir + FILESEPARATOR + i + ".xyz");
            files.add(file);
            downloadUrlFileRandomHeader(s, file);
        }
        try {
            File file = new File(dir + FILESEPARATOR + fileName);
            FileUtils.deleteQuietly(file);
            try (FileOutputStream fileOutputStream = new FileOutputStream(file)) {
                byte[] b = new byte[4096];
                for (File f : files) {
                    try (FileInputStream fileInputStream = new FileInputStream(f)) {
                        int len;
                        while ((len = fileInputStream.read(b)) != -1) {
                            fileOutputStream.write(b, 0, len);
                        }
                    }
                    fileOutputStream.flush();
                    FileUtils.deleteQuietly(f);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
