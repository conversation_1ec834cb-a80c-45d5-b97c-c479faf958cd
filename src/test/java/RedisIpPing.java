import redis.clients.jedis.Jedis;

public class RedisIpPing {
    public static String s = "Host: ************ ()   Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************ ()   Ports: 6379/open/tcp//redis///\n" +
            "Host: ************ ()   Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************ ()   Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************9 ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: *************2 () Ports: 6379/open/tcp//redis///\n" +
            "Host: *************6 () Ports: 6379/open/tcp//redis///\n" +
            "Host: *************8 () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************01 () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************08 () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************09 () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************10 () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************13 () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************15 () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************18 () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************21 () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************27 () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************28 () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************34 () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************35 () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************36 () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************43 () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************44 () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************49 () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************ ()   Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************6 ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: *************5 () Ports: 6379/open/tcp//redis///\n" +
            "Host: *************6 () Ports: 6379/open/tcp//redis///\n" +
            "Host: 60.205.117.237 () Ports: 6379/open/tcp//redis///\n" +
            "Host: *************6 () Ports: 6379/open/tcp//redis///\n" +
            "Host: *************7 () Ports: 6379/open/tcp//redis///\n" +
            "Host: 60.205.117.252 () Ports: 6379/open/tcp//redis///\n" +
            "Host: 60.205.117.253 () Ports: 6379/open/tcp//redis///\n" +
            "Host: 60.205.118.31 ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: 60.205.118.39 ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: 60.205.118.56 ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: 60.205.118.63 ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: 60.205.118.65 ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: 60.205.118.68 ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: 60.205.118.77 ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: 60.205.118.78 ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: 60.205.118.81 ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: 60.205.118.82 ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: 60.205.118.108 () Ports: 6379/open/tcp//redis///\n" +
            "Host: 60.205.118.109 () Ports: 6379/open/tcp//redis///\n" +
            "Host: 60.205.118.114 () Ports: 6379/open/tcp//redis///\n" +
            "Host: 60.205.118.119 () Ports: 6379/open/tcp//redis///\n" +
            "Host: 60.205.118.123 () Ports: 6379/open/tcp//redis///\n" +
            "Host: 60.205.118.135 () Ports: 6379/open/tcp//redis///\n" +
            "Host: 60.205.118.140 () Ports: 6379/open/tcp//redis///\n" +
            "Host: 60.205.118.174 () Ports: 6379/open/tcp//redis///\n" +
            "Host: 60.205.118.186 () Ports: 6379/open/tcp//redis///\n" +
            "Host: 60.205.118.188 () Ports: 6379/open/tcp//redis///\n" +
            "Host: 60.205.118.202 () Ports: 6379/open/tcp//redis///\n" +
            "Host: 60.205.118.215 () Ports: 6379/open/tcp//redis///\n" +
            "Host: 60.205.118.218 () Ports: 6379/open/tcp//redis///\n" +
            "Host: 60.205.118.224 () Ports: 6379/open/tcp//redis///\n" +
            "Host: 60.205.118.225 () Ports: 6379/open/tcp//redis///\n" +
            "Host: 60.205.118.228 () Ports: 6379/open/tcp//redis///\n" +
            "Host: 60.205.118.236 () Ports: 6379/open/tcp//redis///\n" +
            "Host: 60.205.118.240 () Ports: 6379/open/tcp//redis///\n" +
            "Host: 60.205.118.242 () Ports: 6379/open/tcp//redis///\n" +
            "Host: 60.205.118.243 () Ports: 6379/open/tcp//redis///\n" +
            "Host: 60.205.118.246 () Ports: 6379/open/tcp//redis///\n" +
            "Host: 60.205.118.249 () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************ ()   Ports: 6379/open/tcp//redis///\n" +
            "Host: ************ ()   Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************7 ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************ ()   Ports: 6379/open/tcp//redis///\n" +
            "Host: ************ ()   Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************0 ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************8 ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: *************2 () Ports: 6379/open/tcp//redis///\n" +
            "Host: *************6 () Ports: 6379/open/tcp//redis///\n" +
            "Host: *************8 () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************** () Ports: 6379/open/tcp//redis///\n" +
            "Host: ************ ()   Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************ ()   Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///\n" +
            "Host: ************* ()  Ports: 6379/open/tcp//redis///";

    public static void main(String[] args) {
        String[] split = s.split("\n");
        for (String s1 : split) {
            String ip = s1.replace("Host: ", "").replace("Ports: 6379/open/tcp//redis///", "").replace(" ", "").replace("(", "").replace(")", "");
            Jedis jedis = new Jedis(ip);
            String ping;
            try {
                ping = jedis.ping();
            } catch (Exception e) {
                jedis.close();
                continue;
            }
            if ("PONG".equals(ping)) {
                System.out.println(ip);
            }
            jedis.close();
        }
    }
}
