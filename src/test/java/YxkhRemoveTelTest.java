import com.google.common.collect.Lists;
import com.jfinal.plugin.activerecord.ActiveRecordPlugin;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import com.jfinal.plugin.druid.DruidPlugin;
import com.theolympiastone.club.common.OSClubConfig;
import com.theolympiastone.club.common.model._MappingKit;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class YxkhRemoveTelTest {
    static {
        DruidPlugin druidPlugin = OSClubConfig.getDruidPlugin();
        ActiveRecordPlugin arp = new ActiveRecordPlugin("mysql", druidPlugin);
        _MappingKit.mapping(arp);
        druidPlugin.start();
        arp.start();
    }

    public static void main(String[] args) {
        List<Record> recordList = Db.find("select * from yxkh where jczyx is not null");
        String emailRegex = "\\b[A-Za-z0-9._%+-]+(@[A-Za-z0-9.-]+)+\\.[A-Z|a-z]{2,}(\\.[A-Z|a-z]{2,})?\\b";
        Pattern pattern = Pattern.compile(emailRegex);
        for (Record record : recordList) {
            String jczyx = record.getStr("jczyx");
            int id = record.getInt("id");
            String jczyxNoBlank = jczyx.replace(" ", "");
            Matcher matcher = pattern.matcher(jczyxNoBlank);
            List<String> emailList = Lists.newArrayList();
            while (matcher.find()) {
                String email = matcher.group();
                if (email.indexOf('@') != email.lastIndexOf('@')) {
                    emailList.add(email);
                    System.out.println("id: " + id + ", email: " + email + " 待处理\n");
                } else {
                    emailList.add(email);
                }
            }
            String newJczyx = StringUtils.join(emailList, ";");
            if (!jczyx.equalsIgnoreCase(newJczyx)) {
                System.out.format("update yxkh set jczyx='%s' where id=%d;\n", newJczyx, id);
//                System.out.format("%s\n%s\n\n", jczyx, newJczyx);
            } else if ("oxter(at)westnet.com.au".equalsIgnoreCase(jczyx)) {
                System.out.format("update yxkh set jczyx='%s' where id=%d;\n", jczyx.replace("(at)", "@"), id);
            }else if ("info(@monuments.ie".equalsIgnoreCase(jczyx)) {
                System.out.format("update yxkh set jczyx='%s' where id=%d;\n", jczyx.replace("(", ""), id);
            }else if ("631-957-0700/+17188964400;公司：************************个人：<EMAIL>".equalsIgnoreCase(jczyx)) {
                System.out.format("update yxkh set jczyx='%s' where id=%d;\n", "<EMAIL>;<EMAIL>", id);
            }else if ("<EMAIL>.Morton由SilverStripe提供支持".equalsIgnoreCase(jczyx)) {
                System.out.format("update yxkh set jczyx='%s' where id=%d;\n", "<EMAIL>", id);
            }
        }
    }
}
