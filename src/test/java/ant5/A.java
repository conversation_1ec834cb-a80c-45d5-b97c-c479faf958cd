//
// Decompiled by Jadx - 857ms
//
package ant5;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

public class A {
    public static byte[][] a(int i, int i2, byte[] bArr, byte[] bArr2, int i3) throws NoSuchAlgorithmException {
        MessageDigest messageDigest = MessageDigest.getInstance("md5");
        int i4 = i;
        byte[] bArr3 = new byte[i4];
        int i5 = i2;
        byte[] bArr4 = new byte[i5];
        byte[][] bArr5 = {bArr3, bArr4};
        if (bArr2 == null) {
            return bArr5;
        }
        byte[] bArr6 = null;
        int i6 = 0;
        int i7 = 0;
        int i8 = 0;
        while (true) {
            messageDigest.reset();
            int i9 = i6 + 1;
            if (i6 > 0) {
                messageDigest.update(bArr6);
            }
            messageDigest.update(bArr2);
            if (bArr != null) {
                messageDigest.update(bArr, 0, 8);
            }
            bArr6 = messageDigest.digest();
            for (int i10 = 1; i10 < i3; i10++) {
                messageDigest.reset();
                messageDigest.update(bArr6);
                bArr6 = messageDigest.digest();
            }
            int i11 = 0;
            if (i4 > 0) {
                while (i4 != 0 && i11 != bArr6.length) {
                    bArr3[i7] = bArr6[i11];
                    i4--;
                    i11++;
                    i7++;
                }
            }
            if (i5 > 0 && i11 != bArr6.length) {
                while (i5 != 0 && i11 != bArr6.length) {
                    bArr4[i8] = bArr6[i11];
                    i5--;
                    i11++;
                    i8++;
                }
            }
            if (i4 == 0 && i5 == 0) {
                break;
            }
            i6 = i9;
        }
        for (int i12 = 0; bArr6!=null &&i12 < bArr6.length; i12++) {
            bArr6[i12] = 0;
        }
        return bArr5;
    }

    public static String b(byte[] bArr) {
        StringBuffer stringBuffer = new StringBuffer();
        for (byte b : bArr) {
            String hexString = Integer.toHexString(b & 255);
            if (hexString.length() == 1) {
                stringBuffer.append("0");
            }
            stringBuffer.append(hexString);
        }
        return stringBuffer.toString();
    }

    public static String c(byte[] bArr) {
        String str = "";
        for (byte b : bArr) {
            String hexString = Integer.toHexString(b & 255);
            str = hexString.length() == 1 ? str + "0" + hexString : str + hexString;
        }
        return str.toUpperCase();
    }

    public static byte[] d(byte[] bArr, byte[] bArr2) {
        byte[] bArr3 = new byte[bArr.length + bArr2.length];
        System.arraycopy(bArr, 0, bArr3, 0, bArr.length);
        System.arraycopy(bArr2, 0, bArr3, bArr.length, bArr2.length);
        return bArr3;
    }

    public static String f(String str, String str2) {
        try {
            Cipher cipher = Cipher.getInstance("AES/CFB/NoPadding");
            cipher.init(1, new SecretKeySpec(a(32, 16, null, str.getBytes("UTF-8"), 0)[0], "AES"));
            return c(d(cipher.getIV(), cipher.doFinal(str2.getBytes("UTF-8"))));
        } catch (Exception e2) {
            e2.printStackTrace();
            return null;
        }
    }
}