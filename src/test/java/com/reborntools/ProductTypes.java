package com.reborntools;

import com.google.common.collect.Sets;
import com.taobao.api.internal.util.StringUtils;

import java.io.IOException;
import java.util.Comparator;
import java.util.Set;
import java.util.TreeSet;

public class ProductTypes {
    public static String aa = "Laser-welded blade>diamond saw blade>laser welded blade\n" +
            "Product>Laser-welded blade>Alloy steel\n" +
            "Product>Laser-welded blade>Channel stainless steel\n" +
            "Product>Laser-welded blade>Flat stainless steel\n" +
            "Product>Laser-welded blade>Stainless steel coil\n" +
            "Product>Laser-welded blade>diamond saw blade\n" +
            "Cutting tools>Laser-welded blade>General Purpose  TUR\n" +
            "Grinding tools>Sintered>Single Row Grinding Cup Wheel\n" +
            "Cutting tools>Laser-welded blade>Gneral purpose  ARR\n" +
            "Cutting tools>Laser-welded blade>General Purpose DOT\n" +
            "Cutting tools>Laser-welded blade>General Purpose,EGL\n" +
            "Cutting tools>Laser-welded blade>General Purpose,PEK\n" +
            "Cutting tools>Laser-welded blade>General Purpose,SEG\n" +
            "Grinding tools>Sintered>T-Segment Grinding Cup Wheel\n" +
            "Grinding tools>Sintered>Arrow Segment Grinding Cup Wheel\n" +
            "Grinding tools>Sintered>Turbo Row Grinding Cup Wheel\n" +
            "Grinding tools>Sintered>Double Row Grinding Cup Wheel\n" +
            "Grinding tools>Sintered>R3 Grinding Cup Wheel\n" +
            "Grinding tools>Floor grinding preparations>10 Inch Floor Grinding Head, 20 Arrow Segments\n" +
            "Grinding tools>Floor grinding preparations>10 Inch Floor Grinding Head, 20 Arrow Segments\n" +
            "Grinding tools>Floor grinding preparations>10 Inch Floor Grinding Head, 20 Bar Segments\n" +
            "Grinding tools>Floor grinding preparations>10 Inch PCD Floor Grinding Head\n" +
            "Cutting tools>Sintered blade>Continuous Rim Diamond Blade\n" +
            "Cutting tools>Laser-welded blade>Diamond Saw Blade for Asphalt,ASP-S\n" +
            "Cutting tools>Laser-welded blade>Diamond Saw Blade for Asphalt,ASP-W\n" +
            "Cutting tools>Laser-welded blade>Diamond Saw Blade for Wall Saws\n" +
            "Cutting tools>Sintered blade>Segmented Rim Diamond Blade\n" +
            "Cutting tools>Sintered blade>Segmented Turbo Diamond Blade,ST1\n" +
            "Cutting tools>Sintered blade>Segmented Turbo Diamond Blade,ST2\n" +
            "Cutting tools>Sintered blade>Segmented Turbo Diamond Blade,ST3\n" +
            "Cutting tools>Sintered blade>Segmented Turbo Diamond Blade,ST4\n" +
            "Cutting tools>Sintered blade>Segmented Turbo Diamond Blade,ST5\n" +
            "Cutting tools>Sintered blade>Segmented Turbo Diamond Blade,ST5\n" +
            "Cutting tools>Sintered blade>Thin Turbo Rim Diamond Blade,TSR\n" +
            "Cutting tools>Sintered blade>Thin Turbo Rim Diamond Blade,TXR\n" +
            "Cutting tools>Sintered blade>Thin Turbo Rim Diamond Blade,TRR\n" +
            "Cutting tools>Sintered blade>Turbo Rim Diamond Blade,TRA\n" +
            "Cutting tools>Sintered blade>Turbo Rim Diamond Blade,TRS\n" +
            "Cutting tools>Sintered blade>Turbo Rim Diamond Blade,TUR\n" +
            "Cutting tools>Tuck point blade>Diamond Crack Chaser Blade\n" +
            "Cutting tools>Tuck point blade>Diamond Sandwich Tuck Point Blade\n" +
            "Cutting tools>Tuck point blade>Diamond Sandwich Tuck Point Blade, SAN3\n" +
            "Cutting tools>Tuck point blade>Diamond Tuck Point Blade\n" +
            "Cutting tools>Vacuum Brazed blade>Multi-Purpose Rescue Blades\n" +
            "diamond saw blade>laser welded blade>NULL\n" +
            "Laser-welded blade>Alloy steel>NULL\n" +
            "Laser-welded blade>Channel stainless steel>NULL\n" +
            "Laser-welded blade>Flat stainless steel>NULL\n" +
            "Laser-welded blade>Stainless steel coil>NULL\n" +
            "Laser-welded blade>General Purpose  TUR>NULL\n" +
            "Tools accessories>Extensions>NULL\n" +
            "Cutting tools>Electroplated blade>NULL\n" +
            "Cutting tools>TCT/PCD saw blade>NULL\n" +
            "Sintered>Single Row Grinding Cup Wheel>NULL\n" +
            "Grinding tools>Stones - brazed>NULL\n" +
            "Grinding tools>Grinding shoes>NULL\n" +
            "Grinding tools>Foundry>NULL\n" +
            "Drilling tools>Vacuum brazed drill bits>NULL\n" +
            "Drilling tools>Electroplated drill bits>NULL\n" +
            "Drilling tools>Laser-welded drill bits>NULL\n" +
            "Drilling tools>Sintered drill bits>NULL\n" +
            "Drilling tools>Step drill>NULL\n" +
            "Drilling tools>Bi-metal how saw>NULL\n" +
            "Polishing tools>Dry polishing pad>NULL\n" +
            "Polishing tools>Wet polishing pad>NULL\n" +
            "Polishing tools>3-step pads>NULL\n" +
            "Polishing tools>PVA pads>NULL\n" +
            "Polishing tools>Hand polishing pad>NULL\n" +
            "Laser-welded blade>Gneral purpose  ARR>NULL\n" +
            "Laser-welded blade>General Purpose DOT>NULL\n" +
            "Laser-welded blade>General Purpose,EGL>NULL\n" +
            "Laser-welded blade>General Purpose,PEK>NULL\n" +
            "Laser-welded blade>General Purpose,SEG>NULL\n" +
            "Sintered>T-Segment Grinding Cup Wheel>NULL\n" +
            "Sintered>Arrow Segment Grinding Cup Wheel>NULL\n" +
            "Sintered>Turbo Row Grinding Cup Wheel>NULL\n" +
            "Sintered>Double Row Grinding Cup Wheel>NULL\n" +
            "Sintered>R3 Grinding Cup Wheel>NULL\n" +
            "Floor grinding preparations>10 Inch Floor Grinding Head, 20 Arrow Segments>NULL\n" +
            "Floor grinding preparations>10 Inch Floor Grinding Head, 20 Arrow Segments>NULL\n" +
            "Floor grinding preparations>10 Inch Floor Grinding Head, 20 Bar Segments>NULL\n" +
            "Floor grinding preparations>10 Inch PCD Floor Grinding Head>NULL\n" +
            "Tools accessories>M14-HEX>NULL\n" +
            "Tools accessories>Adaptors>NULL\n" +
            "Sintered blade>Continuous Rim Diamond Blade>NULL\n" +
            "Laser-welded blade>Diamond Saw Blade for Asphalt,ASP-S>NULL\n" +
            "Laser-welded blade>Diamond Saw Blade for Asphalt,ASP-W>NULL\n" +
            "Laser-welded blade>Diamond Saw Blade for Wall Saws>NULL\n" +
            "Sintered blade>Segmented Rim Diamond Blade>NULL\n" +
            "Sintered blade>Segmented Turbo Diamond Blade,ST1>NULL\n" +
            "Sintered blade>Segmented Turbo Diamond Blade,ST2>NULL\n" +
            "Sintered blade>Segmented Turbo Diamond Blade,ST3>NULL\n" +
            "Sintered blade>Segmented Turbo Diamond Blade,ST4>NULL\n" +
            "Sintered blade>Segmented Turbo Diamond Blade,ST5>NULL\n" +
            "Sintered blade>Segmented Turbo Diamond Blade,ST5>NULL\n" +
            "Sintered blade>Thin Turbo Rim Diamond Blade,TSR>NULL\n" +
            "Sintered blade>Thin Turbo Rim Diamond Blade,TXR>NULL\n" +
            "Sintered blade>Thin Turbo Rim Diamond Blade,TRR>NULL\n" +
            "Sintered blade>Turbo Rim Diamond Blade,TRA>NULL\n" +
            "Sintered blade>Turbo Rim Diamond Blade,TRS>NULL\n" +
            "Sintered blade>Turbo Rim Diamond Blade,TUR>NULL\n" +
            "Tuck point blade>Diamond Crack Chaser Blade>NULL\n" +
            "Tuck point blade>Diamond Sandwich Tuck Point Blade>NULL\n" +
            "Tuck point blade>Diamond Sandwich Tuck Point Blade, SAN3>NULL\n" +
            "Tuck point blade>Diamond Tuck Point Blade>NULL\n" +
            "Vacuum Brazed blade>Multi-Purpose Rescue Blades>NULL\n" +
            "Drilling tools>wet core drill bits>NULL\n";

    public static void main(String[] args) throws IOException {
        String[] split = aa.split("\n");
        Set<String> set = Sets.newHashSet();
        for (String s : split) {
            String key = s.replace("Product>", "").replace("  ", "").replace(">NULL>NULL", "").replace(">NULL", "");
            set.add(key);
        }
        Set<String> sortSet = new TreeSet<String>(new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                return o1.compareTo(o2);
            }
        });
        sortSet.addAll(set);
        for (String s : set) {
            sortSet.removeIf(s1 -> s.contains(s1) && !s.equals(s1));
        }
        System.out.println(StringUtils.join(sortSet, "\n"));
    }
}
