package com.theolympiastone;

import org.apache.commons.io.FileUtils;

import java.io.File;
import java.io.IOException;
import java.util.List;

public class ClTest {
    public static void main(String[] args) throws IOException {
        File file = new File("/Users/<USER>/Desktop/cl_202111101451.txt");
        List<String> strings = FileUtils.readLines(file, "GBK");
        for (String string : strings) {
            String[] split = string.split("\\|");
            System.out.println("mv \"" + split[2].trim() + "\" " + split[1].trim());
        }
    }
}
