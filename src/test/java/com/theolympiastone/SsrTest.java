package com.theolympiastone;

import com.google.gson.Gson;
import org.apache.commons.io.FileUtils;

import java.io.File;
import java.io.IOException;
import java.util.List;

public class SsrTest {
    public static void main(String[] args) throws IOException {
        File f = new File("/Users/<USER>/Downloads/ssr.txt");
        List<String> strings = FileUtils.readLines(f, "UTF-8");
        for (String string : strings) {
            SsrObject ssrObject = new SsrObject();
            ssrObject = new Gson().fromJson(string, ssrObject.getClass());
            List<SsrNode> data = ssrObject.getData();
            System.out.println(data.get(0).getSslink());
        }
    }
}
