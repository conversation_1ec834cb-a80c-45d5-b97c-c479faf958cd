package com.theolympiastone;

import com.google.common.collect.Sets;
import org.apache.commons.io.FileUtils;

import java.io.File;
import java.io.IOException;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class YellowpagesAuMask {
    private final static Pattern emailer = Pattern.compile("\\w+(\\.\\w)*@\\w+(\\.\\w{2,3}){1,3}");
    private final static Pattern urlPattern = Pattern.compile("(https://8x1078x.com/html/)(\\d+)(/.*)");

    public static void main(String[] args) throws IOException {
        String url = "https://8x1078x.com/html/2465/";
        Matcher matcher = urlPattern.matcher(url);
        if (matcher.find()) {
            System.out.println(matcher.group(2));
        }
        if (true) {
            return;
        }
        File file = new File("/Users/<USER>/Downloads/yellowpages_com_au_mask.csv");
        String content = FileUtils.readFileToString(file, "UTF-8");
        Matcher matchr = emailer.matcher(content);
        Set<String> set = Sets.newHashSet();
        while (matchr.find()) {
            String email = matchr.group();
            set.add(email);
        }
        for (String s : set) {
            System.out.println(s);
        }
    }
}
