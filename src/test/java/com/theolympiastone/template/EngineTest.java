package com.theolympiastone.template;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.jfinal.template.Engine;
import com.jfinal.template.Template;
import com.theolympiastone.SsrNode;
import com.theolympiastone.SsrObject;
import org.apache.commons.io.FileUtils;

import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Map;

public class EngineTest {

    public static void main(String[] args) throws IOException {
        String ssrJson = "{\n" +
                "  \"random\" : false,\n" +
                "  \"authPass\" : null,\n" +
                "  \"useOnlinePac\" : false,\n" +
                "  \"TTL\" : 0,\n" +
                "  \"global\" : false,\n" +
                "  \"reconnectTimes\" : 3,\n" +
                "  \"index\" : 0,\n" +
                "  \"proxyType\" : 0,\n" +
                "  \"proxyHost\" : null,\n" +
                "  \"authUser\" : null,\n" +
                "  \"proxyAuthPass\" : null,\n" +
                "  \"isDefault\" : false,\n" +
                "  \"pacUrl\" : null,\n" +
                "  \"configs\" : [\n" +
                " #for( ss : ssList) " +
                "    {\n" +
                "      \"enable\" : false,\n" +
                "      \"password\" : \"#(ss.password??)\",\n" +
                "      \"method\" : \"#(ss.method??)\",\n" +
                "      \"remarks\" : \"#(ss.remarks??)\",\n" +
                "      \"server\" : \"#(ss.server??)\",\n" +
                "      \"obfs\" : \"#(ss.obfs??)\",\n" +
                "      \"protocol\" : \"#(ss.protocol??)\",\n" +
                "      \"group\" : \"free_ssr\",\n" +
                "      \"server_port\" : #(ss.server_port??),\n" +
                "      \"remarks_base64\" : \"\"\n" +
                "    },\n" +
                "#end " +
                "  ],\n" +
                "  \"proxyPort\" : 0,\n" +
                "  \"randomAlgorithm\" : 0,\n" +
                "  \"proxyEnable\" : false,\n" +
                "  \"enabled\" : true,\n" +
                "  \"autoban\" : false,\n" +
                "  \"proxyAuthUser\" : null,\n" +
                "  \"shareOverLan\" : false,\n" +
                "  \"localPort\" : 1080\n" +
                "}";
        File f = new File("/Users/<USER>/Downloads/ssr.txt");
        List<String> strings = FileUtils.readLines(f, "UTF-8");
        List<SsrNode> datas = Lists.newArrayList();
        for (String string : strings) {
            SsrObject ssrObject = new SsrObject();
            ssrObject = new Gson().fromJson(string, ssrObject.getClass());
            datas.addAll(ssrObject.getData());
        }
        Map<String, List<SsrNode>> map = Maps.newHashMap();
        map.put("ssList", datas);
        Engine engine = Engine.use();
        Template template = engine.getTemplateByString(ssrJson);
        String ret = template.renderToString(map);
        FileUtils.writeStringToFile(new File("/Users/<USER>/Downloads/ssr_import.txt"), ret, "UTF-8");
        System.out.println(ret);
    }
}
