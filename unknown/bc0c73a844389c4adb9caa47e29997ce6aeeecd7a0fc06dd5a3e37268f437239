package com.theolympiastone.club.download;

import com.jfinal.aop.Before;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.theolympiastone.club.common.controller.BaseController;
import com.theolympiastone.club.common.interceptor.AuthCacheClearInterceptor;
import com.theolympiastone.club.common.interceptor.FrontAuthInterceptor;
import com.theolympiastone.club.common.kit.IpKit;
import com.theolympiastone.club.common.model.Account;

/**
 * 下载控制器
 */
@Before(FrontAuthInterceptor.class)
public class DownloadController extends BaseController {
    @Inject
    DownloadService srv;

    /**
     * 下载
     */
    public void index() {
        keepPara();
        Account loginAccount = getLoginAccount();
        String ip = IpKit.getRealIp(getRequest());
        Ret ret = srv.download(loginAccount, getPara("file"), ip);
        if (ret.isOk()) {
            String fullFileName = ret.getAs("fullFileName");
            renderFile(fullFileName);
        } else {
            renderError(404);
        }
    }

    /**
     * 清缓存
     */
    @Before(AuthCacheClearInterceptor.class)
    public void clear() {
        srv.clearCache();
        redirect("/");
    }
}
